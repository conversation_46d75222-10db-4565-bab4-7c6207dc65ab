/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ["./src/**/*.{js,jsx,ts,tsx}"],
	theme: {
		boxShadow: {
			standard: "0px 1px 7px 0px rgba(0,0,0,0.04)",
		},
		colors: {
			standard: {
				1: "#F6F6F6",
				2: "var(--text-color-border, #e6e9f4)",
				3: "var(--text-color-disable, #d7dbec)",
				4: "#1D2129",
				5: "#1F1F1F",
				border: "rgba(235,237,240,0.46)",
				warning: "rgba(240, 116, 0, 1)",
				success: "rgba(0, 204, 51, 1)",
			},
			primary: "var(--ant-primary-color, #0058ff)",
			error: "var(--ant-error-color, #f0142f)",
			info: "var(--ant-info-color, #57b8ff)",
			warning: "var(--ant-warning-color, #ff9900)",
			success: "var(--ant-success-color, #21d59b)",
			white: "#FFFFFF",
			black: "#000000",
			"primary-color": "var(--primary-color)",
			"secondary-color": "var(--secondary-color)",
		},
		extend: {
			fontSize: {
				'xs': ['24rpx', { lineHeight: '32rpx' }],
				'sm': ['28rpx', { lineHeight: '40rpx' }],
				'base': ['32rpx', { lineHeight: '48rpx' }],
				'lg': ['36rpx', { lineHeight: '56rpx' }],
				'xl': ['40rpx', { lineHeight: '64rpx' }],
				'2xl': ['48rpx', { lineHeight: '72rpx' }],
			},
			screens: {
				'sm': '320px',  // iPhone 5
				'md': '375px',  // iPhone 6/7/8
				'lg': '414px',  // iPhone 6/7/8 Plus
				'xl': '768px',  // iPad
			},
		},
		fontWeight: {
			bold: "500",
		},
	},
	plugins: [],
	// 由于小程序环境的特殊性，建议关闭预检
	corePlugins: {
		preflight: false,
	},
};
