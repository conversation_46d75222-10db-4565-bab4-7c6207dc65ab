# http://editorconfig.org

root = true

[*] # 表示所有文件适用
charset = utf-8 # 设置文件字符集为 utf-8
indent_style = tab # 缩进风格（tab | space）
indent_size = 4 # 缩进大小
end_of_line = lf # 控制换行类型(lf | cr | crlf)
trim_trailing_whitespace = true # 去除行首的任意空白字符
insert_final_newline = true # 始终在文件末尾插入一个新行

[*.{js,ts,jsx,tsx}]
ij_javascript_spaces_within_imports = true
ij_javascript_spaces_within_object_literal_braces = true
ij_javascript_object_literal_wrap = off
ij_typescript_spaces_within_imports = true
ij_typescript_spaces_within_object_literal_braces = true
ij_typescript_spaces_within_object_type_braces = true
ij_typescript_object_literal_wrap = off

[*.json]
indent_size = 4

