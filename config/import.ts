const options = {
	include: [
		/\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
	],
	imports: [
		// "react",
		{
			// lodash: [["default", "_"]],
			// qs: [["default", "qs"]],
			// umi: [
			// 	// valtio
			// 	"proxy",
			// 	"subscribe",
			// 	"snapshot",
			// 	"proxyWithComputed",
			// 	"proxyWithHistory",
			// 	"proxyWithPersistant",
			// 	"useSnapshot",
			// 	// umi
			// 	"useClientLoaderData",
			// 	// dva
			// 	"connect",
			// 	"useSelector",
			// ],
			classnames: [["default", "cls"]],
		},
	],
	defaultExportByFilename: false,
	dirsScanOptions: {
		types: true, // Enable auto import the types under the directories
	},
	dirs: [
		"./src/helper",
		// "./src/**/hooks",
		"./src/constant/env",
		"./src/service",
		// './composables' // only root modules
		// './composables/**', // all nested modules
		// ...
	],
	dts: "./types/imports.d.ts",
	resolvers: [
		/* ... */
	],
	injectAtEnd: true,

	// Generate corresponding .eslintrc-auto-import.json file.
	// eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
	// eslintrc: {
	//   enabled: true, // Default `false`
	//   filepath: "./.imports-lint.json", // Default `./.eslintrc-auto-import.json`
	//   globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
	// },
};

export default options;
