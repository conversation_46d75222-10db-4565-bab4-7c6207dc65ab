import { defineConfig, type UserConfigExport } from "@tarojs/cli";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import { UnifiedWebpackPluginV5 } from "weapp-tailwindcss/webpack";
import devConfig from "./dev";
import prodConfig from "./prod";
import { BASE_API } from "./env";
import importOptions from "./import";
// import ImportPlugin from "./ImportPlugin";

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
// @ts-ignore
export default defineConfig(async (merge, { command, mode }) => {
	const baseConfig: UserConfigExport = {
		projectName: "member-cloud-tob",
		date: "2025-03-01",
		designWidth(input: any) {
			if (
				input?.file?.replace(/\+/g, "/")?.indexOf("taro-ui/dist") > -1
			) {
				return 750;
			}
			return 375;
		},
		deviceRatio: {
			640: 2.34 / 2,
			750: 1,
			375: 2,
			828: 1.81 / 2,
		},
		sourceRoot: "src",
		outputRoot: "dist",
		plugins: [
			"@taro-hooks/plugin-react",
			"@tarojs/plugin-http",
			["@taro-hooks/plugin-auto-import", importOptions],
		],
		defineConstants: {
			BASE_API: JSON.stringify(BASE_API),
		},
		copy: {
			patterns: [],
			options: {},
		},
		framework: "react",
		compiler: {
			type: "webpack5",
			prebundle: { enable: false, force: true },
		},
		cache: {
			enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
		},
		mini: {
			optimizeMainPackage: {
				enable: true,
			},
			miniCssExtractPluginOption: {
				ignoreOrder: true,
			},
			postcss: {
				pxtransform: {
					enable: true,
					config: {
						onePxTransform: true,
						unitPrecision: 5,
						propList: ["*"],
						selectorBlackList: [],
						replace: true,
						mediaQuery: false,
						minPixelValue: 0,
					},
				},
				url: {
					enable: true,
					config: {
						limit: 1024, // 设定转换尺寸上限
					},
				},
				cssModules: {
					enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
					config: {
						namingPattern: "module", // 转换模式，取值为 global/module
						generateScopedName: "[name]__[local]___[hash:base64:5]",
					},
				},
			},
			webpackChain(chain) {
				chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
				chain.merge({
					plugin: {
						install: {
							plugin: UnifiedWebpackPluginV5,
							args: [
								{
									appType: "taro",
									// 下面个配置，会开启 rem -> rpx 的转化
									rem2rpx: true,
								},
							],
						},
					},
				});
			},
		},
		h5: {
			publicPath: "/",
			staticDirectory: "static",
			output: {
				filename: "js/[name].[hash:8].js",
				chunkFilename: "js/[name].[chunkhash:8].js",
			},
			miniCssExtractPluginOption: {
				ignoreOrder: true,
				filename: "css/[name].[hash].css",
				chunkFilename: "css/[name].[chunkhash].css",
			},
			esnextModules: ["taro-ui"],
			postcss: {
				"postcss-px-scale": {
					enable: true,
					config: {
						scale: 0.5, // 缩放为 1/2
						units: "px",
						includes: ["taro-ui"],
					},
				},
				pxtransform: {
					enable: false,
					config: {
						onePxTransform: true,
						unitPrecision: 5,
						propList: ["*"],
						selectorBlackList: [],
						replace: true,
						mediaQuery: false,
						minPixelValue: 0,
						baseFontSize: 20,
						maxRootSize: 40,
						minRootSize: 20,
					},
				},
				autoprefixer: {
					enable: true,
					config: {},
				},
				cssModules: {
					enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
					config: {
						namingPattern: "module", // 转换模式，取值为 global/module
						generateScopedName: "[name]__[local]___[hash:base64:5]",
					},
				},
			},
			webpackChain(chain) {
				chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
			},
		},
		rn: {
			appName: "taroDemo",
			postcss: {
				cssModules: {
					enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
				},
			},
		},
	};
	if (process.env.NODE_ENV === "development") {
		// 本地开发构建配置（不混淆压缩）
		return merge({}, baseConfig, devConfig);
	}
	// 生产构建配置（默认开启压缩混淆等）
	return merge({}, baseConfig, prodConfig);
});
