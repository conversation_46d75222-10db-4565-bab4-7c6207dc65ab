/* eslint-disable */
const { generateService } = require("realmore-openapi");
const fs = require("fs");
const path = require("path");
const { default: axios } = require("axios");

const getSchemaPath = async (config, refresh) => {
	const pathDir = "./mock";

	if (!fs.existsSync(pathDir)) {
		fs.mkdirSync(pathDir);
	}
	const filepath = path.resolve(
		__dirname,
		`${pathDir}/${config.namespace}.json`
	);
	if (!refresh) {
		try {
			await fs.promises.access(filepath);
			return filepath;
		} catch (error) {
			console.log("----", error);
		}
	}

	return axios({
		url: config.schemaPath,
		method: "GET",
		timeout: 5 * 60 * 1000,
	})
		.then((result) =>
			fs.promises.writeFile(
				filepath,
				JSON.stringify(result.data, null, 2)
			)
		)
		.then(() => filepath);
};

const generator = async (config, apiList, refresh) => {
	const filepath = await getSchemaPath(config, refresh);
	generateService({
		requestLibPath: "import request from '@/helper/request';",
		serversPath: "./src/service",
		// 在这里添加需要生成的api列表
		generateApis: apiList,
		generateTraceId: true,
		...config,
		schemaPath: filepath,
	});
};

module.exports = generator;
