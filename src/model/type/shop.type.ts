export type ShopType = {
    businessId: number,
    businessName: string,
    businessManagerId: number,
    businessDesc?: string,
    businessStatus: string,
    businessStartHours: string,
    businessEndHours: string,
    businessCategory: string,
    contactPhone: string,
    contactUser: string,
    logo: string,
    region: string,
    address: string,
    delFlag: boolean,
    deleteBy?: string,
    deleteTime?: string
    createBy: string,
    createTime: string,
    updateBy?: string,
    updateTime?: string,
    params: Record<string, any>
}