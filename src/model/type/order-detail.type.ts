export type OrderProductItemType = {

	id: number,
	orderId: number,
	valid: number,
	serialId: string,
	createTime: string,
	createUser: string,
	modifyTime: string,
	modifyUser: string,
	orderSerialId: string,
	productId: number,
	productName: string,
	productPrice: number,
	productMemberPrice: number,
	productCategoryId: number,
	productCategoryName: string,
	productBrief: string,
	productCoverImgUrl: string,

}

export type OrderDetailType = {

	id: number,
	status: number,
	cancelStatus: number,
	userId: number,
	valid: number,
	payStatus: number,
	needPayPrice: number,
	needPetCleaning: number,
	petCleaningPrice: number,
	totalPrice: number,
	actualPayPrice: number,
	couponId: number,
	couponPrice: number,
	addressProvince: string,
	addressCity: string,
	addressDistrict: string,
	addressDetail: string,
	addressLongitude: number,
	addressLatitude: number,
	addressRoom: string,
	contactName: string,
	contactPhone: string,
	createTime: string,
	createUser: string,
	detailList: Array<OrderProductItemType>,
	expectArriveTimeEnd: string,
	expectArriveTimeStart: string,
	housekeeper: {
		id: number,
		valid: number,
		status: number,
		avatar: string,
		birthday: string,
		createTime: string,
		createUser: string,
		email: string,
		gender: number,
		housekeeper: number,
		housekeeperAvatar: string,
		housekeeperLevelId: number,
		housekeeperName: string,
		housekeeperNo: string,
		loginName: string,
		logoffTime: string,
		membership: number,
		membershipActiveDate: string,
		membershipExpireDate: string,
		membershipId: number,
		membershipName: string,
		mobile: string,
		modifyTime: string,
		modifyUser: string,
		nickname: string,
		password: string,
		registerChannel: string,
		registerTime: string,
		totalPoints: number,
		wechatOpenId: string,
	}
	housekeeperId: number,
	housekeeperLevelId: number,
	housekeeperLevelName: string,
	housekeeperLevelPrice: number,
	housekeeperName: string,
	housekeeperNo: string,
	imageList: Array<{
		id: number,
		orderId: number,
		orderSerialId: string,
		type: 'BEFORE' | 'AFTER',
		imgUrl: string,
	}>,
	modifyTime: string,
	modifyUser: string,
	remark: string,
	serialId: string,
	serviceTimeEnd: string,
	serviceTimeStart: string,

}
