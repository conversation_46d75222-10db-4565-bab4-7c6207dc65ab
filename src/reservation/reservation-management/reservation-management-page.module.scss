.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.roomSelector, .dateSelector {
  display: flex;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.roomName, .date {
  font-size: 16px;
  font-weight: 500;
}

.arrow {
  margin-left: 8px;
  font-size: 16px;
  color: #999;
}

.tabsContainer {
  display: flex;
  padding: 8px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.tabItem {
  padding: 6px 0px;
  margin-right: 6px;
  // background-color: #f0f0f0;
  border-radius: 16px;
}

.tabText {
  font-size: 14px;
  color: #333;
}

.activeTab {
  color: #fff;
  background-color: #FFCD33;
  padding: 6px 6px;
  border-radius: 16px;
}

.occupiedBtn, .addBtn {
  margin-left: auto;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  padding: 0 12px;
  border-radius: 16px;
}

.occupiedBtn {
  background-color: #f0f0f0;
  color: #333;
  margin-right: 8px;
}

.addBtn {
  background-color: #FFCD33;
  color: #fff;
}

.statsContainer {
  padding: 12px 16px;
  background-color: #fff;
  margin-bottom: 1px;
}

.statsText {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.statsDetails {
  display: flex;
}

.statItem {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.statDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.reservedDot {
  background-color: #4CD964;
}

.arrivedDot {
  background-color: #FF9500;
}

.cancelledDot {
  background-color: #8E8E93;
}

.scheduleContainer {
  display: flex;
  flex: 1;
  background-color: #fff;
  overflow: hidden;
}

.timeColumn {
  width: 60px;
  flex-shrink: 0;
  border-right: 1px solid #eee;
}

.timeSlot {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #eee;
}

.staffColumns {
  flex: 1;
  overflow-x: auto;
}

.staffColumnsInner {
  display: flex;
  min-width: 100%;
}

.staffColumn {
  flex: 1;
  min-width: 100px;
  border-right: 1px solid #eee;
}

.staffHeader {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #eee;
  font-weight: 500;
}

.slot {
  height: 60px;
  border-bottom: 1px solid #eee;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.emptySlot {
  background-color: #fff;
}

.reservedSlot {
  background-color: #E8F5E9;
}

.arrivedSlot {
  background-color: #FFF3E0;
}

.occupiedSlot {
  background-color: #ECEFF1;
}

.projectName {
  font-size: 14px;
  color: #333;
}

.indicator {
  font-size: 12px;
  color: #FF9500;
  align-self: flex-end;
  margin-top: 4px;
}


.reservationList {
  height: calc(100vh - 168px);
  padding: 0 12px;
  width: calc(100vw - 24px);
  padding-top: 12px;
}