import React, { useCallback, useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { Text, View, Image, ScrollView } from "@tarojs/components";
import { AtTabs, AtTabsPane } from "taro-ui";
import PageWithNav, { PageContainerRef } from "@/view/component/page-with-nav/page-with-nav.component";
import { postReservationRecordList } from '@/service/business/reservationRecordController';
import ReservationItem from "@/view/page/tab/home/<USER>";
import Plus from '@/assets/image/plus.svg'
import styles from "./reservation-management-page.module.scss";


// 使用后端返回的数据类型
type ReservationItem = BUSINESS.ReservationRecordListVo;

const Index: React.FC = () => {
	const container: React.RefObject<PageContainerRef> = useRef(null);
	const [current, setCurrent] = useState(0);
	const [loading, setLoading] = useState(false);
	const [reservationList, setReservationList] = useState<ReservationItem[]>([]);

	const tabList = [
		{ title: '全部' },
		{ title: '已接单' },
		{ title: '未接单' }
	];

	const handleTabClick = (value: number) => {
		setCurrent(value);
		refreshReservationList(value);
	};

	const refreshReservationList = useCallback(async (tabIndex: number) => {
		setLoading(true);
		let statuses;
		if (tabIndex === 1) statuses = [1]; // 已预约
		else if (tabIndex === 2) statuses = [0]; // 未预约（待接单）
		// 0:待接单，1:已接单，2:已取消，3:已到店，4:未到店，5:已完成，6:已逾期

		try {
			const res = await postReservationRecordList({ statuses });
			setReservationList(res ?? []);
		} catch (e) {
			console.error('获取预约列表失败:', e);
			setReservationList([]);
			Taro.showToast({
				title: '获取预约列表失败',
				icon: 'error',
				duration: 2000
			});
		} finally {
			setLoading(false);
		}
	}, []);

	// 添加预约
	const handleAddReservation = () => {
		Taro.navigateTo({
			url: "/reservation/appointment/appointment-page",
		});
	};

	useEffect(() => {
		refreshReservationList(current);
	}, [refreshReservationList, current]);

	return (
		<PageWithNav
			showNavBar
			title="预约管理"
			containerRef={container}
			containerClassName={styles.container}
		>
			<AtTabs current={current} tabList={tabList} onClick={handleTabClick}>
				<AtTabsPane current={current} index={0}>
					<ScrollView className={styles.reservationList} scrollY >
						{loading ? (
							<View className="flex justify-center items-center h-full">加载中...</View>
						) : reservationList.length > 0 ? (
							reservationList.map(item => (
								<ReservationItem key={item.id} item={item} onRefresh={() => refreshReservationList(current)} />
							))
						) : (
							<View className="flex justify-center items-center h-full text-gray-500">
								暂无预约数据
							</View>
						)}
					</ScrollView>
				</AtTabsPane>
				<AtTabsPane current={current} index={1}>
					<ScrollView className={styles.reservationList} scrollY >
						{loading ? (
							<View className="flex justify-center items-center h-full">加载中...</View>
						) : reservationList.length > 0 ? (
							reservationList.map(item => (
								<ReservationItem key={item.id} item={item} onRefresh={() => refreshReservationList(current)} />
							))
						) : (
							<View className="flex justify-center items-center h-full text-gray-500">
								暂无预约数据
							</View>
						)}
					</ScrollView>
				</AtTabsPane>
				<AtTabsPane current={current} index={2}>
					<ScrollView className={styles.reservationList} scrollY>
						{loading ? (
							<View className="flex justify-center items-center h-full">加载中...</View>
						) : reservationList.length > 0 ? (
							reservationList.map(item => (
								<ReservationItem key={item.id} item={item} onRefresh={() => refreshReservationList(current)} />
							))
						) : (
							<View className="flex justify-center items-center h-full text-gray-500">
								暂无预约数据
							</View>
						)}
					</ScrollView>
				</AtTabsPane>
			</AtTabs>
			{/* 悬浮添加按钮 */}
			<View 
				className="fixed right-4 bottom-20 w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center"
				onClick={handleAddReservation}
				style={{
					boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
				}}
			>
				<Image src={Plus} className="w-6 h-6" />
			</View>
		</PageWithNav>
	);
};

export default Index;
