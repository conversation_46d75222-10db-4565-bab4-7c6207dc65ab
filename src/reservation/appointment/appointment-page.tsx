import {
  View,
  Text,
  Input,
  Picker,
  RadioGroup,
  Radio,
} from "@tarojs/components";
import dayjs from "dayjs";
import Form, { useForm } from "rc-field-form";
import { useEffect, useRef, useState } from "react";
import ServiceSelector, { ServiceItem } from "@/view/component/service-selector";
import EmployeeSelector, { EmployeeItem } from "@/view/component/employee-selector";
import { postReservationRecord, postReservationRecordGetMemberType } from '@/service/business/reservationRecordController'
import { getEmployeeList } from '@/service/business/businessEmployeeController'
import { postReservationTemplateProjectList } from '@/service/business/reservationTemplateController'
import Taro from "@tarojs/taro";
import CartEmptyImage from "@/assets/image/success.png";
import SuccessModal from "@/view/component/success-modal";
import PageWithNav, { PageContainerRef } from "@/view/component/page-with-nav/page-with-nav.component";

// 性别枚举
enum Gender {
  Male = 0,
  Female = 1,
  Other = 2
}

// 进店渠道枚举
const SOURCE_CHANNEL = [
  { value: 0, label: '小程序' },
  { value: 1, label: '公众号' },
  { value: 2, label: 'H5' },
  { value: 3, label: 'PC' },
  { value: 4, label: '分享链接' },
  { value: 5, label: '其他' }
];

// 生成日期选项（保持不变）
const getDateOptions = () => {
  return Array.from({ length: 30 }, (_, i) => {
    const date = dayjs().add(i, 'day');
    const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()];
    return i === 0 ? `今天 ${weekDay}` : `${date.format('MM月DD日')} ${weekDay}`;
  });
};

// 动态生成时间选项配置
const getDynamicTimeRange = (selectedDateIndex: number, selectedHourIndex: number = 0) => {
  return [
    getDateOptions(),
    getValidHours(selectedDateIndex),
    getValidMinutes(selectedDateIndex, selectedHourIndex)
  ];
};

// 动态生成有效的小时选项
const getValidHours = (dateIndex: number) => {
  const selectedDate = dayjs().add(dateIndex, 'day');
  const currentDate = dayjs();

  // 如果选择的是今天，则只显示当前小时及以后的选项
  if (selectedDate.isSame(currentDate, 'day')) {
    const currentHour = currentDate.hour();
    return Array.from({ length: 24 - currentHour }, (_, i) => {
      const hour = currentHour + i;
      return hour < 10 ? `0${hour}:00` : `${hour}:00`;
    });
  }

  // 未来日期显示所有24小时选项
  return Array.from({ length: 24 }, (_, i) =>
    i < 10 ? `0${i}:00` : `${i}:00`
  );
};

// 动态生成有效的分钟选项
const getValidMinutes = (dateIndex: number, hourIndex: number) => {
  const selectedDate = dayjs().add(dateIndex, 'day');
  const currentDate = dayjs();

  // 如果选择的是今天
  if (selectedDate.isSame(currentDate, 'day')) {
    const validHours = getValidHours(dateIndex);
    const selectedHour = parseInt(validHours[hourIndex]?.split(':')[0] || '0');
    const currentHour = currentDate.hour();
    const currentMinute = currentDate.minute();

    // 如果选择的小时是当前小时，则只显示当前分钟之后的选项
    if (selectedHour === currentHour) {
      const currentMinuteSlot = Math.floor(currentMinute / 10) + 1; // 下一个10分钟时间段
      const availableSlots = 6 - currentMinuteSlot; // 剩余可用时间段

      if (availableSlots <= 0) {
        return []; // 当前小时没有可用的分钟选项
      }

      return Array.from({ length: availableSlots }, (_, i) => {
        const minuteSlot = currentMinuteSlot + i;
        return minuteSlot === 0 ? '00' : `${minuteSlot}0`;
      });
    }
  }

  // 其他情况显示所有分钟选项
  return Array.from({ length: 6 }, (_, i) =>
    i === 0 ? '00' : `${i}0`
  );
};

const AppointmentPage = () => {
  const [form] = useForm();
  const [isServiceSelectorVisible, setIsServiceSelectorVisible] = useState(false);
  const [isEmployeeSelectorVisible, setIsEmployeeSelectorVisible] = useState(false);
  const [employeeList, setEmployeeList] = useState<EmployeeItem[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<BUSINESS.RecordEmployeeParam[]>([]);
  const [isAllocation, setIsAllocation] = useState(false); // 添加是否到店分配的状态
  const [memberType, setMemberType] = useState<0 | 1>(0); // 0: 散客, 1: 会员
  const [selectedItems, setSelectedItems] = useState<BUSINESS.RecordProjectParam[]>([]);
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>([]);
  const [successVisible, setSuccessVisible] = useState(false);
  const container: React.RefObject<PageContainerRef> = useRef(null);
  const [selectedDateIndex, setSelectedDateIndex] = useState(0); // 跟踪当前选择的日期索引


  const handleRemoveItem = (index: number) => {
    setSelectedItems(items => {
      const newItems = items.filter((_, i) => i !== index);
      // 重新设置序号
      return newItems.map((item, i) => ({
        ...item,
        seq: i
      }));
    });
  };

  const handleServiceSelect = (service: ServiceItem) => {
    if (selectedItems.some(item => item.templateProjectId === Number(service.id))) {
      setSelectedItems(items => items.filter(item => item.templateProjectId !== Number(service.id)));
    } else {
      const newItem: BUSINESS.RecordProjectParam = {
        templateProjectId: Number(service.id),
        isAllocation: true,
        projectOrigPrice: service.projectOrigPrice || 0,
        projectDiscPrice: service.projectOrigPrice || 0,
        projectDuration: service.projectDuration,
        buyCount: 1,
        seq: selectedItems.length // 添加序号，新项目的序号为当前列表长度
      };
      setSelectedItems(items => [...items, newItem]);
    }
  };

  const handleEmployeeSelect = (employees: EmployeeItem[], isAllocationSelected: boolean) => {
    if (isAllocationSelected) {
      setIsAllocation(true);
      setSelectedEmployees([]);
      form.setFieldValue('staff', '');
    } else {
      setIsAllocation(false);
      const mappedEmployees = employees.map((employee, index) => ({
        seq: index,
        templateEmployeeId: employee.employeeId
      }));
      setSelectedEmployees(mappedEmployees);
      form.setFieldValue('staff', employees[0]?.employeeNickname || '');
    }
  };

  const handleValueChange = useMemoizedFn((value: any, allValues: any) => {
    // console.log(value, allValues, '3333')
  })

  const handleSubmit = () => {
    form.validateFields().then(values => {
      // console.log(values, '---vvvvvv', form.getFieldsValue())
      // 校验必填项
      if (!values.nickName) {
        Taro.showToast({
          title: '请输入客人姓名',
          icon: 'error',
          duration: 2000
        });
        return;
      }
      if (!values.phonenumber) {
        Taro.showToast({
          title: '请输入手机号',
          icon: 'error',
          duration: 2000
        });
        return;
      }
      if (!values.reservationDate) {
        Taro.showToast({
          title: '请选择预约时间',
          icon: 'error',
          duration: 2000
        });
        return;
      }
      if (selectedItems.length === 0) {
        Taro.showToast({
          title: '请选择预约项目',
          icon: 'error',
          duration: 2000
        });
        return;
      }

      const formData: BUSINESS.RecordAddParam = {
        // 顾客类型（0散客，1会员）
        memberType,
        // 来源渠道：0小程序，1公众号，2H5，3PC，4分享链接，5其他
        sourceChannel: values.sourceChannel,
        // 预约日期
        reservationDate: values.reservationDate,
        // 客户名称（联系人）名称
        nickName: values.nickName,
        // 用户性别;0=男,1=女,2=其他
        sex: values.sex,
        // 联系方式手机
        phonenumber: values.phonenumber,
        // 是否到店分配
        isAllocation,
        // 手艺人列表（可多选或不选）
        employees: selectedEmployees,
        // 项目列表
        projects: selectedItems,
      };

      // 调用保存接口
      postReservationRecord(formData).then(res => {
        // console.log(res, 'res')
        if (res) {
          // 显示自定义成功弹窗
          setSuccessVisible(true);
          // 清空表单
          form.resetFields();
          setSelectedEmployees([]);
          setSelectedItems([]);
          setMemberType(0);
        } else {
          // console.log(res, '---res')
          Taro.showToast({
            title: '预约失败',
            icon: 'error',
            duration: 2000
          });
        }
      }).catch(error => {
        // console.log(error, '----')
        Taro.showToast({
          title: error?.data?.msg || '预约失败',
          icon: 'error',
          duration: 2000
        });
      });
    }).catch(error => {
      console.error('表单验证失败:', error);
      Taro.showToast({
        title: '请填写完整信息',
        icon: 'error',
        duration: 2000
      });
    });
  };

  // 获取顾客类型
  const getMemberType = async (phone: string) => {
    try {
      const res = await postReservationRecordGetMemberType({
        phone: phone
      });
      // 直接使用返回的枚举值
      setMemberType(res === 'MEMBER' ? 1 : 0);
      form.setFieldValue('memberType', res === 'MEMBER' ? 1 : 0);
    } catch (error) {
      console.error('获取顾客类型失败:', error);
    }
  };

  // 监听手机号变化，获取顾客类型
  const handlePhoneChange = (value: string) => {
    if (value && value.length === 11) {
      getMemberType(value);
    }
  };

  useEffect(() => {
    // 获取手艺人列表
    getEmployeeList({} as any).then(res => {
      if (res?.data) {
        setEmployeeList(res.data.map(item => ({
          employeeId: item.employeeId,
          employeeNickname: item.employeeNickname,
          avatarUrl: item.avatarUrl,
          position: item.position
        })));
      }
    });

    // 获取项目列表
    postReservationTemplateProjectList({} as any).then((data: BUSINESS.TemplateProjectListVo[] | null | undefined) => {
      if (data) {
        // 映射 API 数据到 ServiceItem
        const mappedItems = data.map((item: BUSINESS.TemplateProjectListVo) => ({
          id: item.templateProjectId?.toString() || '',
          projectName: item.projectName || '',
          projectDuration: item.projectDuration,
          projectOrigPrice: item.projectOrigPrice,
          projectDiscPrice: item.projectDiscPrice,
          image: item.image,
        }));
        setServiceItems(mappedItems);
      }
    });
  }, []);

  return (
    <PageWithNav
      showNavBar
      onBack={() => Taro.navigateBack()}
      title="添加预约"
      containerRef={container}
      className='relative h-full bg-white overflow-hidden'
    >
      <View className="h-full bg-standard-1 overflow-y-auto">
        <Form form={form} onValuesChange={handleValueChange}>
          {/* 会员/散客选择 */}
          <View className="bg-white w-full h-[50px] flex items-center px-3">
            <View
              className={`w-[62px] h-[28px] flex justify-center items-center rounded-l-full ${memberType === 1 ? 'bg-gradient-to-r from-[#472A11] to-[#AC9C82]' : 'bg-[#F5F5F5]'}`}
              onClick={() => {
                setMemberType(1);
                form.setFieldValue('memberType', 1);
              }}
            >
              <Text className={memberType === 1 ? 'text-white text-sm' : 'text-standard-4 text-sm'}>会员</Text>
            </View>
            <View
              className={`w-[62px] h-[28px] flex justify-center items-center rounded-r-full ${memberType === 0 ? 'bg-gradient-to-r from-[#FFB800] to-[#FF9500]' : 'bg-[#F5F5F5]'}`}
              onClick={() => {
                setMemberType(0);
                form.setFieldValue('memberType', 0);
              }}
            >
              <Text className={memberType === 0 ? 'text-white text-sm' : 'text-standard-4 text-sm'}>散客</Text>
            </View>
          </View>

          <View className="flex flex-col gap-2 p-[10px]">
            {/* 客人信息 */}
            <View className="bg-white shadow-standard rounded-lg px-4">
              <View className="pt-3 pb-2">
                <Text className="text-standard-5 font-bold text-base sm:text-sm">客人信息</Text>
              </View>
              <View className="[&_>*]:h-[50px]">
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">姓名<Text className=" text-error">*</Text></Text>
                  <Form.Field name="nickName">
                    {(control) => (
                      <Input
                        className="flex-1 text-base sm:text-sm"
                        placeholder="请输入客人姓名"
                        placeholderStyle="font-size: 28rpx; color: #999999;"
                        onInput={(e) => {
                          control.onChange(e.detail.value);
                        }}
                      />
                    )}
                  </Form.Field>
                </View>
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">手机号<Text className="text-error">*</Text></Text>
                  <Form.Field name="phonenumber">
                    {(control) => (
                      <Input
                        placeholder="请输入客人手机号"
                        className="flex-1 text-base sm:text-sm"
                        type="number"
                        value={control.value}
                        placeholderStyle="font-size: 28rpx; color: #999999;"
                        onInput={(e) => {
                          control.onChange(e.detail.value);
                          handlePhoneChange(e.detail.value);
                        }}
                      />
                    )}
                  </Form.Field>
                </View>
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">进店渠道</Text>
                  <Form.Field name="sourceChannel">
                    {(control) => (
                      <Picker
                        mode="selector"
                        className="flex-1 w-full sm:text-sm"
                        range={SOURCE_CHANNEL.map(item => item.label)}
                        value={control.value || 0}
                        onChange={(e) => {
                          const selectedIndex = e.detail.value;
                          control.onChange(SOURCE_CHANNEL[selectedIndex].value);
                        }}
                      >
                        <View className="flex-1 text-standard-5 w-full py-2 sm:text-sm">
                          {control.value !== undefined
                            ? SOURCE_CHANNEL.find(item => item.value === control.value)?.label || '请选择'
                            : '请选择'}
                        </View>
                      </Picker>
                    )}
                  </Form.Field>
                </View>
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">性别</Text>
                  <Form.Field name="sex">
                    {(control) => (
                      <RadioGroup
                        className="flex-1 flex items-center gap-6 text-sm"
                        onChange={(e) => {
                          control.onChange(Number(e.detail.value));
                        }}
                      >
                        <View className="flex items-center gap-4">
                          <Radio
                            value={Gender.Male.toString()}
                            checked={control.value === Gender.Male}
                            className={`${control.value === Gender.Male ? 'text-[#FFB300]' : 'text-[#999]'}`}
                            color="#FFB300"
                          >
                            <Text className={control.value === Gender.Male ? 'text-[#FFB300] text-sm' : 'text-[#333] text-sm'}>男</Text>
                          </Radio>
                          <Radio
                            value={Gender.Female.toString()}
                            checked={control.value === Gender.Female}
                            className={`${control.value === Gender.Female ? 'text-[#FFB300]' : 'text-[#999]'}`}
                            color="#FFB300"
                          >
                            <Text className={control.value === Gender.Female ? 'text-[#FFB300] text-sm' : 'text-[#333] text-sm'}>女</Text>
                          </Radio>
                          <Radio
                            value={Gender.Other.toString()}
                            checked={control.value === Gender.Other}
                            className={`${control.value === Gender.Other ? 'text-[#FFB300]' : 'text-[#999]'}`}
                            color="#FFB300"
                          >
                            <Text className={control.value === Gender.Other ? 'text-[#FFB300] text-sm' : 'text-[#333] text-sm'}>未知</Text>
                          </Radio>
                        </View>
                      </RadioGroup>
                    )}
                  </Form.Field>
                </View>
              </View>
            </View>

            {/* 预约信息 */}
            <View className="bg-white shadow-standard rounded-lg px-4">
              <View className="pt-3 pb-2">
                <Text className="text-standard-5 font-bold text-base sm:text-sm">预约信息</Text>
              </View>
              <View className="[&_>*]:h-[50px]">
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">预约时间<Text className="text-error">*</Text></Text>
                  <Form.Field name="reservationDate">
                    {(control) => {
                      // 设置默认时间：如果没有值，默认选择今天的下一个可用时间
                      let defaultTime = dayjs();
                      const validHours = getValidHours(0); // 今天的有效小时
                      if (validHours.length > 0) {
                        const firstValidHour = parseInt(validHours[0].split(':')[0]);
                        const validMinutes = getValidMinutes(0, 0); // 第一个有效小时的有效分钟
                        if (validMinutes.length > 0) {
                          const firstValidMinute = parseInt(validMinutes[0]);
                          defaultTime = dayjs().hour(firstValidHour).minute(firstValidMinute);
                        } else {
                          // 如果第一个小时没有有效分钟，选择下一个小时
                          if (validHours.length > 1) {
                            const nextValidHour = parseInt(validHours[1].split(':')[0]);
                            defaultTime = dayjs().hour(nextValidHour).minute(0);
                          } else {
                            // 如果今天没有可用时间，选择明天9点
                            defaultTime = dayjs().add(1, 'day').hour(9).minute(0);
                          }
                        }
                      } else {
                        // 如果今天没有可用小时，选择明天9点
                        defaultTime = dayjs().add(1, 'day').hour(9).minute(0);
                      }

                      const currentValue = control.value ? dayjs(control.value) : defaultTime;
                      let currentDateIndex = selectedDateIndex;
                      let currentHourIndex = 0;
                      let currentMinuteIndex = 0;

                      // 如果已有值，计算对应的索引
                      if (control.value) {
                        currentDateIndex = Math.max(0, currentValue.diff(dayjs().startOf('day'), 'day'));
                        const validHours = getValidHours(currentDateIndex);
                        const hourStr = currentValue.format('HH:mm').substring(0, 5);
                        currentHourIndex = Math.max(0, validHours.findIndex(h => h === hourStr));

                        const validMinutes = getValidMinutes(currentDateIndex, currentHourIndex);
                        const minuteStr = currentValue.format('mm');
                        currentMinuteIndex = Math.max(0, validMinutes.findIndex(m => m === minuteStr));
                      } else {
                        // 没有值时，计算默认时间的索引
                        currentDateIndex = Math.max(0, defaultTime.diff(dayjs().startOf('day'), 'day'));
                        const validHours = getValidHours(currentDateIndex);
                        const hourStr = defaultTime.format('HH') + ':00';
                        currentHourIndex = Math.max(0, validHours.findIndex(h => h === hourStr));

                        const validMinutes = getValidMinutes(currentDateIndex, currentHourIndex);
                        const minuteStr = defaultTime.format('mm');
                        currentMinuteIndex = Math.max(0, validMinutes.findIndex(m => m === minuteStr));
                      }

                      return (
                        <Picker
                          mode="multiSelector"
                          range={getDynamicTimeRange(currentDateIndex, currentHourIndex)}
                          value={[
                            currentDateIndex,
                            currentHourIndex,
                            currentMinuteIndex
                          ]}
                          className="flex-1 text-base sm:text-sm"
                          onColumnChange={(e) => {
                            const { column, value } = e.detail;
                            if (column === 0) {
                              // 日期列发生变化，更新selectedDateIndex状态
                              setSelectedDateIndex(value);
                            }
                          }}
                          onChange={(e) => {
                            const [dateIndex, hourIndex, minuteIndex] = e.detail.value;
                            setSelectedDateIndex(dateIndex);
                            const selectedDate = dayjs().add(dateIndex, 'day');
                            const validHours = getValidHours(dateIndex);
                            const validMinutes = getValidMinutes(dateIndex, hourIndex);

                            // 从动态生成的选项中获取实际的小时和分钟值
                            const actualHour = parseInt(validHours[hourIndex]?.split(':')[0] || '0');
                            const actualMinute = parseInt(validMinutes[minuteIndex] || '0');

                            const selectedDateTime = selectedDate
                              .hour(actualHour)
                              .minute(actualMinute);

                            const dateTimeStr = selectedDateTime.format('YYYY-MM-DDTHH:mm:ss');
                            control.onChange(dateTimeStr);
                          }}
                        >
                          <View className="flex-1 text-standard-5 sm:text-xs">
                            {control.value ? dayjs(control.value).format('MM月DD日 HH:mm') : '请选择预约时间'}
                          </View>
                        </Picker>
                      );
                    }}
                  </Form.Field>
                </View>
                {/* <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
								<Text className="text-standard-4 w-[70px] text-sm sm:text-xs">到店分配</Text>
								<Form.Field name="isAllocation">
									{(control) => (
										<View className="flex-1 flex justify-start">
											<Switch 
												checked={control.value}
												onChange={(e) => {
													control.onChange(e.detail.value);
													setIsAllocation(e.detail.value);
													if (e.detail.value) {
														// 如果选择到店分配，清空已选择的手艺人
														setSelectedEmployees([]);
														form.setFieldValue('staff', '');
													}
												}}
												color="#FF9500"
												style={{
													transform: 'scale(0.8)',
													transformOrigin: 'right center'
												}}
											/>
										</View>
									)}
								</Form.Field>
							</View> */}

                {/* 手艺人选择区域 - 仅在不到店分配时显示 */}
                <View className="flex items-center gap-3 border-b border-0 border-standard-border border-solid">
                  <Text className="text-standard-4 w-[70px] text-sm sm:text-xs">手艺人</Text>
                  <Form.Field name="staff">
                    <View
                      className="flex-1  p-2"
                      onClick={() => setIsEmployeeSelectorVisible(true)}
                    >
                      {selectedEmployees.length > 0 || isAllocation ? (
                        <View className="flex items-center gap-2 overflow-hidden">
                          {selectedEmployees.slice(0, 4).map((employee, index) => {
                            const employeeInfo = employeeList.find(item => item.employeeId === employee.templateEmployeeId);
                            return (
                              <View key={employee.templateEmployeeId} className="flex items-center bg-[#F8F8F8] rounded-lg px-2 py-1 flex-shrink-0">
                                <Text className="text-[#FF9500] text-sm sm:text-xs">{employeeInfo?.employeeNickname}</Text>
                              </View>
                            );
                          })}
                          {selectedEmployees.length > 4 && (
                            <View className="flex items-center rounded-lg bg-[#F8F8F8] px-2 py-1 flex-shrink-0">
                              <Text className="text-[#FF9500] text-sm sm:text-xs">+{selectedEmployees.length - 4}</Text>
                            </View>
                          )}
                          {isAllocation && (<View className="flex items-center rounded-lg bg-[#F8F8F8] px-2 py-1 flex-shrink-0">
                            <Text className="text-[#FF9500] text-sm sm:text-xs">到店分配</Text>
                          </View>)}
                        </View>
                      ) : (
                        <Text className="text-standard-3 text-sm sm:text-xs">请选择手艺人</Text>
                      )}
                    </View>
                  </Form.Field>
                </View>
              </View>
            </View>

            {/* 项目 */}
            <View className="bg-white shadow-standard rounded-lg px-4 pb-4">
              <View
                className="flex items-center justify-between h-[50px] border-b border-0 border-standard-border border-solid"
                onClick={() => setIsServiceSelectorVisible(true)}
              >
                <Text className="text-standard-5 font-medium text-base sm:text-sm">项目<Text className="text-error">*</Text></Text>
                <View className="flex items-center gap-2">
                  <Text className="text-standard-4 text-sm sm:text-xs">已选{selectedItems.length}项</Text>
                </View>
              </View>
              {selectedItems.map((item, index) => {
                const serviceInfo = serviceItems.find(s => s.id === item.templateProjectId?.toString());
                const projectName = serviceInfo?.projectName || '未知项目';
                const durationText = item.projectDuration ? `${item.projectDuration}分钟` : '';
                const typeText = item.isAllocation ? '到店分配' : '指定手艺人';

                return (
                  <View
                    key={item.templateProjectId}
                    className="py-3 bg-[#F8F8F8] rounded-lg mt-3 px-3 relative"
                  >
                    <View className="flex justify-between items-center">
                      <View className="flex-1">
                        <Text className="text-[15px] text-[#333333] font-medium text-base sm:text-sm">{projectName}</Text>
                        <View className="flex items-center mt-1">
                          <Text className="text-xs text-[#999999] text-sm sm:text-xs">{' '}</Text>
                        </View>
                      </View>
                      <View className=" flex flex-row">
                        <View className="flex flex-1 items-center justify-center mr-2">
                          <Text className="text-[#333333] text-sm sm:text-xs">x{item.buyCount}</Text>
                        </View>
                        <View className="flex items-center">
                          <Text className="text-xs text-[#999999] text-sm sm:text-xs ml-2">{durationText}</Text>
                        </View>
                      </View>
                    </View>
                    <View
                      className="absolute right-[1px] top-[1px] w-[18px] h-[18px] bg-[#E0E0E0] rounded-bl-md rounded-tr-md flex items-center justify-center"
                      onClick={() => handleRemoveItem(index)}
                    >
                      <Text className="text-[#666666] text-sm sm:text-xs leading-none">×</Text>
                    </View>
                  </View>
                )
              })}
            </View>
          </View>

          {/* 确定按钮 */}
          <View className="sticky bottom-0 left-0 right-0 p-3 bg-white z-10">
            <View
              className="w-full h-[44px] bg-[#FFCC33] rounded-full flex items-center justify-center"
              onClick={handleSubmit}
            >
              <Text className="text-white text-base sm:text-sm">确定</Text>
            </View>
          </View>

          {/* 日期选择弹窗 */}
          {/* <Popup 
					open
					onClose={() => setIsDatePickerOpen(false)}
				>
					<DatePicker
						mode="format"
						format="YYYY-MM-DD HH:mm"
						dateTime={dateTime}
						start={Date.now()}
						onConfirm={(date: any) => {
							form.setFieldsValue({
								appointmentTime: dayjs(date).format("YYYY-MM-DD HH:mm")
							});
							setIsDatePickerOpen(false);
						}}
					/>
				</Popup> */}
        </Form>

        {/* 项目选择器 */}
        <ServiceSelector
          visible={isServiceSelectorVisible}
          onClose={() => setIsServiceSelectorVisible(false)}
          onSelect={handleServiceSelect}
          services={serviceItems}
          selectedServices={serviceItems.filter(item =>
            selectedItems.some(selected => selected.templateProjectId === Number(item.id))
          )}
        />

        {/* 手艺人选择器 */}
        <EmployeeSelector
          visible={isEmployeeSelectorVisible}
          onClose={() => setIsEmployeeSelectorVisible(false)}
          onSelect={(employees: EmployeeItem[], allocation: any) => handleEmployeeSelect(employees, allocation)}
          selectedEmployees={employeeList.filter(item =>
            selectedEmployees.some(selected => selected.templateEmployeeId === item.employeeId)
          )}
          employees={employeeList}
        />

        <SuccessModal
          visible={successVisible}
          onClose={() => {
            setSuccessVisible(false);
            Taro.redirectTo({
              url: '/reservation/reservation-management/reservation-management-page'
            });
          }}
          image={CartEmptyImage}
          text="已经成功添加预约项目"
          buttonText="我知道了"
        />
      </View>
    </PageWithNav>
  );
};


export default AppointmentPage;
