@use "@/theme.scss" as t;

.modalContainer {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.5);

	:global {
		.weui-input {
			height: 90px !important;
		}
	}
}

.modalContent {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 90%;
	max-height: 90vh;
	overflow: hidden;
	background-color: #fff;
	border-radius: 16px;
}

.modalHeader {
	position: relative;
	padding: 30px 30px 20px;
}

.modalTitle {
	color: t.$color-black-1;
	font-weight: bold;
	font-size: t.$font-size-base;
}

.modalSubtitle {
	margin-top: 8px;
	color: t.$color-grey-2;
	font-size: t.$font-size-sm;
}

.closeBtn {
	position: absolute;
	top: 20px;
	right: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	background-color: #f00;
	border-radius: 50%;
}

.closeBtnText {
	color: #fff;
	font-size: t.$font-size-base;
	line-height: 1;
}

.section {
	padding: 20px 30px;
}

.sectionTitle {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	color: t.$color-black-1;
	font-size: t.$font-size-base;
}

.sectionIcon {
	width: 30px;
	height: 30px;
	margin-right: 10px;
	background-color: t.$color-brand;
	border-radius: 4px;
}

.optionGroup {
	display: flex;
	justify-content: space-between;
}

.option {
	display: flex;
	flex: 1;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100px;
	margin: 0 10px;
	color: t.$color-black-1;
	font-size: t.$font-size-base;
	background-color: t.$background-color;
	border-radius: 8px;
}

.optionDesc {
	margin-top: 8px;
	color: t.$color-grey-2;
	font-size: t.$font-size-sm;
}

.optionActive {
	background-color: rgba(t.$color-brand, 0.2);
	border: 2px solid t.$color-brand;
}

.formItem {
	position: relative;
	margin-bottom: 20px;
}

.input {
	height: 90px;
	padding: 0 30px;
	color: t.$color-black-1;
	font-size: t.$font-size-base;
	background-color: t.$background-color;
	border-radius: 8px;
}

.placeholder {
	color: t.$color-grey-2;
	font-size: t.$font-size-base;
}

.inputSuffix {
	position: absolute;
	top: 50%;
	right: 30px;
	color: t.$color-black-1;
	font-size: t.$font-size-base;
	transform: translateY(-50%);
}

.inputIcon {
	position: absolute;
	top: 50%;
	right: 30px;
	font-size: t.$font-size-base;
	transform: translateY(-50%);
}

.btnGroup {
	display: flex;
	height: 100px;
	margin-top: 20px;
}

.cancelBtn {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: t.$font-size-base;
	background-color: #000;
}

.submitBtn {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: t.$font-size-base;
	background-color: t.$color-brand;
}
