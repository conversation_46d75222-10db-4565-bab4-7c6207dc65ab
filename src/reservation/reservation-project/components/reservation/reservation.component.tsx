import { View, Text, Input, Picker } from "@tarojs/components";
import { useEffect, useState } from "react";
import styles from "./index.module.scss";

interface AppointmentModalProps {
	record: any;
	templatesData: any[];
	visible: boolean;
	onClose: () => void;
	onSubmit: (data: AppointmentData) => void;
}

interface AppointmentData {
	templateId: number | null | string; // 预约项目模版id
	arriveNum: number; // 预约人数
	appointmentTime: string; // 预约时间
	name: string;
	phone: string; //
	customerRemarks: string; // 顾客备注
	merchantRemarks: string; // 商家备注
}

const AppointmentModal: React.FC<AppointmentModalProps> = ({
	record, // 预约信息
	templatesData,
	visible,
	onClose,
	onSubmit,
}) => {
	const [data, setData] = useState<AppointmentData>({
		templateId: "1",
		arriveNum: 1,
		appointmentTime: "",
		name: "",
		phone: "",
		customerRemarks: "",
		merchantRemarks: "",
	});

	useEffect(() => {
		record && setData(record);
	}, [record]);

	const handleChange = (payload) => {
		setData((prev) => ({
			...prev,
			...payload,
		}));
	};

	const handleSubmit = () => {
		onSubmit(data);
	};

	if (!visible) return null;

	return (
		<View className={styles.modalContainer}>
			<View className={styles.modalContent}>
				<View className={styles.modalHeader}>
					<Text className={styles.modalTitle}>预约</Text>
					<Text className={styles.modalSubtitle}>预约预订</Text>
					<View className={styles.closeBtn} onClick={onClose}>
						<Text className={styles.closeBtnText}>×</Text>
					</View>
				</View>

				<View className={styles.section}>
					<View className={styles.sectionTitle}>
						<View className={styles.sectionIcon}></View>
						<Text>选择预约项目</Text>
					</View>
					<View className={styles.optionGroup}>
						{templatesData.map((item) => {
							return (
								<View
									key={item.templatesName}
									className={`${styles.option} ${
										data.templateId === item.templateId
											? styles.optionActive
											: ""
									}`}
									onClick={() =>
										handleChange({
											templateId: item.templateId,
										})
									}
								>
									<Text>{item.templateName}</Text>
									<Text className={styles.optionDesc}>
										{item.templateId}
									</Text>
								</View>
							);
						})}
					</View>
				</View>

				<View className={styles.section}>
					<View className={styles.sectionTitle}>
						<View className={styles.sectionIcon}></View>
						<Text>填写预约信息</Text>
					</View>
					<View className={styles.formItem}>
						<Input
							className={styles.input}
							type="text"
							placeholder="预约人数    例如: 2"
							placeholderClass={styles.placeholder}
							value={data.arriveNum}
							onInput={(e) =>
								handleChange({ arriveNum: e.detail.value })
							}
						/>
						<Text className={styles.inputSuffix}>人</Text>
					</View>
					<Picker
						mode="date"
						onChange={(e) =>
							handleChange({ appointmentTime: e.detail.value })
						}
					>
						<View className={styles.formItem}>
							<Input
								className={styles.input}
								type="text"
								placeholder="预约时间    请选择预约时间"
								placeholderClass={styles.placeholder}
								value={data.appointmentTime}
								disabled
							/>
							<Text className={styles.inputIcon}>📅</Text>
						</View>
					</Picker>
				</View>

				<View className={styles.section}>
					<View className={styles.sectionTitle}>
						<View className={styles.sectionIcon}></View>
						<Text>填写顾客信息</Text>
					</View>
					<View className={styles.formItem}>
						<Input
							className={styles.input}
							type="text"
							placeholder="您的名字    例如: 张三"
							placeholderClass={styles.placeholder}
							value={data.name}
							onInput={(e) =>
								handleChange({ name: e.detail.value })
							}
						/>
					</View>
					<View className={styles.formItem}>
						<Input
							className={styles.input}
							type="number"
							placeholder="您的电话    例如: 18086257177"
							placeholderClass={styles.placeholder}
							value={data.phone}
							onInput={(e) =>
								handleChange({ phone: e.detail.value })
							}
						/>
					</View>
					<View className={styles.formItem}>
						<Input
							className={styles.input}
							type="text"
							placeholder="顾客备注    请输入备注..."
							placeholderClass={styles.placeholder}
							value={data.customerRemarks}
							onInput={(e) =>
								handleChange({
									customerRemarks: e.detail.value,
								})
							}
						/>
					</View>
					<View className={styles.formItem}>
						<Input
							className={styles.input}
							type="text"
							placeholder="商家备注    请输入备注..."
							placeholderClass={styles.placeholder}
							value={data.merchantRemarks}
							onInput={(e) =>
								handleChange({
									merchantRemarks: e.detail.value,
								})
							}
						/>
					</View>
				</View>

				<View className={styles.btnGroup}>
					<View className={styles.cancelBtn} onClick={onClose}>
						<Text>关闭</Text>
					</View>
					<View className={styles.submitBtn} onClick={handleSubmit}>
						<Text>提交</Text>
					</View>
				</View>
			</View>
		</View>
	);
};

export default AppointmentModal;
