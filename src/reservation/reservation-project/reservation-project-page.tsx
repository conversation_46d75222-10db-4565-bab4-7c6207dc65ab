import React, { useCallback, useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { Text, View, ScrollView } from "@tarojs/components";
import { AtTag, AtNoticebar, AtAvatar, AtLoadMore, AtToast } from "taro-ui";
import useStore from "@/hook/store";
import { GlobalInfoStore } from "@/store/global-info.store";
import PageContainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import Reservation from "./components/reservation/reservation.component";
import styles from "./reservation-project-page.module.scss";

const btnsMap = {
	edit: {
		title: "修改",
		url: "/employee/staff-add-staff/staff-add-staff-page",
		type: "edit",
	},
	delete: {
		title: "删除",
		type: "delete",
		url: "/employee/staff-add-staff/staff-add-staff-page",
	},
};

const Index: React.FC = () => {
	const globalInfo: GlobalInfoStore = useStore().globalInfoStore;
	const container: PageContainerRef = useRef(null);
	const [{ dataSource, total }, setDataSource] = useState<any>({
		dataSource: [],
		total: 0,
	});
	const pageSize = 10;
	const [pageNum, setPageNum] = useState(1);
	const [status, setStatus] = useState<"more" | "noMore" | "loading">("more"); // more-加载更多 loading-加载中 noMore-无更多数据
	const [clientHeight, setClientHeight] = useState(0); // 可视区域高度
	const [visible, setVisible] = useState(false); // 是否显示添加员工弹层
	const [isOpened, setIsOpened] = useState(false);
	const [record, setRecord] = useState<any>({});

	// 可视区域高度
	useEffect(() => {
		const query = Taro.createSelectorQuery();
		query
			.select("#scrollView")
			.boundingClientRect((rect: any) => {
				rect && setClientHeight(rect?.height);
			})
			.exec();

		// 获取商家模版
		service.business.appointmentTemplateController
			.getAppointmentTemplateList({ businessId: globalInfo?.businessId })
			.then((res) => {
				if (res?.data) {
					const list = (res?.data || []).map((item) => ({
						templateName:
							item.templateName ?? `模版${item.templateId}`,
						templateId: item.templateId,
					}));
					globalInfo.setTemplatesData(list);
				}
			});
	}, []);

	useEffect(() => {
		getList();
	}, [pageNum]);

	const getList = useCallback(() => {
		setStatus("loading");
		service.business.appointmentRecordController
			.getAppointmentRecordList({
				pageNum,
				pageSize,
				businessId: globalInfo?.businessId,
			})
			.then((res) => {
				if (res?.data) {
					setDataSource({
						dataSource: dataSource?.concat(res?.data) ?? dataSource,
						total: res?.total ?? 0,
					});
					setStatus(
						pageNum * pageSize < res?.total ? "more" : "noMore"
					);
				}
			});
	}, []);

	// 滚动事件
	const onScroll = (e) => {
		const { scrollTop, scrollHeight } = e.detail;

		// 判断是否滚动到底部
		if (scrollTop + clientHeight >= scrollHeight - 30) {
			if (pageNum * pageSize < total) {
				setPageNum(pageNum + 1);
				setStatus("more");
			} else {
				setStatus("noMore");
			}
		}
	};
	const refreshList = () => {
		setVisible(false);
		setPageNum(1);
		setDataSource({
			dataSource: [],
			total: 0,
		});
	};

	const handleType = (type, id) => {
		if (type === "edit") {
			onEdit(id);
		} else {
			onDelete(id);
		}
	};

	const onEdit = (id) => {
		service.business.appointmentRecordController
			.getAppointmentRecordRecordId({
				recordId: id,
			})
			.then((res) => {
				if (res) {
					setRecord(res);
					setVisible(true);
				}
				setIsOpened(false);
			});
	};

	const onDelete = (id) => {
		setIsOpened(true);
		service.business.appointmentRecordController
			.deleteAppointmentRecordRecordId({
				recordId: id,
			})
			.then((res) => {
				if (res) {
					refreshList();
				}
				setIsOpened(false);
			});
	};

	const onSubmit = (data) => {
		service.business.appointmentRecordController
			.postAppointmentRecord(data)
			.then((res) => {
				if (res) {
					refreshList();
				}
			});
	};

	return (
		<PageContainer className={styles.container} ref={container}>
			<View className={styles.top}>
				<View className={styles.title}>
					<Text className={styles.person}>预约项目</Text>
					<Text className={styles.total}>共有{total}份预约记录</Text>
				</View>

				<AtTag circle active onClick={() => setVisible(true)}>
					+ 添加预约
				</AtTag>
			</View>
			<View className={styles.bottom}>
				<AtNoticebar>
					员工可为消费者预约项目，预约项目后，消费者可在个人中心查看预约记录
				</AtNoticebar>
				<View className={styles.list}>
					<ScrollView
						scrollY
						scrollTop={0}
						style={{
							height: `calc(100vh - 92px)`,
						}}
						id="scrollView"
						lowerThreshold={20}
						upperThreshold={20}
						// scrollWithAnimation
						// refresherEnabled // 开启下拉刷新
						// refresherTriggered={refreshing} // 控制刷新状态
						// onRefresherRefresh={onRefresh} // 下拉刷新事件
						onScroll={onScroll}
					>
						{dataSource.map((item) => (
							<View
								className={styles.listItem}
								key={item.recordId}
							>
								<View
									className={`${styles.status} ${
										item.status
											? styles.openStatus
											: styles.closeStatus
									}`}
								>
									{item.status ? "启用" : "禁用"}
								</View>
								<View className={styles.left}>
									<AtAvatar
										circle
										image={item.avatar}
									></AtAvatar>
									<View className={styles.text}>
										<View>
											<Text>{item.username ?? "-"}</Text>
											<Text className={styles.sub}>
												｜ {item.sex ?? "-"}｜
												{item.position}
											</Text>
										</View>
										<Text className={styles.sub}>
											{item.employeeNickname}
										</Text>
									</View>
								</View>
								<View>
									{[btnsMap.statistics, btnsMap.edit].map(
										(obj) => (
											<Text
												className={styles.optTag}
												key={obj.title}
											>
												<AtTag
													circle
													active
													type="primary"
													size="small"
													onClick={() =>
														handleType(
															obj.type,
															item.recordId
														)
													}
												>
													{obj.title}
												</AtTag>
											</Text>
										)
									)}
								</View>
							</View>
						))}
						<AtLoadMore status={status} />
					</ScrollView>
				</View>
			</View>

			<AtToast
				isOpened={isOpened}
				text="正在加载..."
				hasMask
				status="loading"
				duration={0}
			></AtToast>

			{visible && (
				<Reservation
					record={record}
					templatesData={globalInfo?.templatesData}
					visible={visible}
					onClose={() => setVisible(false)}
					onSubmit={onSubmit}
				/>
			)}
		</PageContainer>
	);
};

export default Index;
