import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  Input,
  Textarea,
  Switch,
  Form,
} from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { postMemberRecordAddNew } from '@/service/business/huiyuanxiangqingxiaofeimingxi';
import { getRelationGetMember } from '@/service/business/dianpuhuiyuanyuhuiyuankaguanlianguanli';
import { postShopProjectAll } from '@/service/business/shangjiaxiangmuguanli';
import { getEmployeeList } from '@/service/business/businessEmployeeController';
import EmployeeSelector from '@/view/component/employee-selector/index';
import ProjectMultiSelectModal from '@/view/component/project-multi-select-modal/ProjectMultiSelectModal';
import AmountInputModal from '@/view/component/amount-input-modal/AmountInputModal';
import memberUser from '@/assets/image/common/member-user.png';
import styles from './member-billing-detail-page.module.scss';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import memberBillingDetailStore from './store';

declare namespace BUSINESS {
  interface TMemberVo {
    memberId?: string;
    nickName?: string;
    phonenumber?: string;
    sex?: number;
    avatar?: string;
    balance?: number;
    coupon?: number;
    countNumber?: number;
    cardLevel?: string;
    cardName?: string;
    cardImageUrl?: string;
    cardType?: string;
    cardNumber?: string;
    discountRate?: number;
    validityType?: string;
    endDate?: string;
    staffRemark?: string;
    status?: number;
    setmealList?: any[];
    lastStoreDate?: string;
    remarks?: string;
    userName?: string;
    realName?: string;
    weixinOpenid?: string;
    birthday?: string;
    transferTime?: string;
    banStatus?: string;
    customerRemark?: string;
    isSmsSubscribe?: number;
    giftAmount?: number;
    discountValue?: number;
    cardTimes?: number;
    giftTimes?: number;
    validDays?: number;
    validMonths?: number;
    integral?: number;
    giftIntegral?: number;
    giftCoupon?: number;
    giftCountNumber?: number;
    operationType?: string;
    operationTime?: string;
    amount?: number;
    employeeId?: string;
    businessId?: string;
    templateId?: string;
    memberCardRelationId?: number;
  }

  interface TMemberOperRecordPerAddVo {
    memberId: number;
    memberCardRelationId: string;
    businessId: number;
    employeeId: string;
    amount: number;
    giftAmount: number;
    balance: number;
    receivedAmount: number;
    operRecordBPLists: any[];
    cardType: string;
    cardName: string;
    cardNumber: string;
    templateId: string;
    nickName: string;
    phonenumber: string;
    enickName: string;
  }

  interface RBoolean {
    code?: number;
    msg?: string;
    data?: boolean;
  }
}

const MemberBillingDetailPage: React.FC = () => {
  const router = useRouter();
  const { scanResult } = router.params;

  const [memberInfo, setMemberInfo] = useState<BUSINESS.TMemberVo | null>(null);
  const { cart } = memberBillingDetailStore;

  const [subtotal, setSubtotal] = useState(0);
  const [projectList, setProjectList] = useState<any[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<any[]>([]);
  const [employeeSelectorVisible, setEmployeeSelectorVisible] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<any[]>([]);
  const [remark, setRemark] = useState('');
  const [subtotalModalVisible, setSubtotalModalVisible] = useState(false);
  const [subtotalInput, setSubtotalInput] = useState(subtotal.toString());
  const [employees, setEmployees] = useState<any[]>([]);

  const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return '暂无记录';
    try {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      // return `${year}-${month}-${day} ${hours}:${minutes}`;
      return `${month}-${day} ${hours}`;
    } catch (error) {
      return '暂无记录';
    }
  };

  const fetchMemberInfo = async () => {
    if (!scanResult) return;
    try {
      const response = await getRelationGetMember({
        memberCardRelationId: Number(scanResult),
      });
      if (response) {
        setMemberInfo(response as BUSINESS.TMemberVo);
        // setSubtotal(0);
      }
    } catch (error) {
      console.error('获取会员信息失败:', error);
      Taro.showToast({
        title: '获取会员信息失败',
        icon: 'none',
      });
    }
  };

  useEffect(() => {
    const { cart, totalAmount } = memberBillingDetailStore;
    if (cart.length < 0) {
      Taro.navigateBack();
      return;
    }

    setSubtotal(totalAmount);
    setSelectedProjects(cart.map((i: any) => i.service));

    if (scanResult) {
      fetchMemberInfo();
    } else {
      const memberDetail = memberBillingDetailStore.memberInfo as any;
      const card = memberBillingDetailStore.card as any;

      memberDetail.memberCardRelationId = card.memberCardRelationId;
      memberDetail.cardType = card.cardType || '';
      memberDetail.cardName = card.cardName || '';
      memberDetail.cardNumber = card.cardNumber || '';
      memberDetail.templateId = card.templateId || '';
      memberDetail.balance = card.balance;

      setMemberInfo(memberDetail);
    }

    // 获取项目列表
    const fetchProjects = async () => {
      try {
        const loginInfo = Taro.getStorageSync('loginInfo');
        const res = await postShopProjectAll({
          businessId: loginInfo.businessId,
          status: 1,
        });
        if (res) {
          setProjectList(res || []);
        }
      } catch (error) {
        Taro.showToast({ title: '获取项目失败', icon: 'none' });
      }
    };
    fetchProjects();

    // 获取员工列表
    const fetchEmployees = async () => {
      try {
        const res = await getEmployeeList({ businessEmployee: {} });
        if (res?.data) {
          setEmployees(res.data);
        }
      } catch (error) {
        Taro.showToast({ title: '获取员工失败', icon: 'none' });
      }
    };
    fetchEmployees();
  }, []);

  const handleSubmit = (e) => {
    console.log('表单提交:', e);
  };

  const handleSubtotalChange = (e) => {
    let value = e.detail.value.replace(/[^\d.]/g, '');
    // 只保留两位小数
    if (value) {
      // 避免多余小数点
      value = value.replace(/^([0-9]+\.[0-9]{0,2}).*$/, '$1');
    }
    setSubtotal(Number(value));
  };

  // 办卡与更换顾客点击事件
  const handleOpenCard = () => {
    Taro.showToast({ title: '办卡功能开发中', icon: 'none' });
  };
  const handleChangeMember = () => {
    Taro.showToast({ title: '更换顾客功能开发中', icon: 'none' });
  };

  const handleEmployeeSelect = (employees: any[]) => {
    console.log(22222);
    const mappedEmployees = employees.map((employee, index) => ({
      seq: index,
      templateEmployeeId: employee.employeeId,
    }));
    setSelectedEmployees(mappedEmployees);
    // form.setFieldValue('staff', employees[0]?.employeeNickname || '');
  };

  const handleCheckout = async () => {
    if (!memberInfo) {
      Taro.showToast({ title: '请选择会员', icon: 'none' });
      return;
    }
    // if (selectedProjectIds.length === 0) {
    //   Taro.showToast({ title: '请选择项目', icon: 'none' });
    //   return;
    // }
    if (selectedEmployees.length === 0) {
      Taro.showToast({ title: '请选择员工', icon: 'none' });
      return;
    }
    try {
      const loginInfo = Taro.getStorageSync('loginInfo');

      // 组装项目明细
      const operRecordBPLists = selectedProjects.map((p) => {
        const project = projectList.find(
          (item) => String(item.projectId) === p.id
        );
        return {
          memberId: Number(memberInfo.memberId),
          count: 1, // 默认1次，可根据业务调整
          categoryId: project?.categoryId || 0,
          projectId: project?.projectId,
          businessId: String(loginInfo.businessId),
        };
      });
      const params: BUSINESS.TMemberOperRecordPerAddVo = {
        memberId: Number(memberInfo.memberId),
        memberCardRelationId:
          memberInfo.memberCardRelationId !== undefined
            ? String(memberInfo.memberCardRelationId)
            : String(scanResult),
        businessId: Number(loginInfo.businessId),
        employeeId: selectedEmployees
          .map((e) => e.templateEmployeeId)
          .join(','),
        amount: subtotal, // 操作前余额
        giftAmount: subtotal, // 消费金额，非金额相关卡为0
        balance: memberInfo.balance ?? 0, // 本次消费后余额
        receivedAmount: subtotal, // 实收金额
        operRecordBPLists,
        cardType: memberInfo.cardType || '',
        cardName: memberInfo.cardName || '',
        cardNumber: memberInfo.cardNumber || '',
        templateId: memberInfo.templateId || '',
        nickName: memberInfo.nickName || '',
        phonenumber: memberInfo.phonenumber || '',
        enickName: selectedEmployees
          .map(
            (e) =>
              employees.find((emp) => emp.employeeId === e.templateEmployeeId)
                ?.employeeNickname
          )
          .join(','),
        // 其他字段可根据需要补充
      };
      const res = (await postMemberRecordAddNew(
        params
      )) as unknown as BUSINESS.RBoolean;
      if (typeof res === 'boolean' && res) {
        Taro.showToast({ title: '结账成功', icon: 'success' });
        setTimeout(() => {
          memberBillingDetailStore.clear();
          Taro.redirectTo({
            url: '/view/page/tab/home/<USER>',
          });
        }, 2000);
        // 可根据需要跳转或刷新
      } else if (res && typeof res === 'object' && 'msg' in res) {
        Taro.showToast({ title: (res as any).msg || '结账失败', icon: 'none' });
      } else {
        Taro.showToast({ title: '结账失败', icon: 'none' });
      }
    } catch (error) {
      Taro.showToast({ title: '结账异常', icon: 'none' });
    }
  };

  return (
    <PageWithNav title="开单结账">
      <View className={styles.page}>
        {/* 会员卡信息卡片 */}
        <View className="flex items-center rounded-2xl bg-[#2B2929] px-2 py-3 mt-3 mb-4 w-[calc(100%-32px)] mx-4">
          <Image
            className="w-[92rpx] h-[92rpx] rounded-full bg-[#fff] flex-shrink-0"
            src={memberInfo?.avatar || memberUser}
          />
          <View className="flex-1 min-w-0 ml-3">
            <View className="flex items-center mb-1">
              <Text className="font-semibold text-base text-white truncate max-w-[220rpx]">
                {memberInfo?.nickName || '未命名'}
              </Text>
              {memberInfo?.cardLevel && (
                <View className="flex items-center ml-2">
                  <Image
                    className="w-[108rpx] h-[48rpx] mr-1"
                    src={require('@/assets/image/member/vip.png')}
                  />
                </View>
              )}
            </View>
            <Text className="text-xs text-[#fff] opacity-70">
              上次到店 {formatDate(memberInfo?.lastStoreDate)}
            </Text>
          </View>
          <View className="flex flex-shrink-0 ml-3 gap-2">
            <View
              className="px-4 h-6 flex items-center rounded-2xl bg-[#FFE9B0] text-[#7A5B13] text-xs font-semibold whitespace-nowrap cursor-pointer"
              onClick={handleOpenCard}
            >
              办卡
            </View>
            {/* <View
              className="px-4 h-6 flex items-center rounded-2xl bg-[#FFE9B0] text-[#7A5B13] text-xs font-semibold whitespace-nowrap cursor-pointer"
              onClick={handleChangeMember}
            >
              更换顾客
            </View> */}
          </View>
        </View>

        {/* 服务项目卡片 */}
        {/* <View className={styles.projectCard}>
          <View className={styles.projectRow}>
            <Image className={styles.projectIcon} src={require('@/assets/image/member/project.png')} />
            <Text className={styles.projectName}>{memberInfo?.cardName || ''}</Text>
          </View>
          <Text className={styles.projectPrice}>￥{subtotal.toFixed(2)}</Text>
        </View> */}
        <View className={styles.projectCardWrap}>
          {cart.map((item: any) => (
            <View className={styles.projectCard} key={item.service.id}>
              <View className={styles.projectRow}>
                <Image
                  className={styles.projectIcon}
                  src={require('@/assets/image/member/project.png')}
                />
                <Text className={styles.projectName}>
                  {item.service.name || ''}
                </Text>
                <Text className={styles.projectCount}>x {item.count}</Text>
              </View>
              <Text className={styles.projectPrice}>
                ￥{item.service.price?.toFixed(2)}
              </Text>
            </View>
          ))}
        </View>

        {/* 表单内容 */}
        <Form onSubmit={handleSubmit}>
          <ProjectMultiSelectModal
            visible={modalVisible}
            projectList={projectList.filter(
              (p) => typeof p.projectId === 'number'
            )}
            selectedIds={selectedProjectIds}
            onClose={() => setModalVisible(false)}
            onConfirm={(ids) => {
              setSelectedProjectIds(ids);
              setSelectedProjects(
                projectList.filter((p) => ids.includes(p.projectId))
              );
              setModalVisible(false);
            }}
          />
          {/* 选择员工 */}
          <View className={styles.formRow}>
            <Text className={styles.formLabel}>选择员工</Text>
            <View
              className={styles.formValue}
              style={{ flex: 1, cursor: 'pointer' }}
              onClick={() => setEmployeeSelectorVisible(true)}
            >
              <Text
                className={
                  !selectedEmployees.length ? styles.placeholderText : ''
                }
              >
                {selectedEmployees.length
                  ? selectedEmployees
                      .map(
                        (e) =>
                          employees.find(
                            (emp) => emp.employeeId === e.templateEmployeeId
                          )?.employeeNickname
                      )
                      .join('，')
                  : '请选择员工'}
              </Text>
              <Text className={styles.formValueArrow}>{'>'}</Text>
            </View>
          </View>
          <EmployeeSelector
            visible={employeeSelectorVisible}
            onClose={() => setEmployeeSelectorVisible(false)}
            onSelect={(employees: EmployeeItem[], allocation: any) =>
              handleEmployeeSelect(employees, allocation)
            }
            selectedEmployees={employees?.filter((item) =>
              selectedEmployees.some(
                (selected) => selected.templateEmployeeId === item.employeeId
              )
            )}
            employees={employees}
          />
          {/* 会员权益 */}
          {/* <View className={styles.formRow}>
            <Text className={styles.formLabel}>会员权益</Text>
            <View className={styles.formValue}>
              <Text className={!memberInfo?.remarks ? styles.placeholderText : ''}>
                {memberInfo?.remarks || '暂无权益'}
              </Text>
              <Text className={styles.formValueArrow}>{'>'}</Text>
            </View>
          </View> */}
          {/* 余额 */}
          <View className={styles.formRow}>
            <Text className={styles.formLabel}>余额</Text>
            <View className={styles.formValue}>
              <Text style={{ color: '#ffb400', fontWeight: 600 }}>
                ￥{memberInfo?.balance?.toFixed(2) ?? '0.00'}
              </Text>
            </View>
          </View>
          {/* 小计 */}
          <View className={styles.formRow}>
            <Text className={styles.formLabel}>小计</Text>
            <View
              className={styles.formValue}
              style={{ flex: 1, cursor: 'pointer' }}
              onClick={() => {
                setSubtotalInput(subtotal.toString());
                setSubtotalModalVisible(true);
              }}
            >
              <Text style={{ color: '#ffb400', fontWeight: 600 }}>
                ￥{subtotal.toFixed(2)}
              </Text>
              <Text className={styles.formValueArrow}>{'>'}</Text>
            </View>
          </View>
          <AmountInputModal
            visible={subtotalModalVisible}
            value={subtotalInput}
            onInput={(val) => {
              let value = val.replace(/[^\d.]/g, '');
              value = value.replace(/^([0-9]+\.[0-9]{0,2}).*$/, '$1');
              setSubtotalInput(value);
            }}
            onCancel={() => setSubtotalModalVisible(false)}
            onConfirm={() => {
              setSubtotal(Number(subtotalInput) || 0);
              setSubtotalModalVisible(false);
            }}
            title="请输入小计金额"
            placeholder="请输入金额"
          />
          {/* 优惠券 */}
          <View className={styles.formRow}>
            <Text className={styles.formLabel}>优惠券</Text>
            <View className={styles.formValue}>
              <Text className={styles.formValueCoupon}>
                {memberInfo?.coupon
                  ? `可用${memberInfo.coupon}张优惠券`
                  : '暂无可用优惠券'}
              </Text>
              <Text className={styles.formValueArrow}>{'>'}</Text>
            </View>
          </View>
          {/* 备注 */}
          <View className={styles.remarkRow}>
            <View className={styles.remarkTop}>
              <Text className={styles.remarkLabel}>备注</Text>
              <Text className={styles.remarkCount}>{remark.length}/200</Text>
            </View>
            <View className={styles.remarkBottom}>
              <Textarea
                className={styles.remarkTextarea}
                value={remark}
                onInput={(e) => setRemark(e.detail.value)}
                maxlength={200}
                placeholder="编辑客户信息"
                placeholderStyle="color:#bbb;font-size:14px;"
                style={{
                  background: 'none',
                  border: 'none',
                  padding: 0,
                  width: '100%',
                }}
              />
            </View>
          </View>
        </Form>

        {/* 底部操作栏 */}
        <View className={styles.footerBar}>
          <Text className={styles.payLabel}>应付：</Text>
          <Text className={styles.payAmount}>{subtotal.toFixed(2)}</Text>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-end',
              gap: '8px',
            }}
          >
            {/* <AtButton className={styles.footerBtn} type="secondary">
              挂单
            </AtButton> */}
            <AtButton
              className={styles.footerBtn}
              type="primary"
              onClick={handleCheckout}
            >
              结账
            </AtButton>
          </View>
        </View>
      </View>
    </PageWithNav>
  );
};

export default MemberBillingDetailPage;
