.page {
  min-height: 100vh;
  background: #f6f7fa;
  padding-bottom: 80px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  position: sticky;
  top: 0;
  z-index: 10;
}
.navLeft, .navRight {
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navTitle {
  font-size: 18px;
  font-weight: 600;
  color: #222;
}

.memberCard {
  background: linear-gradient(90deg, #3b3b3b 0%, #222 100%);
  border-radius: 12px;
  margin: 16px 16px 0 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  position: relative;
}
.avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #fff;
  margin-right: 12px;
}
.memberInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.memberNameRow {
  display: flex;
  align-items: center;
  gap: 6px;
}
.memberName {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}
.vipIcon {
  width: 54px;
  height: 24px;
}
.vipText {
  font-size: 13px;
  color: #ffb400;
  font-weight: 600;
  margin-left: 2px;
}
.lastArrive {
  font-size: 12px;
  color: #fff;
  opacity: 0.7;
  margin-top: 2px;
}
.memberBtns {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}
.cardBtn {
  background: #fff !important;
  color: #ffb400 !important;
  border-radius: 16px !important;
  font-size: 13px !important;
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 14px !important;
  border: none !important;
}

.projectCardWrap {
  margin: 16px 16px 12px 16px;
  padding: 8px 0px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.projectCard {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.projectRow {
  display: flex;
  align-items: center;
  gap: 8px;
}
.projectIcon {
  width: 22px;
  height: 22px;
}
.projectName {
  font-size: 15px;
  color: #222;
  font-weight: 500;
}
.projectCount {
  color: #999;
}
.projectPrice {
  font-size: 16px;
  color: #ffb400;
  font-weight: 600;
}

.section {
  background: #fff;
  border-radius: 12px;
  margin: 16px 16px 0 16px;
  padding: 14px 16px 10px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.sectionLabel {
  font-size: 14px;
  color: #999;
  margin-bottom: 2px;
}
.sectionValue {
  font-size: 14px;
  color: #222;
  font-weight: 500;
}
.subtotal {
  font-size: 16px;
  color: #ffb400;
  font-weight: 600;
}
.staffRow {
  display: flex;
  align-items: center;
  gap: 12px;
}
.staffName {
  font-size: 14px;
  color: #222;
  font-weight: 500;
}
.staffList {
  display: flex;
  gap: 8px;
  margin-left: 8px;
}
.staffOption {
  font-size: 13px;
  color: #666;
  background: #f6f7fa;
  border-radius: 10px;
  padding: 2px 10px;
  cursor: pointer;
}
.staffSelected {
  font-size: 13px;
  color: #fff;
  background: #ffb400;
  border-radius: 10px;
  padding: 2px 10px;
  font-weight: 600;
  cursor: pointer;
}
.appointRow {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.appointLabel {
  font-size: 13px;
  color: #666;
}
.couponTip {
  font-size: 14px;
  color: #ffb400;
  font-weight: 500;
}
.remarkInput {
  width: 100%;
  min-height: 48px;
  background: #f6f7fa;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  color: #222;
  padding: 8px;
  margin-top: 4px;
}
.remarkCount {
  font-size: 12px;
  color: #999;
  align-self: flex-end;
}
.footerBar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 0 16px;
  height: 56px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  z-index: 20;
}
.payLabel {
  font-size: 15px;
  color: #222;
  font-weight: 500;
  margin-right: 2px;
}
.payAmount {
  font-size: 20px;
  color: #ffb400;
  font-weight: 700;
  margin-right: auto;
}
.footerBtn {
  min-width: 72px;
  height: 32px !important;
  border-radius: 16px !important;
  font-size: 15px !important;
  margin-left: 6px;
  padding: 0 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
// 挂单按钮样式
.footerBtn[type='secondary'] {
  background: #fff !important;
  color: #f8cc4a !important;
  border: 1.5px solid #f8cc4a !important;
}
// 结账按钮样式
.footerBtn[type='primary'] {
  background: #f8cc4a !important;
  color: #fff !important;
  border: none !important;
}

.formRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  min-height: 48px;
  background: #fff;
  // border-radius: 12px;
  font-size: 15px;
  border-bottom: 1px solid #f2f2f2;
  position: relative;
  margin: 0 16px;
  &:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }
  &:last-child {
    border-bottom: none;
  }
  .formLabel {
    color: #999;
    min-width: 80px;
    font-size: 14px;
    flex-shrink: 0;
    font-weight: 400;
  }
  .formValue {
    flex: 1;
    color: #222;
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    .formValueYellow {
      color: #ffb400;
      font-weight: 600;
    }
    .formValueCoupon {
      color: #ffb400;
      font-weight: 500;
    }
    .formValueArrow {
      margin-left: 4px;
      font-size: 16px;
      color: #ccc;
    }
    .placeholderText, .emptyText, .noRightsText {
      color: #999;
    }
  }
  .remarkInput {
    width: 100%;
    min-height: 36px;
    background: #f6f7fa;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    color: #222;
    padding: 8px;
    margin-top: 0;
  }
  .remarkCount {
    font-size: 14px;
    color: #999;
    align-self: flex-end;
    margin-left: 8px;
  }
}
.formRowDouble {
  display: flex;
  flex-direction: column;
  background: #fff;
  // border-radius: 12px;
  margin: 0 16px 12px 16px;
  padding: 0 20px;
  border-bottom: 1px solid #f2f2f2;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  .formRowTop {
    display: flex;
    align-items: center;
    min-height: 48px;
    .formLabel {
      color: #999;
      min-width: 80px;
      font-size: 14px;
      flex-shrink: 0;
      font-weight: 400;
    }
    .formValue {
      flex: 1;
      color: #222;
      font-size: 15px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
    }
  }
  .formRowBottom {
    display: flex;
    align-items: center;
    min-height: 48px;
    .formValue {
      flex: 1;
      color: #222;
      font-size: 15px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
    }
  }
}

.remarkRow {
  background: #fff;
  border-radius: 12px;
  margin: 0 16px;
  border-bottom: 1px solid #f2f2f2;
  margin-top: 10px;
  .remarkTop {
    display: flex;
    align-items: flex-end;
    min-height: 40px;
    padding: 0 20px 0 20px;
    .remarkLabel {
      color: #222;
      font-size: 15px;
      font-weight: 500;
    }
    .remarkCount {
      flex: 1;
      text-align: right;
      color: #bbb;
      font-size: 13px;
      font-weight: 400;
    }
  }
  .remarkBottom {
    display: flex;
    align-items: flex-end;
    min-height: 40px;
    padding: 0 20px 0 20px;
    .remarkTextarea {
      flex: 1;
      color: #bbb;
      font-size: 14px;
      background: none;
      border: none;
      padding: 0;
      resize: none;
      min-height: 24px;
      height: 140px;
    }
  }
}

.editAmountInput {
  color: #ffb400;
  font-weight: 600;
  font-size: 16px;
  text-align: right;
  background: none;
  border: none;
  outline: none;
  width: 100px;
  padding: 0;
}

.customBtnGroup {
  display: flex;
  gap: 16px;
}
.customBtn {
  padding: 0 14px;
  height: 28px;
  line-height: 28px;
  background: linear-gradient(90deg, #fbe3c1 0%, #f6e1c3 100%);
  border-radius: 14px;
  color: #333;
  font-size: 14px;
  border: none;
  outline: none;
  box-shadow: none;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: background 0.2s;
}
.customBtn:active {
  background: linear-gradient(90deg, #f6e1c3 0%, #fbe3c1 100%);
} 
