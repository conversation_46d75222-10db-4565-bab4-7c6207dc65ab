import { makeAutoObservable } from 'mobx';

class MemberBillingDetailStore {
  cart = [];
  totalItems = 0;
  totalAmount = 0;
  memberInfo = {};
  card = {};

  constructor() {
    makeAutoObservable(this);
  }

  setData(data) {
    this.cart = data.cart;
    this.totalItems = data.totalItems;
    this.totalAmount = data.totalAmount;
  }

  setMemberInfo(memberInfo) {
    this.memberInfo = memberInfo;
  }

  setCard(card) {
    this.card = card;
  }

  clear() {
    this.cart = [];
    this.totalItems = 0;
    this.totalAmount = 0;
    this.memberInfo = {};
    this.card = {};
  }
}

const memberBillingDetailStore = new MemberBillingDetailStore();
export default memberBillingDetailStore;
