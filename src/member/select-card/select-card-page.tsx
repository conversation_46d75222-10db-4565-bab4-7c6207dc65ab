import { View, Text } from '@tarojs/components';
import { useRef, useState } from 'react';
import { AtButton } from 'taro-ui';
import Taro, { useRouter, useDidShow } from '@tarojs/taro';
import styles from './select-card-page.module.scss';
import PageWithNav, {
  PageContainerRef,
} from '@/view/component/page-with-nav/page-with-nav.component';
import memberBillingDetailStore from '../member-billing-detail/store';
import { colorEnmu } from './color';

const MemberListPage = () => {
  const router = useRouter();
  const { memberId, action, type = 'selectCard' } = router.params;

  // 会员卡类型数据
  const [cardTypes, setCardTypes]: any = useState([]);
  const container: React.RefObject<PageContainerRef> = useRef(null);

  // 刷新列表方法
  const fetchMemberCardList = () => {
    if (type === 'addMember') {
      service.mem.tMemberCardTemplateController
        .getMemCardtemplateList({
          pageNum: 1,
          pageSize: 1000,
        } as any)
        .then((res: any) => {
          if (res) {
            setCardTypes(
              (res || []).map((item, index) => {
                return {
                  ...item,
                  selected: index === 0 ? true : false,
                  ...colorEnmu[index % 4],
                };
              })
            );
          }
        });
    } else {
      service.businessRelation.tMemberCardRelationController
        .postRelationCardRTMemberList({
          pageNum: 1,
          pageSize: 1000,
          memberId: memberId,
        } as any)
        .then((res: any) => {
          if (res?.data) {
            setCardTypes(
              (res?.data || []).map((item, index) => {
                return {
                  ...item,
                  selected: index === 0 ? true : false,
                  ...colorEnmu[index % 4],
                };
              })
            );
          }
        });
    }
  };

  useMount(() => {
    fetchMemberCardList();
  });

  useDidShow(() => {
    const router = Taro.getCurrentInstance().router;
    if (router?.params?.action === 'created') {
      fetchMemberCardList();
    }
  });

  // 处理卡片选择
  const handleCardSelect = (index) => {
    console.log('DEBUG - index', index);
    const updatedCardTypes = cardTypes.map((item, itemIndex) => {
      return {
        ...item,
        selected: itemIndex === index,
      };
    });
    setCardTypes(updatedCardTypes);
  };

  // 处理确定按钮点击
  const handleConfirm = () => {
    const selectedCard = cardTypes.find((card) => card.selected);
    if (selectedCard) {
      Taro.showToast({
        title: `已选择${selectedCard.cardType}`,
        icon: 'success',
        duration: 2000,
      });

      if (action === 'billingSettlement') {
        memberBillingDetailStore.setCard(selectedCard);
        Taro.redirectTo({
          url: '/member/billing-settlement/billing-settlement-page',
        });
      } else {
        Taro.eventCenter.trigger(
          'UPDATE_CARD',
          {
            card: selectedCard,
          },
          {
            success: () => console.log('DEBUG - 事件触发成功'),
            fail: (err) => console.log('DEBUG - 事件触发失败:', err),
          }
        );
        // 这里可以添加导航到下一个页面的逻辑
        setTimeout(() => {
          Taro.navigateBack({
            delta: 1,
          });
        }, 1);
      }
    }
  };

  // // 处理添加新卡片
  // const handleAddCard = () => {
  //   Taro.navigateTo({
  //     url: '/view/page/single/create-member-card/create-member-card-page',
  //   });
  // };

  return (
    <PageWithNav
      showNavBar
      title={type === 'addMember' ? '选择会员卡模板' : '选择会员卡'}
      containerRef={container}
      className="relative h-full bg-white overflow-hidden"
    >
      <View className={styles.container}>
        <View
          className={`${styles.cardList}`}
          style={{ paddingBottom: '160rpx' }}
        >
          {cardTypes?.map((card, index) => (
            <View
              key={index}
              className={`${styles.cardItem} ${
                card.selected ? styles.selected : ''
              }`}
              style={{ backgroundColor: card.color }}
              onClick={() => handleCardSelect(index)}
            >
              <View
                className={styles.cardHeader}
                style={{ backgroundColor: card.backgroundColor }}
              >
                <Text className={styles.cardName}>{card.cardType}</Text>
                <View className={styles.radioButton}>
                  <View
                    className={`${styles.radioInner} ${
                      card.selected ? styles.radioSelected : ''
                    }`}
                  ></View>
                </View>
              </View>
              <View className={styles.cardContent}>
                {type === 'selectCard' && (
                  <Text className={styles.cardDiscount}>
                    会员卡号：{card.cardNumber}
                  </Text>
                )}
                <Text className={styles.cardDiscount}>
                  享受折扣：{card.cardName}
                </Text>
                <Text className={styles.cardProject}>赠送项目：-</Text>
              </View>
              <View className={styles.cardBackground}>
                <View className={styles.cardPattern}></View>
              </View>
            </View>
          ))}

          {/* {!isEditMode && (
          <View className={styles.addCardItem} onClick={handleAddCard}>
            <Image src={uploadIcon} className={styles.addIcon} />
            <Text className={styles.addText}>去设置会员卡模版</Text>
          </View>
        )} */}
        </View>

        <View
          className={styles.buttonContainer}
          style={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            padding: '20rpx',
            backgroundColor: 'white',
            boxShadow: '0 -2rpx 10rpx rgba(0,0,0,0.1)',
            zIndex: 20,
          }}
        >
          <AtButton
            type="primary"
            className={styles.confirmButton}
            onClick={handleConfirm}
          >
            确定
          </AtButton>
        </View>
      </View>
    </PageWithNav>
  );
};

export default MemberListPage;
