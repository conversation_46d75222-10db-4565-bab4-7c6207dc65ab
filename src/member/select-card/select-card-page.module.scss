// 基础样式常量
$primary-color: #ffb400;
$text-color: #333333;
$text-color-light: #ffffff;
$background-color: #f5f5f5;
$border-radius: 12px;

.container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 16px;
  padding-bottom: 80px;
  box-sizing: border-box;
  overflow-y: auto;
}

.cardList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cardItem {
  position: relative;
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  background: linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);
  padding: 10px 20px 4px 20px;
}

.cardName {
  font-size: 16px;
  // font-weight: bold;
  color: $text-color-light;
}

.radioButton {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid $text-color-light;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.radioInner {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: transparent;
}

.radioSelected {
  background-color: $text-color-light;
}

.cardContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 2;
  padding: 0px 20px 20px 20px;
  height: 74px;
}

.cardDiscount, .cardProject {
  font-size: 12px;
  color: $text-color-light;
  opacity: 0.9;
}

.cardBackground {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}

.cardPattern {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.addCardItem {
  border-radius: $border-radius;
  padding: 20px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  border: 1px dashed #ddd;
}

.addIcon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.addText {
  font-size: 16px;
  color: #999;
}

.buttonContainer {
  margin-top: auto;
  padding: 10px;
}

.confirmButton {
  background-color: $primary-color !important;
  border: none !important;
  border-radius: 30px !important;
  font-size: 16px !important;
  height: 50px !important;
  line-height: 50px !important;
}

