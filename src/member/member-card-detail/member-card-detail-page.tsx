import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';
import memberCardDetailStore from './store';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import styles from './member-card-detail-page.module.scss';

const MemberCardDetailPage = () => {
  const card: any = memberCardDetailStore.card;

  useEffect(() => {
    if (!card || !card.cardType) {
      Taro.navigateBack();
    }
  }, [card]);

  return (
    <PageWithNav showNavBar title="会员卡详情" className={styles.wrapper}>
      <View className={styles.title}>{card.cardType}</View>
      <View className={styles.section}>
        <View className={styles.row}>
          <Text className={styles.label}>积分</Text>
          <Text className={styles.value}>{card.giftIntegral}</Text>
        </View>
        <View className={styles.row}>
          <Text className={styles.label}>会员卡号</Text>
          <Text className={styles.value}>{card.cardNumber}</Text>
        </View>
        <View className={styles.row}>
          <Text className={styles.label}>购买价格</Text>
          <Text className={styles.value}>
            {(card.template?.retailPrice || 0).toFixed(2)}元
          </Text>
        </View>
        {card.endDate && (
          <View className={styles.row}>
            <Text className={styles.label}>到期时间：</Text>
            <Text className={styles.value}>{card.endDate}</Text>
          </View>
        )}
      </View>

      {card.cardType === '折扣卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>余额</Text>
            <Text className={styles.value}>{card.cardBalance}元</Text>
          </View>
          <View className={styles.row}>
            <Text className={styles.label}>折扣</Text>
            <Text className={styles.value}>{card.discountRate}</Text>
          </View>
        </View>
      )}

      {/* {card.cardType === '计时卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>到期时间</Text>
            <Text className={styles.value}>{card.expire || '无字段'}</Text>
          </View>
        </View>
      )} */}

      {card.cardType === '储值卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>余额</Text>
            <Text className={styles.value}>{card.balance}</Text>
          </View>
        </View>
      )}

      {card.cardType === '计数卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>剩余次数</Text>
            <Text className={styles.value}>{card.cardTimes}</Text>
          </View>
        </View>
      )}

      {card.cardType === '套餐卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>剩余次数</Text>
            <Text className={styles.value}>{card.cardTimes}</Text>
          </View>
          <View className={styles.setMenu}>套餐内含有:</View>
          {card.rsetmealList &&
            card.rsetmealList.map((item, index) => (
              <View key={index}>
                <View className={styles.row}>
                  <Text className={styles.label}>{item.item}</Text>
                  <Text className={styles.value}>
                    {item.projectAmount}
                    {item.unit}
                  </Text>
                </View>
              </View>
            ))}
        </View>
      )}

      {card.cardType === '积分卡' && (
        <View className={styles.section}>
          <View className={styles.row}>
            <Text className={styles.label}>积分余额</Text>
            <Text className={styles.value}>{card.giftIntegral}</Text>
          </View>
        </View>
      )}
    </PageWithNav>
  );
};

export default MemberCardDetailPage;
