// 基础样式常量
$primary-color: #FDD244;
$text-color: #333333;
$text-color-light: #666666;
$text-color-lighter: #999999;
$background-color: #f6f6f6;
$white-color: #ffffff;
$border-color: #eeeeee;
$vip-color: #ff6b00;
$positive-color: #3cbc7a;
$negative-color: #f56c6c;

// 字体大小常量
$font-size-small: 12px;
$font-size-normal: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-xlarge: 22px;

// 间距常量
$spacing-small: 8px;
$spacing-medium: 16px;
$spacing-large: 24px;

// 圆角常量
$border-radius-small: 8px;
$border-radius-medium: 12px;
$border-radius-large: 24px;

// 刷新动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotate 1s linear infinite;
}

.container {
  position: relative;
  height: 100vh;
  background-color: $background-color;
}

.header {
  height: 44px;
  background-color: $white-color;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-bottom: 1px solid $border-color;
}

.title {
  font-size: $font-size-large;
  font-weight: 500;
  color: $text-color;
}

.searchBar {
  padding: 12px 16px;
  background-color: $white-color;
  display: flex;
  align-items: center;
  gap: 12px;
}

.searchInputWrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F6F6F6;
  padding: 8px 12px;
  border-radius: 20px;
  position: relative;

  .searchIcon {
    margin-right: 8px;
    color: #999;
  }

  input {
    flex: 1;
    border: none;
    background: none;
    font-size: 14px;
    &::placeholder {
      color: #999;
    }
  }

  .clearIcon {
    position: absolute;
    right: 12px;
    color: #999;
    cursor: pointer;
    transition: opacity 0.2s;
    
    &:hover {
      opacity: 0.7;
    }
  }
}

.searchButton {
  min-width: 80px !important;
  padding: 0 16px !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 18px !important;
  background: $primary-color !important;
  color: $white-color !important;
  font-size: 14px !important;
  border: none !important;
}

.tagContainer {
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $white-color;
  border-bottom: 1px solid $border-color;

  :global {
    .at-tag {
      margin-right: 8px;
      border-radius: 16px;
      padding: 4px 12px;
      border: 1px solid #E5E5E5;
      background: $white-color;
      color: $text-color;
      font-size: 12px;
    }
  }

  .activeTag {
    background-color: $primary-color !important;
    color: $text-color !important;
    border-color: $primary-color !important;
  }
}

.refreshBox {
  padding: 8px 0 8px 16px;
}

.refresh {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 12px;
  border-radius: 20px;
  color: $text-color;
  border: 1px solid $primary-color;
  background-color: $white-color;
}

.refreshImg {
  width: 12px;
  height: 12px;
}

.filterButton {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  background-color: #F6F6F6;
  border-radius: 16px;
  font-size: 14px;
  color: $text-color;
  cursor: pointer;
}

.memberListContainer {
  overflow: hidden;
  position: relative;
  display: flex;
  flex: 1;
  padding: 0 10px;
  // height: calc(100vh - 240px);
}

.alphabetIndex {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  padding-right: 4px;
  font-size: 12px;
}

.memberList {
  flex: 1;
  overflow-y: auto;
}

.memberSection {
  margin-bottom: 8px;

  .sectionTitle {
    padding: 8px 0;
    font-size: 12px;
    color: $text-color-lighter;
  }
}

.memberItem {
  overflow: hidden;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: $white-color;
  border-bottom: 1px solid $border-color;
}

.memberAvatarCard {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.memberAvatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  overflow: hidden;

  .avatarImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.memberInfo {
  flex: 1;
}

.nameRow {
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  .memberName {
    font-size: 16px;
    font-weight: normal;
    color: $text-color;
    margin-right: 4px;
  }
}

.birthday {
  margin-bottom: 4px;

  .memberbirthday {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: $text-color-light;
  }
}

.phoneRow {
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  .memberPhone {
    font-size: 12px;
    color: $text-color-light;
    margin-right: 8px;
  }

  .bottomImg {
    width: 14px;
    height: 14px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.dateRow {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: $text-color-light;

  .createDate {
    color: $negative-color;
  }

  .memberBalance {
    color: $vip-color;
    margin-left: 8px;
  }
}

.actionButtons {
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  margin: 8px 0;
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }

  .bottomImg {
    width: 20px;
    height: 20px;
  }

  .actionText {
    font-size: 12px;
    color: $text-color-light;
  }
}

.memberType {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: $text-color-light;
  margin-top: 4px;

  img {
    margin-right: 4px;
  }

  .memberTypeCard {
    color: $text-color;
    margin-right: 4px;
  }

  .memberStatus {
    color: $negative-color;
    font-size: 12px;
  }
}

.addButtonContainer {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  padding: 16px;
  background-color: $white-color;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
}

.addButton {
  width: 100% !important;
  height: 44px !important;
  line-height: 44px !important;
  border-radius: 22px !important;
  background: $primary-color !important;
  color: $white-color !important;
  font-size: 16px !important;
  border: none !important;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  color: $text-color-lighter;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 筛选面板样式
.filterPanel {
  padding: 16px;
}

.filterSection {
  margin-bottom: 20px;
}

.filterSectionTitle {
  font-size: 14px;
  font-weight: normal;
  color: $text-color;
  margin-bottom: 12px;
}

.filterActions {
  display: flex;
  gap: 12px;
  margin-top: 24px;

  :global {
    .at-button {
      flex: 1;
      height: 40px !important;
      border-radius: 20px !important;
      font-size: 14px !important;

      &--primary {
        background: $primary-color !important;
        color: $text-color !important;
        border: none !important;
      }
    }
  }
}

// 操作面板样式
.actionContent {
  padding: 16px;
}

.actionHeader {
  margin-bottom: 16px;
}

.actionTitle {
  font-size: 16px;
  font-weight: normal;
  color: $text-color;
  margin-bottom: 4px;
}

.actionSubtitle {
  font-size: 12px;
  color: $text-color-light;
}

.actionList {
  .actionItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid $border-color;

    &:last-child {
      border-bottom: none;
    }
  }

  .actionDate {
    font-size: 12px;
    color: $text-color-light;
  }

  .actionDesc {
    flex: 1;
    margin: 0 12px;
    font-size: 14px;
    color: $text-color;
  }

  .actionAmount {
    font-size: 14px;
    font-weight: normal;

    &.actionAmountPositive {
      color: $positive-color;
    }

    &.actionAmountNegative {
      color: $negative-color;
    }
  }
}

.cardInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cardName {
  font-size: 14px;
  color: $text-color;
}

.cardHolder {
  font-size: 12px;
  color: $text-color-light;
}

.cardRelation {
  font-size: 12px;
  color: $text-color-lighter;
}

.cardBalance {
  font-size: 14px;
  color: #1890FF;
}

.addSubCard {
  margin-top: 16px;
  background: $primary-color !important;
  color: $text-color !important;
  border: none !important;
}

/* AtFloatLayout组件样式覆盖 */
:global {
  .at-float-layout {
    .at-float-layout__container {
      border-top-left-radius: 16px !important;
      border-top-right-radius: 16px !important;
    }
    
    .at-float-layout__header {
      padding: 16px !important;
      
      .at-float-layout__title {
        font-size: $font-size-medium !important;
        font-weight: normal !important;
        color: $text-color !important;
      }
    }
  }
  
  .at-button {
    &--small {
      height: 32px !important;
      line-height: 32px !important;
    }
  }
}
