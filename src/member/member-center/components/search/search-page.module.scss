.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 32px calc(env(safe-area-inset-bottom) + 16px) 32px;
	background: #FFFFFF;

	> .empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;

		> .image {
			margin-top: 175px;
			margin-bottom: 8px;
			width: 400px;
			height: 400px;
		}

		> .text {
			color: #9DA3B2;
			font-size: 30px;
		}
	}

	.list {
		display: flex;
		flex-direction: column;

		> .each {
			border-bottom: 1px solid #EFF1F5;
		}

		> .each:last-child {
			border-bottom: 0;
		}
	}
}
