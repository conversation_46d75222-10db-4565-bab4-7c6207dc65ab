import React, { useState } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { ProductListItemType } from '@/model/type/product-list-item.type';

import FrameScrollContainer from '../frame-scroll-container/frame-scroll-container.component';
import SearchBar from '../search-bar/search-bar.component';
import ProductItem from '../search/item/product-item.component';

import CartEmptyImage from '@/assets/image/cart-empty.png';
import styles from './search-page.module.scss';
import { Console } from 'console';

const SearchPage: React.FC = () => {
  const [isEmpty, setIsEmpty] = useState<boolean>(false);
  const [productList, setProductList] = useState<Array<ProductListItemType>>(
    []
  );

  useMount(() => {
    searchMenber('');
  });

  const searchMenber = (e) => {
    service.mem.huiyuanguanli
      .postMemberDetaillist({
        nickName: e,
        businessId: '1',
        pageNum: 1,
        pageSize: 20,
      })
      .then((res: any) => {
        setProductList(res || []);
      });
  };
  const submit = (e: string) => {
    console.log(e);
    searchMenber(e);
  };

  return (
    <View className={styles.container}>
      <SearchBar
        placeholder='根据手机、姓名检索会员'
        onSubmit={submit}
      ></SearchBar>
      {isEmpty ? (
        <View className={styles.empty}>
          <Image className={styles.image} src={CartEmptyImage}></Image>
          <Text className={styles.text}>未找到符合条件的商品</Text>
        </View>
      ) : (
        <FrameScrollContainer>
          <View className={styles.list}>
            {productList.map((each, index) => {
              return (
                <ProductItem
                  className={styles.each}
                  data={each}
                  key={index}
                ></ProductItem>
              );
            })}
          </View>
        </FrameScrollContainer>
      )}
    </View>
  );
};

export default SearchPage;
