import React from 'react';
import Taro from '@tarojs/taro';
import { Image, View, Radio } from '@tarojs/components';
import { ProductListItemType } from '@/model/type/product-list-item.type';
import RadioGroup from '@/view/component/radio-group/radio-group.component';

import styles from './product-item.module.scss';

export type ProductItemPropsType = {
  className?: string;
  data: ProductListItemType;
};

const ProductItem: React.ComponentType = (props) => {
  const { data }: any = props;

  const { nickName, phonenumber, sex, balance, status } = data;

  return (
    <View
      className={`${styles.container}`}
      // onClick={() => Taro.navigateTo({ url: '/view/page/single/housekeeping-detail/housekeeping-detail-page?id=' + props.data.id })}
    >
      {/* <Image className={styles.image} src={props.data.coverImgUrl}></Image> */}
      <View
        className={styles.content}
        // onClick={() => {
        //   Taro.navigateTo({
        //     url:
        //       '/view/page/single/member-detail/member-detail-page?memberId=' +
        //       props.data.memberId,
        //   });
        // }}
      >
        <View className={styles.contentTop}>
          <View className={styles.title}>{nickName}</View>
          <View className={styles.introduction}>{phonenumber}</View>
          <View className={styles.introduction}>{sex}</View>
        </View>

        <View className={styles.bottom}>
          <View className={styles.price}>
            <View className={styles.price1}>
              <View className={styles.value}>{balance}</View>
              <View className={styles.yuan}>元</View>
            </View>
            <View>
              <RadioGroup
                list={[
                  { label: '开启', value: 1 },
                  { label: '关闭', value: 0 },
                ]}
                checkedIndex={status}
                onChange={(e) => {
                console.log(e)
                }}
              ></RadioGroup>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ProductItem;
