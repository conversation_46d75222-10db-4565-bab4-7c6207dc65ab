.container {
    position: relative;
    width: 100%;
    height: 80px;
    overflow: hidden;
    >.bar {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 16px;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background: rgb(245, 246, 250);
        border-radius: 24px;
        transition: 0.35s;
        >.icon {
            margin-left: 24px;
        }
        >.input {
            flex: 1;
            margin-right: 24px;
            font-size: 28px;
        }
    }
    >.button {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 140px;
        height: 80px;
        left: calc(100% + 16px);
        top: 0;
        background: #FF6600;
        color: #FFFFFF;
        font-size: 28px;
        transition: 0.35s;
    }
}