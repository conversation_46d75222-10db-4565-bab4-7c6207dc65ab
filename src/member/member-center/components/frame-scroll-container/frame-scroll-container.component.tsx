import React, { useEffect, useState } from 'react';
import { ScrollView, View } from '@tarojs/components';
import styles from './frame-scroll-container.module.scss';
import Taro from '@tarojs/taro';
import { stat } from 'fs';

type FrameScrollContainerPropsType = {
  className?: string;
  innerClassName?: string;
  background?: string;
  showScrollbar?: boolean;
  children?: any;
};

const FrameScrollContainer: React.ComponentType<
  FrameScrollContainerPropsType
> = (props) => {
  return (
    <View
      className={`${styles.container} ${
        props.className ? props.className : ''
      }`}
    >
      <ScrollView
        className={styles.scroll}
        scrollY
        enhanced
        showScrollbar={props.showScrollbar}
        style={{
          height: Taro.pxTransform(600),
        }}
      >
        <View className={styles.content}>
          <View
            className={`${styles.inner} ${
              props.innerClassName ? props.innerClassName : ''
            }`}
          >
            {props.children}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default FrameScrollContainer;
