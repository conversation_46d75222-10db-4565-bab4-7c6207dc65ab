import { View, Text, Image, Input, Switch, Picker } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtButton, AtIcon, AtList, AtListItem } from 'taro-ui';
import Taro, { useRouter } from '@tarojs/taro';
import Form, { Field, useForm } from 'rc-field-form';
import styles from './renew-member-page.module.scss';
import uploadIcon from '@/assets/image/member/tianjia (2)@2x.png';
import renewMemberStore from './store/renew-member';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';

import { postMemberRecordRenewal } from '@/service/business/huiyuanxiangqingxiaofeimingxi';

const AddMemberPage = () => {
  const router = useRouter();
  const { memberId } = router.params;
  const [form] = useForm();
  const card = renewMemberStore.card as any;

  const [expireDate, setExpireDate] = useState('2025-03-31');
  const [shortMessage, setShortMessage] = useState(true);

  // 处理选择会员卡
  const handleSelectCard = () => {
    Taro.navigateTo({
      url: '/member-card-template/template-list/template-list-page',
    });
  };

  // 处理日期选择
  const handleDateSelect = (type, value) => {
    if (type === 'expireDate') {
      setExpireDate(value);
    }
  };

  // 处理表单提交
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        // console.log('表单数据:', {
        //   ...card,
        //   ...values,
        //   templateId: card.templateId,
        //   cardLevel: card.cardLevel || '1',
        //   operationType: '1',
        // });
        postMemberRecordRenewal({
          ...values,
          templateId: card.templateId,
          memberCardRelationId: card.memberCardRelationId,
          rsetmealList: [card.template],
          operationType: '6',
          memberId,
        })
          .then((res) => {
            console.log('续卡成功:', res);
            Taro.showToast({
              title: '续卡成功',
              icon: 'success',
              duration: 2000,
            });
            setTimeout(() => {
              Taro.navigateBack();
            }, 2000);
          })
          .catch(() => {
            Taro.showToast({
              title: '续卡失败',
              icon: 'none',
              duration: 2000,
            });
          });
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);

        const error = errorInfo.errorFields[0].errors[0];
        Taro.showToast({
          title: error,
          icon: 'none',
          duration: 2000,
        });
      });
  };

  useEffect(() => {
    const card: any = renewMemberStore.card;
    if (card?.templateId) {
      form.setFieldsValue({
        templateId: card.templateId,
      });
    } else {
      Taro.navigateBack();
    }
  }, []);

  return (
    <PageWithNav showNavBar title="会员卡续费" className={styles.container}>
      <Form className={styles.form} form={form}>
        <>
          <View className={styles.card} onClick={handleSelectCard}>
            {card ? (
              <View
                key={card.templateId}
                className={styles.cardItem}
                style={{ backgroundColor: card.color }}
              >
                <View
                  className={styles.cardHeader}
                  style={{ backgroundColor: card.backgroundColor }}
                >
                  <Text className={styles.cardName}>{card.cardType}</Text>
                </View>
                <View className={styles.cardContent}>
                  <Text className={styles.cardDiscount}>
                    享受折扣：{card.cardName}
                  </Text>
                  <Text className={styles.cardProject}>赠送项目：-</Text>
                </View>
                <View className={styles.cardBackground}>
                  <View className={styles.cardPattern}></View>
                </View>
              </View>
            ) : (
              <View className={styles.cardSelect}>
                <Image src={uploadIcon} className={styles.selectIcon} />
                <Text className={styles.selectText}>
                  点击选择要办理的会员卡
                </Text>
              </View>
            )}
            {/* 隐藏的 Field 只用于存储值 */}
            <Field
              name="templateId"
              rules={[{ required: true, message: '请选择会员卡' }]}
            >
              <Input style={{ display: 'none' }} />
            </Field>
          </View>
        </>

        <View className={styles.formContainer}>
          <View className={styles.divider}></View>

          <View className={styles.formItem}>
            <Text className={styles.label}>实收金额</Text>
            <View className={styles.amountContainer}>
              <Field
                name="receivedAmount"
                initialValue={card.template ? card.template.retailPrice : 0}
              >
                <Input
                  className={styles.amountInput}
                  type="digit"
                  placeholderClass={styles.placeholder}
                />
              </Field>
              <Text className={styles.unit}>元</Text>
            </View>
          </View>

          {/* <View className={styles.formItem}>
            <Picker
              mode="date"
              onChange={(e) => handleDateSelect('expireDate', e.detail.value)}
              className={styles.piacker}
            >
              <AtList className={styles.piackerContent}>
                <Text className={styles.label}>添加时间</Text>
                <View className={styles.selectValue}>
                  <Text>{expireDate}</Text>
                  <AtIcon value="chevron-right" size="16" color="#ccc" />
                </View>
              </AtList>
            </Picker>

            <Field name="expireDate" initialValue={expireDate}>
              <Input style={{ display: 'none' }} />
            </Field>
          </View> */}

          <View className={styles.formItem}>
            <View className={styles.shortMessage}>
              <Text className={styles.label}>短信通知</Text>
              <Switch
                color="#ffb400"
                className={styles.switch}
                checked={shortMessage}
                onChange={(e) => {
                  setShortMessage(e.detail.value);
                  form.setFieldsValue({
                    isSmsSubscribe: Number(e.detail.value),
                  });
                }}
              />
            </View>
            <Field name="isSmsSubscribe" initialValue={1}>
              <Input style={{ display: 'none' }} />
            </Field>
          </View>
        </View>

        <View className={styles.submitBtnContainer}>
          <AtButton
            type="primary"
            className={styles.submitBtn}
            formType="submit"
            onClick={handleSubmit}
          >
            确认续卡
          </AtButton>
        </View>
      </Form>
    </PageWithNav>
  );
};

export default AddMemberPage;
