import React, { useState, useEffect } from 'react';
import { View } from '@tarojs/components';

interface FlyingBallProps {
  startPos: { x: number; y: number };
  endPos: { x: number; y: number };
  onAnimationEnd: () => void;
}

const FlyingBall: React.FC<FlyingBallProps> = ({ startPos, endPos, onAnimationEnd }) => {
  const [style, setStyle] = useState({
    left: startPos.x + 'px',
    top: startPos.y + 'px',
    transform: 'translate(-50%, -50%)',
    opacity: 1,
    transition: 'none'
  });

  useEffect(() => {
    // Force a reflow to ensure the initial position is applied
    setTimeout(() => {
      setStyle({
        left: endPos.x + 'px',
        top: endPos.y + 'px',
        transform: 'translate(-50%, -50%) scale(0.5)',
        opacity: 0.7,
        transition: 'all 0.5s cubic-bezier(0.5, -0.5, 1, 1)'
      });
    }, 10);

    const timer = setTimeout(() => {
      onAnimationEnd();
    }, 500);

    return () => clearTimeout(timer);
  }, [endPos, onAnimationEnd]);

  return (
    <View
      className="fixed z-[1001] w-5 h-5 rounded-full bg-[#ffcc00]"
      style={style}
    />
  );
};

export default FlyingBall; 
