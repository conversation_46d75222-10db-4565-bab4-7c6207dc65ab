import React from 'react';
import { View, Text, Image } from '@tarojs/components';
import cartIcon from '@/assets/image/common/cart.png';

interface CartFooterProps {
  totalItems: number;
  totalAmount: number;
  onCheckout: () => void;
  onCartClick: () => void;
  onHoldOrder?: () => void;
  id?: string;
}

const CartFooter: React.FC<CartFooterProps> = ({
  totalItems,
  totalAmount,
  onCheckout,
  onCartClick,
  onHoldOrder,
  id,
}) => {
  // 如果购物车为空，不渲染任何内容
  if (totalItems <= 0) {
    return null;
  }

  return (
    <View
      className="fixed bottom-2 left-0 right-0 h-[88px] bg-white flex items-center px-[24px] shadow-[0_-1px_4px_rgba(0,0,0,0.05)] z-[100]"
      id={id}
    >
      <View
        className="flex items-center cursor-pointer flex-1"
        onClick={onCartClick}
      >
        <View className="relative mr-3">
          <View className="relative">
            <Image src={cartIcon} className="w-[48px] h-[48px]" />
            <View className="absolute -top-[6px] -right-[6px] bg-[#ff6600] text-white text-xs min-w-[24px] h-[24px] rounded-full text-center leading-[24px] flex items-center justify-center">
              <Text className="text-white font-medium">{totalItems}</Text>
            </View>
          </View>
        </View>
        <View className="flex flex-col">
          <Text className="text-base text-[#333]">应付:</Text>
          <Text className="text-xl font-medium text-[#333]">
            ¥{totalAmount.toFixed(2)}
          </Text>
        </View>
      </View>

      <View className="flex items-center">
        {/* {onHoldOrder && (
          <View
            className='w-[60px] h-[28px] rounded-[14px] flex items-center justify-center text-xs text-[#FDD244] border border-[#FDD244] mr-3 cursor-pointer'
            onClick={onHoldOrder}
          >
            <Text>挂单</Text>
          </View>
        )} */}
        <View
          className="w-[60px] h-[28px] rounded-[14px] flex items-center justify-center text-xs text-white shadow-sm bg-[#FDD244] cursor-pointer"
          onClick={onCheckout}
        >
          <Text>下一步</Text>
        </View>
      </View>
    </View>
  );
};

export default CartFooter;
