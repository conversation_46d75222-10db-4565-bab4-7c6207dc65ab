import React, { useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { ITouchEvent } from '@tarojs/components/types/common';
import Taro from '@tarojs/taro';

export interface EmployeeInfo {
  templateEmployeeId: number;
  employeeNickname: string;
  avatarUrl: string;
  employeeTags: string[];
  seq: number;
}

export interface Service {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image?: string;
  count?: number;
  employees?: EmployeeInfo[];
  category?: string;
}

interface ServiceItemProps {
  service: Service;
  onAddToCart: (serviceId: string, startPos?: { x: number; y: number }) => void;
  onRemoveFromCart?: (serviceId: string) => void;
  children?: React.ReactNode;
}

const ServiceItem: React.FC<ServiceItemProps> = ({
  service,
  onAddToCart,
  onRemoveFromCart,
  children
}) => {
  const { id, name, price, image, count = 0 } = service;
  const [employeeError, setEmployeeError] = useState(false);
  const addButtonId = `add-button-${id}`; // Create unique ID for the button

  const handleImageError = () => {
    setEmployeeError(true);
  };

  const handleAddToCart = (e: ITouchEvent) => {
    // Get the position using Taro's query selector
    const query = Taro.createSelectorQuery();
    query
      .select(`#${addButtonId}`)
      .boundingClientRect((rect: any) => {
        if (rect) {
          const startPos = {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
          };
          onAddToCart(id, startPos);
        } else {
          onAddToCart(id);
        }
      })
      .exec();
  };

  return (
    <View className='flex p-4 border-b border-[#f0f0f0] bg-white relative'>
      {image && (
        <View className='mr-4'>
          <Image
            className='w-20 h-20 rounded-lg bg-[#f5f5f5]'
            src={image}
            mode='aspectFill'
            onError={handleImageError}
          />
        </View>
      )}

      <View className='flex-1 flex flex-col justify-between'>
        <Text className='text-base text-[#333] font-normal mb-2'>{name}</Text>

        <View className='flex items-center justify-between'>
          <Text className='text-xl text-[#333] font-semibold'>¥{price}</Text>

          <View className='flex items-center'>
            {count > 0 && onRemoveFromCart && (
              <>
                <View
                  className='flex items-center justify-center w-6 h-6 rounded-full border border-[#FDD244]'
                  onClick={() => onRemoveFromCart(id)}
                >
                  <View className='w-2.5 h-0.5 bg-[#FDD244]'></View>
                </View>

                <Text className='mx-2 text-base'>{count}</Text>
              </>
            )}

            <View
              id={addButtonId}
              className='flex items-center justify-center w-6 h-6 rounded-full bg-[#FDD244]'
              onClick={handleAddToCart}
            >
              <View className='relative w-3 h-3'>
                <View className='absolute left-0 top-[5px] w-3 h-0.5 bg-white'></View>
                <View className='absolute left-[5px] top-0 w-0.5 h-3 bg-white'></View>
              </View>
            </View>
          </View>
        </View>
      </View>

      {children}
    </View>
  );
};

export default ServiceItem; 
