import React, { useState, useEffect } from 'react';
import { View, Text, Image, ScrollView } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { Service } from './ServiceItem';

interface CartPreviewProps {
  visible: boolean;
  cartItems: {
    service: Service;
    count: number;
  }[];
  onClose: () => void;
  onRemoveItem: (serviceId: string) => void;
  onAddItem: (serviceId: string) => void;
  onClearCart: () => void;
  onConfirm: () => void;
  totalAmount: number;
}

const CartPreview: React.FC<CartPreviewProps> = ({
  visible,
  cartItems,
  onClose,
  onRemoveItem,
  onAddItem,
  onClearCart,
  onConfirm,
  totalAmount
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // 处理可见性变化
  useEffect(() => {
    if (visible && cartItems.length > 0) {
      setIsVisible(true);
      setIsAnimating(true);
    } else if (!visible || cartItems.length === 0) {
      setIsAnimating(false);
      // 动画结束后再隐藏组件
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300); // 与CSS过渡时间匹配
      return () => clearTimeout(timer);
    }
  }, [visible, cartItems.length]);

  // 如果不需要显示，则不渲染
  if (!isVisible && !isAnimating) return null;

  // 处理关闭操作
  const handleClose = () => {
    setIsAnimating(false);
    // 延迟调用实际的关闭方法，让动画有时间完成
    setTimeout(() => {
      onClose();
    }, 250);
  };

  // 处理清空购物车操作
  const handleClearCart = () => {
    setIsAnimating(false);
    // 延迟调用实际的清空方法，让动画有时间完成
    setTimeout(() => {
      onClearCart();
    }, 250);
  };

  return (
    <View className='fixed inset-0 z-[1000] flex flex-col'>
      <View
        className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${isAnimating ? 'opacity-100' : 'opacity-0'}`}
        onClick={handleClose}
      />
      <View
        className={`absolute left-0 right-0 bottom-0 bg-white rounded-t-2xl flex flex-col max-h-[70vh] transition-all duration-300 ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-50'
          }`}
      >
        <View className='flex justify-between items-center p-4 border-b border-[#f0f0f0]'>
          <Text className='text-base font-medium text-[#333]'>已选服务</Text>
          <View className='flex items-center py-1.5 px-2.5 bg-[#f5f5f5] rounded-[14px]' onClick={handleClearCart}>
            <AtIcon value='trash' size='16' color='#999' />
            <Text className='text-xs text-[#666] ml-1'>清空</Text>
          </View>
        </View>

        <ScrollView scrollY className='flex-1 overflow-y-auto max-h-[50vh]'>
          {cartItems.map(({ service, count }) => (
            <View key={service.id} className='flex justify-between items-center p-3 px-4 border-b border-[#f5f5f5]'>
              <View className='flex items-center flex-1'>
                {service.image && (
                  <Image className='w-[50px] h-[50px] rounded-md mr-3 bg-[#f0f0f0]' src={service.image} mode='aspectFill' />
                )}
                <View className='flex flex-col'>
                  <Text className='text-sm text-[#333] mb-1'>{service.name}</Text>
                  <Text className='text-base font-medium text-[#ff4d4f]'>¥{service.price}</Text>
                </View>
              </View>
              <View className='flex items-center'>
                <View
                  className='flex items-center justify-center w-7 h-7'
                  onClick={() => onRemoveItem(service.id)}
                >
                  <AtIcon value='subtract-circle' size='20' color='#ffcc00' />
                </View>
                <Text className='min-w-[30px] text-center text-base'>{count}</Text>
                <View
                  className='flex items-center justify-center w-7 h-7'
                  onClick={() => onAddItem(service.id)}
                >
                  <AtIcon value='add-circle' size='20' color='#ffcc00' />
                </View>
              </View>
            </View>
          ))}
        </ScrollView>

        <View className='flex justify-between items-center p-4 border-t border-[#f0f0f0]'>
          <View className='flex items-center'>
            <Text>合计：</Text>
            <Text className='text-lg font-medium text-[#ff4d4f]'>¥{totalAmount.toFixed(2)}</Text>
          </View>
          <View
            className='bg-[#ffcc00] py-2.5 px-5 rounded-[20px] text-[#333] font-medium'
            onClick={() => {
              setIsAnimating(false);
              setTimeout(() => onConfirm(), 250);
            }}
          >
            <Text>确认选择</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default CartPreview; 
