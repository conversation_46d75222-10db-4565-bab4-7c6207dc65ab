import { View, Text } from "@tarojs/components";
import { observer } from "mobx-react";
import cls from "classnames";
import { pinyin } from "pinyin-pro";
import styles from "./index.module.scss";

interface Props {
	onClick?: () => void;
	style?: React.CSSProperties;
	className?: string;
	no?: string;
	name?: string;
	level?: string;

	name_color?: string;
	pinyin_color?: string;
	no_color?: string;
	level_color?: string;
	bg_color?: string;
}

const DEFAULT_PROPS: Props = {
	name_color: "#000000",
	pinyin_color: "#000000",
	no_color: "#000000",
	level_color: "#000000",
	bg_color: "#000000",
};

const MemberCard: React.FC<Props> = observer((props) => {
	const mergedProps = { ...DEFAULT_PROPS, ...props };

	return (
		<View
			className={cls(styles.card, props.className)}
			onClick={mergedProps.onClick}
			style={{
				...mergedProps.style,
				backgroundColor: mergedProps.bg_color,
			}}
		>
			<Text
				className={styles.name}
				style={{ color: mergedProps.name_color }}
			>
				{mergedProps.name}
			</Text>
			<Text
				className={styles.pinyin}
				style={{ color: mergedProps.pinyin_color }}
			>
				{pinyin(mergedProps.name ?? "", { toneType: "none" })}
			</Text>
			<Text className={styles.no} style={{ color: mergedProps.no_color }}>
				No. {mergedProps.no}
			</Text>
			<Text
				className={styles.level}
				style={{ color: mergedProps.level_color }}
			>
				{mergedProps.level}
			</Text>
		</View>
	);
});

export default MemberCard;
