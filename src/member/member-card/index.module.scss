.card {
  position: relative;
  // width: 856px;
  // height: 540px;
  width: 656px;
  height: 390px;
  border-radius: 24px;
  color: white;
  // transform: rotateX(45deg) rotateY(-15deg) rotate(45deg);
  transition: all 0.2s ease;
  overflow: hidden;
  cursor: pointer;

  > .image {
    margin-top: 15px;
    pointer-events: none;
  }

  > .name {
    position: absolute;
    top: 18px;
    left: 18px;
    font-size: 26px;
    font-weight: 800;
  }

  > .pinyin {
    position: absolute;
    font-weight: 700;
    top: 52px;
    left: 18px;
    font-size: 18px;
    font-weight: 600;
    opacity: 0.8;
  }

  > .no {
    position: absolute;
    font-weight: 700;
    bottom: 18px;
    left: 15px;
    font-size: 24px;
    font-weight: 600;
  }

  > .level {
    position: absolute;
    font-weight: 700;
    top: 18px;
    right: 18px;
    font-size: 24px;
  }
}