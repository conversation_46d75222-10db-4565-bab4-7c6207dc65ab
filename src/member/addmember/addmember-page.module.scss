// 基础样式常量
$primary-color: #ffb400;
$text-color: #333333;
$text-color-gray: #666666;
$text-color-light: #ffffff;
$text-color-lighter: #999999;
$background-color: #f5f5f5;
$white-color: #ffffff;
$border-color: #eeeeee;
$required-color: #ff4d4f;

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: $background-color;
  padding: 10px 10px 30px 10px;
  overflow-y: auto;
}

.card {
  background-color: $white-color;
  box-shadow: 0px 1px 7px 0px rgba(0,0,0,0.04);
  border-radius: 8px;
  overflow: hidden;
}

.module {
  padding: 0 15px;
  background-color: $white-color;
  box-shadow: 0px 1px 7px 0px rgba(0,0,0,0.04);
  border-radius: 8px;
  overflow: hidden;;
}

.cardItem {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background-color: $primary-color;
}


.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  background: linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);
  padding: 10px 20px 4px 20px;
}

.cardName {
  font-size: 16px;
  color: $text-color-light;
}

.cardContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 2;
  padding: 0px 20px 20px 20px;
  height: 74px;
}

.cardDiscount, .cardProject {
  font-size: 12px;
  color: $text-color-light;
  opacity: 0.9;
}

.cardBackground {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}

.cardPattern {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.card {
  margin-bottom: 10px;
}

.cardSelect {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  background-color: $white-color;
}

.selectIcon {
  width: 24px;
  height: 24px;
  margin-bottom: 10px;
}

.selectText {
  font-size: 14px;
  color: $text-color-gray;
}

.formItem {
  display: flex;
  align-items: center;
  min-height: 50px;
  border-bottom: 1px solid $border-color;
  position: relative;
}

.formItem:last-child {
  border-bottom: none;
}

.formItemCol{
  display: flex;
  flex-direction: column;
  min-height: 50px;
  padding-top: 10px;
  border-bottom: 1px solid $border-color;
  position: relative;
}

.label {
  width: 90px;
  font-size: 14px;
  color: $text-color;
  flex-shrink: 0;
}

.required {
  color: $required-color;
  margin-left: 2px;
}

.input {
  flex: 1;
  font-size: 14px;
}

.placeholder {
  color: $text-color-lighter;
  font-size: 14px;
}

.selectValue {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  font-size: 14px;
  color: $text-color;
}

.listItem {
  flex: 1;
  padding: 0 !important;
  
  :global {
    .at-list__item-content {
      padding: 0 !important;
    }
  }
}

.remarkBox {
  padding-top: 8px;
  padding-bottom: 12px;
}

.remarkTip {
  font-size: 12px;
  color: $text-color-lighter;
  line-height: 1.5;
}

.divider {
  height: 10px;
  background-color: $background-color;
}

.amountContainer {
  flex: 1;
  display: flex;
  align-items: center;
}

.amountInput {
  flex: 1;
  font-size: 14px;
}

.unit {
  font-size: 14px;
  color: $text-color;
  margin-left: 5px;
}

.switch {
  transform: scale(0.8);
}

.submitBtnContainer {
  padding: 20px 15px;
  // margin-top: 20px;
}

.submitBtn {
  background-color: $primary-color !important;
  border: none !important;
}
.piacker{
 width: 100%; 
}

.piackerContent{
  display: flex;
  align-items: center;
  flex: 1;
}

.shortMessage{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
