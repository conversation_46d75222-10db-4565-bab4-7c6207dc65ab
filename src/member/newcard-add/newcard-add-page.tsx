import { View, Text, Image, Input, Picker } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtButton, AtIcon, AtList } from 'taro-ui';
import Taro, { useRouter } from '@tarojs/taro';
import Form, { Field, useForm } from 'rc-field-form';
import dayjs from 'dayjs';
import styles from './newcard-add-page.module.scss';
import uploadIcon from '@/assets/image/member/tianjia (2)@2x.png';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';

const NewCardAddPage = () => {
  const router = useRouter();
  const memberId = router.params.memberId;
  const [form] = useForm();

  const [expireDate, setExpireDate] = useState(dayjs().format('YYYY-MM-DD'));
  // 会员卡
  const [card, setCard] = useState<any>(null);

  // 处理选择会员卡
  const handleSelectCard = () => {
    Taro.navigateTo({
      url: `/member/select-card/select-card-page?type=addMember`,
    });
  };

  // 处理日期选择
  const handleDateSelect = (type, value) => {
    if (type === 'birthday') {
    } else if (type === 'expireDate') {
      setExpireDate(value);
    }
  };

  // 处理表单提交
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        console.log('表单数据:', {
          ...card,
          ...values,
          templateId: card.templateId,
          cardLevel: card.cardLevel || '1',
          operationType: '1',
          memberId: memberId,
        });
        service.businessRelation.dianpuhuiyuanyuhuiyuankaguanlianguanli
          .postRelation({
            ...card,
            ...values,
            templateId: card.templateId,
            cardLevel: card.cardLevel || '1',
            operationType: '1',
            memberId: memberId,
          })
          .then((res) => {
            console.log('添加会员卡成功:', res);
            Taro.showToast({
              title: '添加会员卡成功',
              icon: 'success',
              duration: 2000,
            });
            setTimeout(() => {
              Taro.navigateBack();
            }, 2000);
          })
          .catch(() => {
            Taro.showToast({
              title: '添加会员卡失败',
              icon: 'none',
              duration: 2000,
            });
          });
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);

        const error = errorInfo.errorFields[0].errors[0];
        Taro.showToast({
          title: error,
          icon: 'none',
          duration: 2000,
        });
      });
  };

  useEffect(() => {
    // 监听会员卡选择
    const handler = (data) => {
      if (data.card) {
        setCard(data.card);
        form.setFieldsValue({
          templateId: data.card.templateId,
          amount: data.card?.retailPrice || 0,
        });
      }
    };
    Taro.eventCenter.on('UPDATE_CARD', handler);

    return () => {
      Taro.eventCenter.off('UPDATE_CARD', handler);
    };
  }, []);

  return (
    <PageWithNav title="添加会员卡">
      <View className={styles.container}>
        <Form form={form}>
          <>
            <View className={styles.card} onClick={handleSelectCard}>
              {card ? (
                <View
                  key={card.templateId}
                  className={styles.cardItem}
                  style={{ backgroundColor: card.color }}
                >
                  <View
                    className={styles.cardHeader}
                    style={{ backgroundColor: card.backgroundColor }}
                  >
                    <Text className={styles.cardName}>{card.cardType}</Text>
                  </View>
                  <View className={styles.cardContent}>
                    <Text className={styles.cardDiscount}>
                      享受折扣：{card.cardName}
                    </Text>
                    <Text className={styles.cardProject}>赠送项目：-</Text>
                  </View>
                  <View className={styles.cardBackground}>
                    <View className={styles.cardPattern}></View>
                  </View>
                </View>
              ) : (
                <View className={styles.cardSelect}>
                  <Image src={uploadIcon} className={styles.selectIcon} />
                  <Text className={styles.selectText}>
                    点击选择要办理的会员卡
                  </Text>
                </View>
              )}
              {/* 隐藏的 Field 只用于存储值 */}
              <Field
                name="templateId"
                rules={[{ required: true, message: '请选择会员卡' }]}
              >
                <Input style={{ display: 'none' }} />
              </Field>
            </View>
          </>

          {/* <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>
                姓名<Text className={styles.required}>*</Text>
              </Text>
              <Field
                name="realName"
                rules={[{ required: true, message: '请输入会员姓名' }]}
              >
                <Input
                  className={styles.input}
                  placeholder="请输入会员姓名"
                  placeholderClass={styles.placeholder}
                />
              </Field>
            </View>

            <View className={styles.formItem}>
              <Text className={styles.label}>
                手机号<Text className={styles.required}>*</Text>
              </Text>
              <Field
                name="phonenumber"
                rules={[
                  { required: true, message: '请输入手机号码' },
                  { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
                ]}
              >
                <Input
                  className={styles.input}
                  placeholder="请输入手机号码"
                  type="number"
                  maxlength={11}
                  placeholderClass={styles.placeholder}
                />
              </Field>
            </View>
          </View>
          <View className={styles.divider}></View>
          <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>昵称</Text>
              <Field name="nickName">
                <Input
                  className={styles.input}
                  placeholder="请输入会员昵称"
                  placeholderClass={styles.placeholder}
                />
              </Field>
            </View>
          </View>
          <View className={styles.divider}></View> */}
          <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>实收金额</Text>
              <View className={styles.amountContainer}>
                <Field name="amount" initialValue={0}>
                  <Input
                    className={styles.amountInput}
                    type="digit"
                    placeholderClass={styles.placeholder}
                  />
                </Field>
                <Text className={styles.unit}>元</Text>
              </View>
            </View>

            <View className={styles.formItem}>
              <Picker
                mode="date"
                onChange={(e) => handleDateSelect('expireDate', e.detail.value)}
                className={styles.piacker}
              >
                <AtList className={styles.piackerContent}>
                  <Text className={styles.label}>添加时间</Text>
                  <View className={styles.selectValue}>
                    <Text>{expireDate}</Text>
                    <AtIcon value="chevron-right" size="16" color="#ccc" />
                  </View>
                </AtList>
              </Picker>

              <Field name="expireDate" initialValue={expireDate}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>
          </View>
        </Form>
      </View>
      <View className={styles.submitBtnContainer}>
        <AtButton
          type="primary"
          className={styles.submitBtn}
          formType="submit"
          onClick={handleSubmit}
        >
          确认添加
        </AtButton>
      </View>
    </PageWithNav>
  );
};

export default NewCardAddPage;
