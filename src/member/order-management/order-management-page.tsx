import React, { useState } from "react";
import { View, Text, Image, ScrollView, Textarea } from "@tarojs/components";
import Taro from "@tarojs/taro";
import PageWithNav from "@/view/component/page-with-nav/page-with-nav.component";
import classNames from "classnames";

interface Employee {
  id: string;
  name: string;
}

interface Service {
  id: string;
  name: string;
  price: number;
  image: string;
}

const OrderManagementPage: React.FC = () => {
  // 当前会员信息
  const [member, setMember] = useState({
    name: "吴雨涵",
    avatar: "https://img.yzcdn.cn/vant/cat.jpeg", // 使用实际可访问的图片
    isVip: true,
    lastVisit: "2025-03-12",
  });

  // 当前服务
  const [currentService, setCurrentService] = useState<Service>({
    id: "1",
    name: "鱼子酱护理不伤发",
    price: 688,
    image: "https://img.yzcdn.cn/vant/cat.jpeg", // 使用实际可访问的图片
  });

  // 折扣信息
  const [discount, setDiscount] = useState({
    type: "会员权益",
    value: "充值8.0折 (储值卡)",
  });

  // 总计金额
  const [totalAmount, setTotalAmount] = useState(550.4);

  // 选择的员工
  const [selectedEmployee, setSelectedEmployee] = useState<Employee>({
    id: "1",
    name: "tony",
  });

  // 指定员工
  const [assignedEmployee, setAssignedEmployee] = useState<Employee>({
    id: "2",
    name: "赵晓凡",
  });

  // 是否指定员工
  const [isAssigned, setIsAssigned] = useState(false);

  // 备注信息
  const [remark, setRemark] = useState("");
  const [remarkCount, setRemarkCount] = useState(0);
  const maxRemarkLength = 200;

  // 挂单处理
  const handleHoldOrder = () => {
    Taro.showToast({
      title: "挂单成功",
      icon: "success",
    });
  };

  // 结账处理
  const handleCheckout = () => {
    Taro.showToast({
      title: "结账成功",
      icon: "success",
    });
  };

  // 更换顾客
  const handleChangeCustomer = () => {
    console.log("更换顾客");
  };

  // 办卡
  const handleApplyCard = () => {
    console.log("办卡");
  };

  // 选择优惠券
  const handleSelectCoupon = () => {
    console.log("选择优惠券");
  };

  // 切换指定员工状态
  const toggleAssignEmployee = () => {
    setIsAssigned(!isAssigned);
  };

  // 处理备注输入
  const handleRemarkChange = (e) => {
    const value = e.detail.value;
    if (value.length <= maxRemarkLength) {
      setRemark(value);
      setRemarkCount(value.length);
    }
  };

  // 会员权益点击
  const handleMemberBenefitClick = () => {
    console.log("会员权益点击");
  };

  // 小计点击
  const handleSubtotalClick = () => {
    console.log("小计点击");
  };

  // 选择员工点击
  const handleSelectEmployeeClick = () => {
    console.log("选择员工点击");
  };

  return (
    <PageWithNav showNavBar title="开单结账">
      <View className="flex flex-col h-screen bg-[#f8f9fa]">
        {/* 会员信息 */}
        <View className="bg-[#222] p-4 rounded-b-lg">
          <View className="flex items-center">
            <View className="w-[60px] h-[60px] mr-4 rounded-full overflow-hidden bg-gray-300">
              <Image className="w-full h-full" src={member.avatar} mode="aspectFill" />
            </View>
            <View className="flex-1">
              <View className="flex items-center">
                <Text className="text-white text-[18px] font-medium mr-2">{member.name}</Text>
                {member.isVip && (
                  <View className="px-2 py-0.5 bg-gradient-to-r from-[#F9AE43] to-[#F9C361] rounded-[3px] text-xs text-white flex items-center">
                    <View className="w-3 h-3 mr-0.5 flex items-center justify-center">
                      <Text className="font-bold transform scale-90 text-white">V</Text>
                    </View>
                    <Text className="font-semibold text-white">VIP</Text>
                  </View>
                )}
              </View>
              <Text className="text-gray-400 text-[14px] mt-1">上次到店 {member.lastVisit}</Text>
            </View>
            <View className="flex space-x-2">
              <View
                className="px-3 py-1.5 bg-[#FFF0D2] rounded-[20px]"
                onClick={handleApplyCard}
              >
                <Text className="text-[#B86914] text-[14px] font-medium">办卡</Text>
              </View>
              <View
                className="px-3 py-1.5 bg-[#FFF0D2] rounded-[20px]"
                onClick={handleChangeCustomer}
              >
                <Text className="text-[#B86914] text-[14px] font-medium">更换顾客</Text>
              </View>
            </View>
          </View>
        </View>

        {/* 服务信息 */}
        <ScrollView scrollY className="flex-1 overflow-auto px-3 py-3">
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View className="flex">
              <View className="w-[60px] h-[60px] mr-3 rounded-md overflow-hidden bg-gray-100">
                <Image className="w-full h-full" src={currentService.image} mode="aspectFill" />
              </View>
              <View className="flex-1">
                <View className="flex flex-col">
                  <Text className="text-base font-medium text-gray-800">{currentService.name}</Text>
                  <View className="flex items-baseline mt-1">
                    <Text className="text-[22px] font-semibold text-gray-900">¥{currentService.price.toFixed(2)}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* 会员权益 */}
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View
              className="flex justify-between items-center"
              onClick={handleMemberBenefitClick}
            >
              <Text className="text-base font-medium text-gray-800">会员权益</Text>
              <View className="flex items-center">
                <Text className="text-sm text-gray-600">{discount.value}</Text>
                <Text className="ml-2 text-gray-400">＞</Text>
              </View>
            </View>
          </View>

          {/* 小计 */}
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View
              className="flex justify-between items-center"
              onClick={handleSubtotalClick}
            >
              <Text className="text-base font-medium text-gray-800">小计</Text>
              <View className="flex items-center">
                <Text className="text-xl font-semibold text-[#FFC23C]">¥ {totalAmount.toFixed(2)}</Text>
                <Text className="ml-2 text-gray-400">＞</Text>
              </View>
            </View>
          </View>

          {/* 选择员工 */}
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View
              className="flex justify-between items-center"
              onClick={handleSelectEmployeeClick}
            >
              <Text className="text-base font-medium text-gray-800">选择员工</Text>
              <View className="flex items-center">
                <Text className="text-sm text-gray-600">{selectedEmployee.name}</Text>
                <Text className="ml-2 text-gray-400">＞</Text>
              </View>
            </View>
          </View>

          {/* 指定 */}
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View className="flex justify-between items-center">
              <View className="flex items-center">
                <Text className="text-base font-medium text-gray-800">{assignedEmployee.name}</Text>
              </View>
              <View
                className={classNames("relative", {
                  "w-12 h-6 rounded-full transition-all duration-300": true,
                  "bg-gray-300": !isAssigned,
                  "bg-[#FFCF53]": isAssigned
                })}
                onClick={toggleAssignEmployee}
              >
                <View
                  className={classNames("absolute top-1 transition-all duration-300", {
                    "w-4 h-4 bg-white rounded-full": true,
                    "left-1": !isAssigned,
                    "left-7": isAssigned
                  })}
                ></View>
                <Text className="absolute right-1 top-0.5 text-xs text-white font-medium">指定</Text>
              </View>
            </View>
          </View>

          {/* 优惠券 */}
          <View className="mb-3 bg-white rounded-lg p-4 shadow-sm">
            <View
              className="flex justify-between items-center"
              onClick={handleSelectCoupon}
            >
              <Text className="text-base font-medium text-gray-800">优惠券</Text>
              <View className="flex items-center">
                <Text className="text-sm text-[#FFC23C]">暂无可用优惠券</Text>
                <Text className="ml-2 text-gray-400">＞</Text>
              </View>
            </View>
          </View>

          {/* 备注 */}
          <View className="mb-24 bg-white rounded-lg p-4 shadow-sm">
            <Text className="text-base font-medium text-gray-800 mb-2">备注</Text>
            <Textarea
              className="w-full min-h-[80px] bg-gray-50 rounded-md p-2 text-sm"
              placeholder="编辑客户信息"
              value={remark}
              onInput={handleRemarkChange}
              maxlength={maxRemarkLength}
            />
            <View className="flex justify-end mt-1">
              <Text className="text-xs text-gray-400">{remarkCount}/{maxRemarkLength}</Text>
            </View>
          </View>
        </ScrollView>

        {/* 底部操作栏 */}
        <View className="fixed bottom-0 left-0 right-0 bg-white p-4 flex items-center justify-between border-t border-gray-100">
          <View className="flex-1">
            <Text className="text-base font-medium">应付: <Text className="text-[#FFC23C]">{totalAmount.toFixed(2)}</Text></Text>
          </View>
          <View className="flex space-x-3">
            <View
              className="px-6 py-2 border border-[#FFC23C] rounded-full"
              onClick={handleHoldOrder}
            >
              <Text className="text-[#FFC23C] font-medium">挂单</Text>
            </View>
            <View
              className="px-6 py-2 bg-gradient-to-r from-[#FFB000] to-[#FFCF53] rounded-full"
              onClick={handleCheckout}
            >
              <Text className="text-white font-medium">结账</Text>
            </View>
          </View>
        </View>
      </View>
    </PageWithNav>
  );
};

export default OrderManagementPage; 
