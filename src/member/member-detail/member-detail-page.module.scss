// 基础样式常量
$primary-color: #ffb400;
$blue-color: #3B5998;
$yellow-color: #FFC107;
$text-color: #333333;
$text-color-light: #666666;
$text-color-lighter: #999999;
$background-color: #f5f5f5;
$white-color: #ffffff;
$border-color: #eeeeee;

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 64px;
  box-sizing: border-box;
  background-color: $background-color;
}

// 会员基本信息卡片
.memberInfoCard {
  background-color: $white-color;
  padding: 20px;
  display: flex;
  position: relative;
}

.memberAvatar {
  width: 62px;
  height: 62px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.avatarImage {
  width: 100%;
  height: 100%;
}

.memberInfo {
  flex: 1;
}

.nameRow {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.memberName {
  font-size: 16px;
  font-weight: 600;
  color: $text-color;
  margin-right: 5px;
}

.memberStatus {
  font-size: 12px;
  color: $text-color-lighter;
}

.birthdayRow {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
  color: $text-color-light;
}

.staffRow {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
  color: $text-color-light;
}

.tagRow {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag {
  background-color: #f0f0f0;
  color: $text-color-light;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.arrowRight {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

// 统计信息
.statsContainer {
  display: flex;
  background-color: $white-color;
  padding: 15px 0;
  padding-top: 0px;
  margin-bottom: 10px;
}

.statItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.statItem:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 1px;
  background-color: $border-color;
}

.statValue {
  font-size: 14px;
  font-weight: 600;
  color: $text-color;
  margin-bottom: 5px;
}

.statLabel {
  font-size: 10px;
  color: $text-color-lighter;
}

// 会员卡信息
.cardContainer {
  margin-top: 10px;
}

.cardTabs {
  display: flex;
  background-color: $white-color;
  padding: 10px 15px;
  border-bottom: 1px solid $border-color;
}

.cardTab {
  flex: 1;
  font-size: 14px;
  color: $text-color;
  margin-right: 20px;
  padding-bottom: 5px;
  text-align: center;
  position: relative;
}

.activeTab {
  // color: $primary-color;
  font-weight: bold;
}

.activeTab::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: $primary-color;
}

.cardDes{
  position: relative;
}

.cardCount {
  font-size: 12px;
  color: $text-color-lighter;
  margin-left: 3px;
  position: absolute;
  top: -8px;
  right: -14px;
}


.scrollList {
  overflow: hidden;
  flex: 1;
}


// 底部按钮
.bottomButton {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex; 
  height: 64px;
}


.icon{
  width: 22px;
  height: 22px;
}

.bottomText{
  font-size: 12px;
}
.openCardButton {
  color: #333333;
  flex: 1;
  background-color:$white-color;
  font-size: 16px;
  text-align: center;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
  
}


.emptyList {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 32px;
  color: #666666;
  font-weight: 400;
  font-size: 14px;

  .empty {
    width: 136px;
    height: 145px;
    margin-bottom: 16px;
  }

  .emptyText {
    color: #666666;
    font-weight: 400;
    font-size: 14px;
  }

  .emptyAddStaffBtn {
    box-sizing: border-box;
    width: 162px;
    height: 36px;
    margin-top: 16px;
    color: #fff;
    line-height: 36px;
    text-align: center;
    background: #fdd244;
    border-radius: 20px;
    cursor: pointer;
  }
}
