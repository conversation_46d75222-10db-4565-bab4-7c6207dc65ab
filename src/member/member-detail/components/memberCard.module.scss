$primary-color: #ffb400;
$blue-color: #3B5998;
$yellow-color: #FFC107;
$text-color: #333333;
$text-color-light: #666666;
$text-color-lighter: #999999;
$background-color: #f5f5f5;
$white-color: #ffffff;
$border-color: #eeeeee;
// 会员卡项
.cardItem {
  margin: 15px;
  border-radius: 12px;
  overflow: hidden;
}

.cardContent {
  padding: 8px  18px;
  color: $white-color;
  position: relative;
}

.cardTitle {
  font-size: 14px;
  font-weight: 600;
}

.cardType{
  position: absolute;
  right: 0px;
  top: 0px;

}

.cardBody{
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  border-bottom: 1px solid rgba(246,246,246,0.28);
  gap:6px;
}

.cardInfoRow {
  flex-grow: 1;
  width: 48%;
  display: flex;
  font-size: 12px;
  margin-bottom: 5px;
  color: rgba(255,255,255,0.74);
}

.cardLabel {
  opacity: 0.8;
  margin-right: 5px;
}

.cardValue {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cardActions {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
}

.cardButton {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: $white-color;
  font-size: 12px;
  padding: 5px 15px;
  border-radius: 15px;
  margin-left: 10px;
}

.setMenu{
 flex-basis: 100%;
}
