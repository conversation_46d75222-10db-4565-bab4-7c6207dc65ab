import { View, Text } from '@tarojs/components';
import styles from './memberCard.module.scss';

const MemberCard = ({
  card,
  handleRenewal,
  handleReturnCard,
  handleCardDetail,
}) => {
  return (
    <View
      key={card.id}
      className={styles.cardItem}
      style={{ backgroundColor: card.color }}
    >
      <View
        className={styles.cardContent}
        onClick={() => handleCardDetail(card)}
      >
        <Text className={styles.cardTitle}>{card.cardName}</Text>
        {/* <Text className={styles.cardType}>{card.cardType}</Text> */}

        <View className={styles.cardBody}>
          <View className={styles.cardInfoRow}>
            <Text className={styles.cardLabel}>积分：</Text>
            <Text className={styles.cardValue}>{card.giftIntegral}</Text>
          </View>

          <View className={styles.cardInfoRow}>
            <Text className={styles.cardLabel}>会员卡号：</Text>
            <Text className={styles.cardValue}>{card.cardNumber}</Text>
          </View>

          {card.endDate && (
            <View className={styles.cardInfoRow}>
              <Text className={styles.cardLabel}>到期时间：</Text>
              <Text className={styles.cardValue}>{card.endDate}</Text>
            </View>
          )}

          <View className={styles.cardInfoRow}>
            <Text className={styles.cardLabel}>购买价格：</Text>
            <Text className={styles.cardValue}>{card.price || 0}元</Text>
          </View>

          {card.cardType === '折扣卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>余额:</Text>
                <Text className={styles.cardValue}>
                  {card.cardBalance || 0}元
                </Text>
              </View>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>折扣：</Text>
                <Text className={styles.cardValue}>{card.discountRate}</Text>
              </View>
            </>
          )}

          {/* {card.cardType === '计时卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>到期时间：</Text>
                <Text className={styles.cardValue}>{card.endDate}</Text>
              </View>
            </>
          )} */}

          {card.cardType === '储值卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>余额：</Text>
                <Text className={styles.cardValue}>{card.balance || 0}</Text>
              </View>
              {/* <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>到期时间：</Text>
                <Text className={styles.cardValue}>{card.endDate}</Text>
              </View> */}
            </>
          )}

          {card.cardType === '计数卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>剩余次数：</Text>
                <Text className={styles.cardValue}>{card.cardTimes || 0}</Text>
              </View>
              {/* <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>到期时间：</Text>
                <Text className={styles.cardValue}>{card.endDate}</Text>
              </View> */}
            </>
          )}

          {card.cardType === '套餐卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>剩余次数：</Text>
                <Text className={styles.cardValue}>{card.cardTimes || 0}</Text>
              </View>
              <View className={styles.setMenu}>套餐内含有:</View>
              {card.rsetmealList.map((item) => {
                return (
                  <View className={styles.cardInfoRow}>
                    <Text className={styles.cardLabel}>{item.item}：</Text>
                    <Text className={styles.cardValue}>
                      {item.projectAmount}
                      {item.unit}
                    </Text>
                  </View>
                );
              })}
            </>
          )}

          {card.cardType === '积分卡' && (
            <>
              <View className={styles.cardInfoRow}>
                <Text className={styles.cardLabel}>积分余额：</Text>
                <Text className={styles.cardValue}>
                  {card.giftIntegral || 0}
                </Text>
              </View>
            </>
          )}
        </View>
      </View>

      <View className={styles.cardActions}>
        {/* <View
        className={styles.cardButton}
        onClick={() => handleReturnCard(card.id)}
      >
        退卡
      </View> */}
        <View className={styles.cardButton} onClick={() => handleRenewal(card)}>
          续卡
        </View>
      </View>
    </View>
  );
};

export default MemberCard;
