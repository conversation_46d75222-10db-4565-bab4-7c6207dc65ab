// 基础样式常量
$text-color: #333333;
$text-color-light: #666666;
$text-color-lighter: #999999;
$primary-color: #B66E00;
$border-color: #eeeeee;
$white-color: #ffffff;

.consumeItem {
  padding: 15px;
  border-bottom: 1px solid $border-color;
  margin-bottom: 10px;
   background-color: $white-color;
}

.serviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.serviceInfo {
  display: flex;
  align-items: center;
}

.serviceIcon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.serviceName {
  font-size: 14px;
  font-weight: 600;
  color: $text-color;
}

.servicePrice {
  font-size: 16px;
  font-weight: 500;
  color: $text-color;
}

.staffcontent{
  margin-left:  32px; 
}

.staffInfo {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: $text-color-lighter;
}

.staffLabel {
  font-size: 14px;
}

.staffName {
  font-size: 14px;
  margin-left: 4px;
}

.staffPosition {
  font-size: 14px;
  margin-left: 8px;
}

.amountInfo {
  display: flex;
  margin-bottom: 4px;
}

.amountLabel {
  font-size: 14px;
  color: $primary-color;
}

.totalAmount {
  font-size: 14px;
  color: $primary-color;
  font-weight: 500;
}

.cardInfo {
  display: flex;
  margin-bottom: 4px;
}

.cardLabel {
  font-size: 12px;
  color: $primary-color;
}

.cardAmount {
  font-size: 12px;
  color: $primary-color;
  font-weight: 500;
}

.timeInfo {
  display: flex;
  margin-top: 8px;
}

.timeLabel {
  font-size: 12px;
  color: $text-color-lighter;
}

.consumeTime {
  font-size: 12px;
  color: $text-color-lighter;
}
