import { View, Text, Image } from '@tarojs/components';
import styles from './consumeList.module.scss';
import serviceIcon from '@/assets/image/member/project.png';

const ConsumeList = ({ item }: any) => {
  return (
    <View key={item.id} className={styles.consumeItem}>
      <View className={styles.serviceHeader}>
        <View className={styles.serviceInfo}>
          <Image src={serviceIcon} className={styles.serviceIcon} />
          <Text className={styles.serviceName}>{item.operationDesc}</Text>
        </View>
        <Text className={styles.servicePrice}>¥{item.amount?.toFixed(2)}</Text>
      </View>
      {item.operRecordBPVoLists?.map((i) => (
        <View className={styles.staffcontent}>
          <View className={styles.serviceHeader}>
            <View className={styles.serviceInfo}>
              <Text className={styles.serviceName}>{i.projectName}</Text>
            </View>
            <Text className={styles.servicePrice}>
              ¥{i.origPrice?.toFixed(2)}
            </Text>
          </View>
        </View>
      ))}
      <View className={styles.staffcontent}>
        <View className={styles.staffInfo}>
          <Text className={styles.staffLabel}>服务员工：</Text>
          <Text className={styles.staffName}>{item.enickName}</Text>
          {item.staffPosition && (
            <Text className={styles.staffPosition}>{item.staffPosition}</Text>
          )}
        </View>
        <Text className={styles.serviceName}>归属：</Text>
        <View className={styles.amountInfo}>
          <Text className={styles.amountLabel}>合计：</Text>
          <Text className={styles.totalAmount}>
            {item.giftamount?.toFixed(2) || 0}元
          </Text>
        </View>

        <View className={styles.cardInfo}>
          <Text className={styles.cardLabel}>储值卡：</Text>
          <Text className={styles.cardAmount}>
            {item.amount?.toFixed(2) || 0}元
          </Text>
        </View>

        <View className={styles.timeInfo}>
          <Text className={styles.timeLabel}>消费时间：</Text>
          <Text className={styles.consumeTime}>{item.operationTime}</Text>
        </View>
      </View>
    </View>
  );
};

export default ConsumeList;
