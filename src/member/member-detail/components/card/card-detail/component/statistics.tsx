import { View, Text, ScrollView } from '@tarojs/components';
import { useState } from 'react';

const Record = ({ memberCardTypeId, memberId }) => {
  const [statis, setStatis] = useState({});
  useMount(() => {
    service.memRecord.huiyuancaozuojiluguanli
      .postRecordMemberlistSummary({
        memberId: memberId,
        memberCardTypeId: memberCardTypeId,
      })
      .then((res) => {
        setStatis(res?.[0] || {});
      });
  });
  return (
    <View style={{display:'flex',flexDirection:'column'}}>
      <Text>本月充值{statis.mmount}</Text>
      <Text>总充值次数{statis.cnt}</Text>
      <Text>总充值金额{statis.mmount}</Text>
    </View>
  );
};

export default Record;
