import { View, Text, <PERSON><PERSON>, ScrollView } from '@tarojs/components';
import { useState } from 'react';
import styles from './component.module.scss'

const Record = ({ memberCardTypeId, memberId }) => {
  const [recordList, setRecordList] = useState([]);
  useMount(() => {
    service.memRecord.huiyuancaozuojiluguanli
      .postRecordDetaillist({
        memberId: memberId,
        memberCardTypeId: memberCardTypeId,
      })
      .then((res) => {
        setRecordList(res);
      });
  });
  return (
    <ScrollView className={styles.record}>
      {recordList.map((item, index) => {
        return (
          <View key={index} className={styles.item}>
            <View className={styles.itemTitle}>{`${item.operationType === '1' ? '充值' : '消费'}${item.amount
              } 元，当前卡内余额${item.opBalance}`}</View>
            <View className={styles.itemDate}>操作时间{item.operationTime}</View>
          </View>
        );
      })}
    </ScrollView>
  );
};

export default Record;
