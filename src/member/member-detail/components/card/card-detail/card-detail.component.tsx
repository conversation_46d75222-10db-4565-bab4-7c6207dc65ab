import { View, Text, ScrollView } from '@tarojs/components';
import { observer } from 'mobx-react';
import { AtButton } from 'taro-ui'
import { useState } from 'react';
import TabBar from '../../tab-bar/tab-bar.component';
import MemberCard from '../../member-card/index';
import styles from './card-detail.module.scss';
import memberDetailStore from '../../../store/member';
import Taro from '@tarojs/taro';
import Record from './component/record';
import Statistics from './component/statistics';

const tabs = ['记录', '统计'];

const CardDetail: React.FC = observer(() => {
  const [currentTabIndex, setCurrentTabIndex] = useState(0);
  const [memberDetail, setMemberDetail]: any = useState({});

  useMount(() => {
    let params = Taro.getCurrentInstance().router.params;

    service.mem.huiyuanguanli
      .getInfo2({ memberId: params.memberId })
      .then((res) => {
        setMemberDetail(res);
        memberDetailStore.setMeberDetail(res);
      });
  });

  const componentEnmu = {
    0: (
      <Record
        memberId={memberDetail.memberId}
        memberCardTypeId={memberDetail.memberCardTypeId}
      />
    ),
    1: (
      <Statistics
        memberId={memberDetail.memberId}
        memberCardTypeId={memberDetail.memberCardTypeId}
      />
    ),
  };

  return (
    <View className={styles.detailPage}>
      <View className={styles.topPage}>
        <MemberCard
          memberDetail={memberDetail}
          className='relative mx-auto -mt-6 overflow-hidden rounded-xl bg-blue-gray-500 bg-clip-border text-white shadow-lg shadow-blue-gray-500/40'
        />

        <TabBar
          tabs={tabs}
          current={currentTabIndex}
          onChange={(e) => {
            console.log(e);
            setCurrentTabIndex(e);
          }}
        ></TabBar>
        <View className={styles.content}>{componentEnmu[currentTabIndex]}</View>
      </View>
      <View className={styles.bottom}>
          <AtButton>卡续费</AtButton>
          <AtButton>划卡消费</AtButton>
      </View>
    </View>
  );
});

export default CardDetail;
