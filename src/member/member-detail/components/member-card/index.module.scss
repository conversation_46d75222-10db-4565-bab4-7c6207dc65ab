.Card {
    position: relative;
    // width: 856px;
    // height: 540px;
    width: 656px;
    height: 390px;
    border-radius: 24px;
    color: white;
    // transform: rotateX(45deg) rotateY(-15deg) rotate(45deg);
    transition: all 0.2s ease;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    >.image {
        margin-top: 15px;
        pointer-events: none;
    }
    >.name {
        top: 18px;
        left: 18px;
        font-size: 26px;
        font-weight: 800;
    }
    >.pinyin {
        font-weight: 700;
        top: 52px;
        left: 18px;
        font-size: 18px;
        font-weight: 600;
        opacity: 0.8;
    }
    >.no {
        font-weight: 700;
        bottom: 18px;
        left: 15px;
        font-size: 24px;
        font-weight: 600;
    }
    >.level {
        font-weight: 700;
        top: 18px;
        right: 18px;
        font-size: 24px;
    }
    >.phone {
        font-weight: 700;
        top: 68px;
        left: 0px;
        font-size: 24px;
    }
    >.sex {
        font-weight: 700;
        top: 88px;
        left: 0px;
        font-size: 24px;
    }
    >.birthday {
        font-weight: 700;
        top: 128px;
        left: 0px;
        font-size: 24px;
    }
    >.headerIimg {
        font-weight: 700;
        top: 168px;
        left: 0px;
        font-size: 24px;
    }
    >.registerTime {
        font-weight: 700;
        top: 208px;
        left: 0px;
        font-size: 24px;
    }
    >.openId {
        font-weight: 700;
        top: 88px;
        left: 0px;
        font-size: 24px;
    }
}