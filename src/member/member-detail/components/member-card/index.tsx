import { View, Text } from '@tarojs/components';
import { observer } from 'mobx-react';
import cls from 'classnames';
import styles from './index.module.scss';
import classNames from 'classnames';

interface Props {
  onClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
  no?: string;
  name?: string;
  level?: string;

  name_color?: string;
  pinyin_color?: string;
  no_color?: string;
  level_color?: string;
  bg_color?: string;
  mergedProps?: object
}

const DEFAULT_PROPS: Props = {
  name_color: '#000000',
  pinyin_color: '#000000',
  no_color: '#000000',
  level_color: '#000000',
  // bg_color: "#000000",
};

const MemberCard: React.FC<Props> = observer((props) => {
  const { birthday, nickName, memberId, cardLevel, balance, registerTime, weixinOpenid, phonenumber }: any = props.memberDetail || {}
  const mergedProps = { ...DEFAULT_PROPS };

  console.log(props)

  return (
    <View
      className={styles.Card}
      style={{
        ...mergedProps.style,
        backgroundColor: mergedProps.bg_color,
      }}
    >
      <Text className={styles.name} style={{ color: mergedProps.name_color }}>
        姓名  {nickName}
      </Text>
      <Text className={styles.no} style={{ color: mergedProps.no_color }}>
        会员卡号 No. {memberId}
      </Text>
      <Text className={styles.level} style={{ color: mergedProps.level_color }}>
        会员等级  {cardLevel}
      </Text>

      <Text className={styles.phone} style={{ color: mergedProps.level_color }}>
        会员手机号  {phonenumber}
      </Text>

      <Text className={styles.sex} style={{ color: mergedProps.level_color }}>
        会员余额 {balance}
      </Text>

      <Text className={styles.sex} style={{ color: mergedProps.level_color }}>
        会员生日 {birthday}
      </Text>

      <Text
        className={styles.registerTime}
        style={{ color: mergedProps.level_color }}
      >
        注册时间 {registerTime}
      </Text>

      <Text
        className={styles.openId}
        style={{ color: mergedProps.level_color }}
      >
        openId {weixinOpenid}
      </Text>
    </View>
  );
});

export default MemberCard;
