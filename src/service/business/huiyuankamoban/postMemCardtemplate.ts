/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /business/mem/cardtemplate */
export async function postMemCardtemplate(
  body: BUSINESS.CardAddParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/mem/cardtemplate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemCardtemplate.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemCardtemplate.traceId = '2776860ea17196637cdd4ae6ed4c84a9';
