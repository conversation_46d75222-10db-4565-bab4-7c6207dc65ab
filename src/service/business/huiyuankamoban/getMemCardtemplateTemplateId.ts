/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /business/mem/cardtemplate/${param0} */
export async function getMemCardtemplateTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemCardtemplateTemplateIdParams,
  options?: { [key: string]: any },
) {
  const { templateId: param0, ...queryParams } = params;
  return request<BUSINESS.RMemberCardTemplateInfoVo>(`/business/mem/cardtemplate/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getMemCardtemplateTemplateId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getMemCardtemplateTemplateId.traceId = 'fc4f0cba30fdcc185e4789e7539315fd';
