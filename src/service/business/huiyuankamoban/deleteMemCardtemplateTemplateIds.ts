/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 DELETE /business/mem/cardtemplate/${param0} */
export async function deleteMemCardtemplateTemplateIds(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.deleteMemCardtemplateTemplateIdsParams,
  options?: { [key: string]: any },
) {
  const { templateIds: param0, ...queryParams } = params;
  return request<BUSINESS.RBoolean>(`/business/mem/cardtemplate/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteMemCardtemplateTemplateIds.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteMemCardtemplateTemplateIds.traceId = '762fa14d730081ebe1a8fd5fc232b146';
