/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 PUT /business/mem/cardtemplate */
export async function putMemCardtemplate(
  body: BUSINESS.CardEditPara,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/mem/cardtemplate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putMemCardtemplate.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putMemCardtemplate.traceId = 'b08c8749416548b298bf93e6fbe3843f';
