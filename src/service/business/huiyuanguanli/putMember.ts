/* eslint-disable */
import request from '@/helper/request';

/** 修改会员详细信息列表 修改会员详细信息列表 PUT /business/member */
export async function putMember(body: BUSINESS.TMember, options?: { [key: string]: any }) {
  return request<BUSINESS.RBoolean>('/business/member', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putMember.traceId = 'd0530f09ec0c1325446924b7b62a8588';
