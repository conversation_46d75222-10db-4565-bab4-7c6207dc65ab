/* eslint-disable */
import request from '@/helper/request';

/** 获取会员详细信息列表 获取会员详细信息列表 GET /business/member/${param0} */
export async function getMemberMemberId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemberMemberIdParams,
  options?: { [key: string]: any },
) {
  const { memberId: param0, ...queryParams } = params;
  return request<BUSINESS.RTMember>(`/business/member/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getMemberMemberId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getMemberMemberId.traceId = '47e97d335a2a35835ca9fdf5a95bdeed';
