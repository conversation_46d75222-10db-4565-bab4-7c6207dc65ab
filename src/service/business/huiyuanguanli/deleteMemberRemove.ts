/* eslint-disable */
import request from '@/helper/request';

/** 删除会员详细信息列表 删除会员详细信息列表 DELETE /business/member/remove */
export async function deleteMemberRemove(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.deleteMemberRemoveParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/remove', {
    method: 'DELETE',
    headers: {
      traceId: deleteMemberRemove.traceId,
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
deleteMemberRemove.traceId = 'cb5175eff9ecf6e71b32a7b9b1f1a20f';
