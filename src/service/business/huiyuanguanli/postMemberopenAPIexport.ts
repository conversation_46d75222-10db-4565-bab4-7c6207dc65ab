/* eslint-disable */
import request from '@/helper/request';

/** 导出会员列表 导出会员列表 POST /business/member/export */
export async function postMemberopenAPIexport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.postMember_openAPI_exportParams,
  options?: { [key: string]: any },
) {
  return request<any>('/business/member/export', {
    method: 'POST',
    headers: {
      traceId: postMemberopenAPIexport.traceId,
    },
    params: {
      ...params,
      tMember: undefined,
      ...params['tMember'],
    },
    ...(options || {}),
  });
}
postMemberopenAPIexport.traceId = 'c8f204fd5d1ff6892f9d4cef672d15f9';
