/* eslint-disable */
import request from '@/helper/request';

/** 当前用户绑定的所有的会员卡信息 当前用户绑定的所有的会员卡信息 POST /business/member/cardDetailList */
export async function postMemberCardDetailList(
  body: BUSINESS.TMember,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListTMember>('/business/member/cardDetailList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberCardDetailList.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberCardDetailList.traceId = 'f77bf4250f0294e5844ba7a505b7c897';
