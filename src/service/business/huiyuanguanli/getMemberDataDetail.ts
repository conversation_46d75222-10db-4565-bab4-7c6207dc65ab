/* eslint-disable */
import request from '@/helper/request';

/** 当前用户的明细信息 当前用户的明细信息 GET /business/member/dataDetail */
export async function getMemberDataDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemberDataDetailParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RTMemberDataDetailVo>('/business/member/dataDetail', {
    method: 'GET',
    headers: {
      traceId: getMemberDataDetail.traceId,
    },
    params: {
      ...params,
      detailVo: undefined,
      ...params['detailVo'],
    },
    ...(options || {}),
  });
}
getMemberDataDetail.traceId = '3e02e545ab0f96a3f443861d87e623e7';
