/* eslint-disable */
import request from '@/helper/request';

/** 会员管理-录入会员：新增会员详细信息列表 会员管理-录入会员：新增会员详细信息列表 POST /business/member */
export async function postMember(body: BUSINESS.TMemberVo, options?: { [key: string]: any }) {
  return request<BUSINESS.RBoolean>('/business/member', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMember.traceId = '337621b1eee8a502a2de98a9303fd39d';
