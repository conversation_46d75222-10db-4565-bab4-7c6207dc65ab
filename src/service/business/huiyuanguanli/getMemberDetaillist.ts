/* eslint-disable */
import request from '@/helper/request';

/** 支持昵称、手机号查询会员列表 支持昵称、手机号查询会员列表 GET /business/member/detaillist */
export async function getMemberDetaillist(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemberDetaillistParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RMapObjectObject>('/business/member/detaillist', {
    method: 'GET',
    headers: {
      traceId: getMemberDetaillist.traceId,
    },
    params: {
      ...params,
      detailVo: undefined,
      ...params['detailVo'],
    },
    ...(options || {}),
  });
}
getMemberDetaillist.traceId = '09e96562ee1e7779eeaa5b368f57a16e';
