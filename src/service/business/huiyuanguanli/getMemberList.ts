/* eslint-disable */
import request from '@/helper/request';

/** 列表 查询会员列表 GET /business/member/list */
export async function getMemberList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemberListParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListTMember>('/business/member/list', {
    method: 'GET',
    headers: {
      traceId: getMemberList.traceId,
    },
    params: {
      ...params,
      tMember: undefined,
      ...params['tMember'],
    },
    ...(options || {}),
  });
}
getMemberList.traceId = 'cfe0148d4e903f1b642d64db80ede147';
