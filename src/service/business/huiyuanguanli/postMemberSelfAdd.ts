/* eslint-disable */
import request from '@/helper/request';

/** 新增会员详细信息列表 新增会员详细信息列表 POST /business/member/selfAdd */
export async function postMemberSelfAdd(
  body: BUSINESS.TMemberVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/selfAdd', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberSelfAdd.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberSelfAdd.traceId = '1ab54b72f3dab0637cd339cfd6f3b5a3';
