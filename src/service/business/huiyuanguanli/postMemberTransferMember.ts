/* eslint-disable */
import request from '@/helper/request';

/** 散客转会员 散客转会员 POST /business/member/transferMember */
export async function postMemberTransferMember(
  body: BUSINESS.TMemberVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/transferMember', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberTransferMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberTransferMember.traceId = '84dcf7994457b8d1f44a3c6c1ec0c429';
