/* eslint-disable */
import request from '@/helper/request';

/** 消息推送 消息推送 POST /business/common/msg/send */
export async function postCommonMsgSend(
  body: BUSINESS.MsgSendParamVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/common/msg/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postCommonMsgSend.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postCommonMsgSend.traceId = 'f84a81609b4f92bacd131bde848750b9';
