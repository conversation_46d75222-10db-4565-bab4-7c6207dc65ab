/* eslint-disable */
import request from '@/helper/request';

/** 编辑 编辑 PUT /business/shop */
export async function putShop(
  body: {
    param?: BUSINESS.BusinessParamVo;
  },
  logo?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (logo) {
    formData.append('logo', logo);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RBoolean>('/business/shop', {
    method: 'PUT',
    headers: {
      traceId: putShop.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
putShop.traceId = 'ed7c7c04f217658ba6a4e8e9cadb3529';
