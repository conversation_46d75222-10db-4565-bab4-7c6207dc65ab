/* eslint-disable */
import request from '@/helper/request';

/** 修改商家资质信息 修改商家资质信息 PUT /business/shop/license */
export async function putShopLicense(
  body: {
    param?: BUSINESS.BusinessLicense;
  },
  licenseImg?: File,
  legalPersonIdentifierFrontImg?: File,
  legalPersonIdentifierBackImg?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (licenseImg) {
    formData.append('licenseImg', licenseImg);
  }

  if (legalPersonIdentifierFrontImg) {
    formData.append('legalPersonIdentifierFrontImg', legalPersonIdentifierFrontImg);
  }

  if (legalPersonIdentifierBackImg) {
    formData.append('legalPersonIdentifierBackImg', legalPersonIdentifierBackImg);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RBoolean>('/business/shop/license', {
    method: 'PUT',
    headers: {
      traceId: putShopLicense.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
putShopLicense.traceId = '85aada7670449213686ef2a8e4f0d43b';
