/* eslint-disable */
import request from '@/helper/request';

/** 上传商家头图、相册文件 上传商家头图、相册文件 POST /business/shop/file/upload */
export async function postShopFileUpload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.postShopFileUploadParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBusinessFile>('/business/shop/file/upload', {
    method: 'POST',
    headers: {
      traceId: postShopFileUpload.traceId,
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
postShopFileUpload.traceId = 'fa49958ed06aba43db370d3ee963d272';
