/* eslint-disable */
import request from '@/helper/request';

/** 新增商家资质信息 新增商家资质信息 POST /business/shop/license */
export async function postShopLicense(
  body: {
    param?: BUSINESS.BusinessLicense;
  },
  licenseImg?: File,
  legalPersonIdentifierFrontImg?: File,
  legalPersonIdentifierBackImg?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (licenseImg) {
    formData.append('licenseImg', licenseImg);
  }

  if (legalPersonIdentifierFrontImg) {
    formData.append('legalPersonIdentifierFrontImg', legalPersonIdentifierFrontImg);
  }

  if (legalPersonIdentifierBackImg) {
    formData.append('legalPersonIdentifierBackImg', legalPersonIdentifierBackImg);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RLong>('/business/shop/license', {
    method: 'POST',
    headers: {
      traceId: postShopLicense.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
postShopLicense.traceId = '63e1eb2fa2d4cc74066770b09620c99c';
