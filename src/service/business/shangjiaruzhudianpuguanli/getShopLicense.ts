/* eslint-disable */
import request from '@/helper/request';

/** 根据商家ID查询商家资质信息信息 根据商家ID查询商家资质信息信息 GET /business/shop/license */
export async function getShopLicense(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getShopLicenseParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBusinessLicense>('/business/shop/license', {
    method: 'GET',
    headers: {
      traceId: getShopLicense.traceId,
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
getShopLicense.traceId = 'a3ea8dc85fb123d5e7422c6cf98b433b';
