/* eslint-disable */
import request from '@/helper/request';

/** 添加商家员工信息 新增员工，同步将该员工信息注册为用户 POST /business/employee */
export async function postEmployee(
  body: {
    employee?: BUSINESS.BusinessEmployeeVO;
  },
  avatar?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (avatar) {
    formData.append('avatar', avatar);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RBoolean>('/business/employee', {
    method: 'POST',
    headers: {
      traceId: postEmployee.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
postEmployee.traceId = '43cda275270afc2fd2647a5887fb03ef';
