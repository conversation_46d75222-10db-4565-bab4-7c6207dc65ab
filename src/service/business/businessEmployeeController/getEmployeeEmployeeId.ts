/* eslint-disable */
import request from '@/helper/request';

/** 根据id获取商家员工信息 根据员工id获取商家员工信息，包含员工对应的用户信息 GET /business/employee/${param0} */
export async function getEmployeeEmployeeId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getEmployeeEmployeeIdParams,
  options?: { [key: string]: any },
) {
  const { employeeId: param0, ...queryParams } = params;
  return request<BUSINESS.RBusinessEmployeeInfoVo>(`/business/employee/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getEmployeeEmployeeId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getEmployeeEmployeeId.traceId = '83058f77e2cd790ef7420ed023d83993';
