/* eslint-disable */
import request from '@/helper/request';

/** 根据条件获取商家员工列表 返回符合条件商家所有员工的列表 GET /business/employee/list */
export async function getEmployeeList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getEmployeeListParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RMapObjectObject>('/business/employee/list', {
    method: 'GET',
    headers: {
      traceId: getEmployeeList.traceId,
    },
    params: {
      ...params,
      businessEmployee: undefined,
      ...params['businessEmployee'],
    },
    ...(options || {}),
  });
}
getEmployeeList.traceId = 'a4d6db13bcceac8f80f8f85f44f520bd';
