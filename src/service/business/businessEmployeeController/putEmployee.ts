/* eslint-disable */
import request from '@/helper/request';

/** 修改商家员工信息 修改员工信息 PUT /business/employee */
export async function putEmployee(
  body: {
    employee?: BUSINESS.BusinessEmployeeVO;
  },
  avatar?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (avatar) {
    formData.append('avatar', avatar);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RBoolean>('/business/employee', {
    method: 'PUT',
    headers: {
      traceId: putEmployee.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
putEmployee.traceId = '251ab70376a736981a5cad79f983d5fa';
