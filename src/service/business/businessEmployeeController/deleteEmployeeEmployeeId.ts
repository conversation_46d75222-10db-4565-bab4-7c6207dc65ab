/* eslint-disable */
import request from '@/helper/request';

/** 删除商家员工 删除员工 DELETE /business/employee/${param0} */
export async function deleteEmployeeEmployeeId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.deleteEmployeeEmployeeIdParams,
  options?: { [key: string]: any },
) {
  const { employeeId: param0, ...queryParams } = params;
  return request<BUSINESS.RBoolean>(`/business/employee/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteEmployeeEmployeeId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteEmployeeEmployeeId.traceId = '3b3593e50289db2776f9bff2588676c6';
