/* eslint-disable */
import request from '@/helper/request';

/** 新增预约模板 新增预约模板 POST /business/appointment/template */
export async function postAppointmentTemplate(
  body: BUSINESS.AppointmentTemplate,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/appointment/template', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postAppointmentTemplate.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postAppointmentTemplate.traceId = '2d646c6dff81fdedd3247fc64020bbdb';
