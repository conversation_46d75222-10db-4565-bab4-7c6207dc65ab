/* eslint-disable */
import request from '@/helper/request';

/** 获取模板详情 包含模板关联的员工与项目列表 GET /business/appointment/template/${param0} */
export async function getAppointmentTemplateTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getAppointmentTemplateTemplateIdParams,
  options?: { [key: string]: any },
) {
  const { templateId: param0, ...queryParams } = params;
  return request<BUSINESS.RAppointmentTemplate>(`/business/appointment/template/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getAppointmentTemplateTemplateId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getAppointmentTemplateTemplateId.traceId = 'bd9202323a0b23aefaff56a90a8105db';
