/* eslint-disable */
import request from '@/helper/request';

/** 更新预约模板 更新预约模板信息 PUT /business/appointment/template */
export async function putAppointmentTemplate(
  body: BUSINESS.AppointmentTemplate,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/appointment/template', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putAppointmentTemplate.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putAppointmentTemplate.traceId = 'af3f160cc91372861d056e7fb58e6bcb';
