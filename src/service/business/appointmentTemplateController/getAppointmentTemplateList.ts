/* eslint-disable */
import request from '@/helper/request';

/** 获取商家预约模板列表 根据请求头里的businessId查询 GET /business/appointment/template/list */
export async function getAppointmentTemplateList(options?: { [key: string]: any }) {
  return request<BUSINESS.RMapObjectObject>('/business/appointment/template/list', {
    method: 'GET',
    headers: {
      traceId: getAppointmentTemplateList.traceId,
    },
    ...(options || {}),
  });
}
getAppointmentTemplateList.traceId = 'f84a7cb0222a2e751e0d96bb31dc4e77';
