/* eslint-disable */
import request from '@/helper/request';

/** 删除预约模板 逻辑删除，但查询接口不返回 DELETE /business/appointment/template/${param0} */
export async function deleteAppointmentTemplateTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.deleteAppointmentTemplateTemplateIdParams,
  options?: { [key: string]: any },
) {
  const { templateId: param0, ...queryParams } = params;
  return request<BUSINESS.RBoolean>(`/business/appointment/template/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteAppointmentTemplateTemplateId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteAppointmentTemplateTemplateId.traceId = '91a57898c87666ce32befdb26a32c034';
