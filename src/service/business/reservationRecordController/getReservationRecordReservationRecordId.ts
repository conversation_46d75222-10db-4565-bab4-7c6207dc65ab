/* eslint-disable */
import request from '@/helper/request';

/** 预约记录明细 预约记录明细 GET /business/reservation-record/${param0} */
export async function getReservationRecordReservationRecordId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getReservationRecordReservationRecordIdParams,
  options?: { [key: string]: any },
) {
  const { reservationRecordId: param0, ...queryParams } = params;
  return request<BUSINESS.RReservationRecordInfoVo>(`/business/reservation-record/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getReservationRecordReservationRecordId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getReservationRecordReservationRecordId.traceId = 'f4f139509d9596f58deb93cc2a4f94c6';
