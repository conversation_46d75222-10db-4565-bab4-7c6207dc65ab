/* eslint-disable */
import request from '@/helper/request';

/** 预约记录列表 预约记录列表 GET /business/reservation-record/list */
export async function getReservationRecordList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getReservationRecordListParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListReservationRecordListVo>('/business/reservation-record/list', {
    method: 'GET',
    headers: {
      traceId: getReservationRecordList.traceId,
    },
    params: {
      ...params,
      param: undefined,
      ...params['param'],
    },
    ...(options || {}),
  });
}
getReservationRecordList.traceId = 'e955f2e97a38b03d1b524b496998bfd0';
