/* eslint-disable */
import request from '@/helper/request';

/** 编辑预约记录 编辑预约记录 PUT /business/reservation-record */
export async function putReservationRecord(
  body: BUSINESS.RecordEditParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/reservation-record', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putReservationRecord.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putReservationRecord.traceId = 'e4d6ca9d66729e7998ea97dce682db0e';
