/* eslint-disable */
import request from '@/helper/request';

/** 新建预约记录 新建预约记录 POST /business/reservation-record */
export async function postReservationRecord(
  body: BUSINESS.RecordAddParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/reservation-record', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postReservationRecord.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postReservationRecord.traceId = 'bacfa2bdcea29b73d4afb8b0cb7808fd';
