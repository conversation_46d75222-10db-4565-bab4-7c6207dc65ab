/* eslint-disable */
import request from '@/helper/request';

/** 修改预约时间 修改预约时间 POST /business/reservation-record/edit-reservation-date */
export async function postReservationRecordEditReservationDate(
  body: BUSINESS.RecordDateEditParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/reservation-record/edit-reservation-date', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postReservationRecordEditReservationDate.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postReservationRecordEditReservationDate.traceId = 'cdeba1291a4825dfdd82b41bb16839b7';
