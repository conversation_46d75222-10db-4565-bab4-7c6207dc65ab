/* eslint-disable */
import request from '@/helper/request';

/** 接单 接单 POST /business/reservation-record/accept */
export async function postReservationRecordAccept(
  body: BUSINESS.RecordStatusEditParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/reservation-record/accept', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postReservationRecordAccept.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postReservationRecordAccept.traceId = '65ed1064910463793eec9294906e3758';
