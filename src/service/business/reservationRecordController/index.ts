/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import { getReservationRecordList } from './getReservationRecordList';
import { getReservationRecordReservationRecordId } from './getReservationRecordReservationRecordId';
import { postReservationRecord } from './postReservationRecord';
import { postReservationRecordAccept } from './postReservationRecordAccept';
import { postReservationRecordEditReservationDate } from './postReservationRecordEditReservationDate';
import { postReservationRecordGetMemberType } from './postReservationRecordGetMemberType';
import { postReservationRecordList } from './postReservationRecordList';
import { putReservationRecord } from './putReservationRecord';
export {
  getReservationRecordList,
  getReservationRecordReservationRecordId,
  postReservationRecord,
  postReservationRecordAccept,
  postReservationRecordEditReservationDate,
  postReservationRecordGetMemberType,
  postReservationRecordList,
  putReservationRecord,
};
