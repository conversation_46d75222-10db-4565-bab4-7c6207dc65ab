/* eslint-disable */
import request from '@/helper/request';

/** 预约记录列表 预约记录列表 POST /business/reservation-record/list */
export async function postReservationRecordList(
  body: BUSINESS.RecordListParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListReservationRecordListVo>('/business/reservation-record/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postReservationRecordList.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postReservationRecordList.traceId = 'e12415bc2ad4cdb330c87458bc2b38de';
