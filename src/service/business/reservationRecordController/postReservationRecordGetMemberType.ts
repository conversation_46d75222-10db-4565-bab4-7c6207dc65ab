/* eslint-disable */
import request from '@/helper/request';

/** 查询顾客类型 根据手机号查询顾客是散客还是会员 POST /business/reservation-record/get-member-type */
export async function postReservationRecordGetMemberType(
  body: BUSINESS.MemberTypeGetParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RMemberTypeEnum>('/business/reservation-record/get-member-type', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postReservationRecordGetMemberType.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postReservationRecordGetMemberType.traceId = '5a98f2647155882237179e9896768dee';
