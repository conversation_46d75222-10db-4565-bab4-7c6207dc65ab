/* eslint-disable */
import request from '@/helper/request';

/** 编辑客资 编辑客资 PUT /business/customer-resource */
export async function putCustomerResource(
  body: BUSINESS.CustomerResourceEditParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/customer-resource', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putCustomerResource.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putCustomerResource.traceId = '9807a7129dd55bb1410798f266c32180';
