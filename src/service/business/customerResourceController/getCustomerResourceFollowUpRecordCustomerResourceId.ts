/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /business/customer-resource/follow-up-record/${param0} */
export async function getCustomerResourceFollowUpRecordCustomerResourceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getCustomerResourceFollowUpRecordCustomerResourceIdParams,
  options?: { [key: string]: any },
) {
  const { customerResourceId: param0, ...queryParams } = params;
  return request<BUSINESS.RListCustomerResourceFollowUpRecordVo>(
    `/business/customer-resource/follow-up-record/${param0}`,
    {
      method: 'GET',
      headers: {
        traceId: getCustomerResourceFollowUpRecordCustomerResourceId.traceId,
      },
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}
getCustomerResourceFollowUpRecordCustomerResourceId.traceId = '956f226e27b29f7a3b0fc3fc3be52aa3';
