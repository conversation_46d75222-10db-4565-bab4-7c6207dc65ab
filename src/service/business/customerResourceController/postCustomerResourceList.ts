/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /business/customer-resource/list */
export async function postCustomerResourceList(
  body: BUSINESS.CustomerResourceListParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListCustomerResourceListVo>('/business/customer-resource/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postCustomerResourceList.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postCustomerResourceList.traceId = 'a727f7dea47bc7e2390909e686a86c54';
