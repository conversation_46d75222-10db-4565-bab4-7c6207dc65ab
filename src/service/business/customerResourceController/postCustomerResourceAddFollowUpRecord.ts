/* eslint-disable */
import request from '@/helper/request';

/** 新增跟进记录 新增跟进记录 POST /business/customer-resource/add-follow-up-record */
export async function postCustomerResourceAddFollowUpRecord(
  body: BUSINESS.FollowUpRecordAddParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/customer-resource/add-follow-up-record', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postCustomerResourceAddFollowUpRecord.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postCustomerResourceAddFollowUpRecord.traceId = 'd1627150190c9b8cbecc4ae9aaabd3d6';
