/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /business/customer-resource/${param0} */
export async function getCustomerResourceCustomerResourceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getCustomerResourceCustomerResourceIdParams,
  options?: { [key: string]: any },
) {
  const { customerResourceId: param0, ...queryParams } = params;
  return request<BUSINESS.RCustomerResourceInfoVo>(`/business/customer-resource/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getCustomerResourceCustomerResourceId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getCustomerResourceCustomerResourceId.traceId = 'b4ac6fffcc7c105d9429600237e24437';
