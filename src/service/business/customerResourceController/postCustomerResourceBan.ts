/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /business/customer-resource/ban */
export async function postCustomerResourceBan(
  body: BUSINESS.BanParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/customer-resource/ban', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postCustomerResourceBan.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postCustomerResourceBan.traceId = 'cfc739383a052111fb67cd2129b31cce';
