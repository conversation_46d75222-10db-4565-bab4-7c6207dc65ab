/* eslint-disable */
import request from '@/helper/request';

/** 客资详情-获取基本信息 客资详情-获取基本信息 GET /business/customer-resource/basic/${param0} */
export async function getCustomerResourceBasicCustomerResourceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getCustomerResourceBasicCustomerResourceIdParams,
  options?: { [key: string]: any },
) {
  const { customerResourceId: param0, ...queryParams } = params;
  return request<BUSINESS.RCustomerResourceBasicInfoVo>(
    `/business/customer-resource/basic/${param0}`,
    {
      method: 'GET',
      headers: {
        traceId: getCustomerResourceBasicCustomerResourceId.traceId,
      },
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}
getCustomerResourceBasicCustomerResourceId.traceId = '701191f22c3e6bde206aec9eb25d2f98';
