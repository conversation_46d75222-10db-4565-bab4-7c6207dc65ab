/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /business/reservation-template/edit */
export async function postReservationTemplateEdit(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.postReservationTemplateEditParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/reservation-template/edit', {
    method: 'POST',
    headers: {
      traceId: postReservationTemplateEdit.traceId,
    },
    params: {
      ...params,
      param: undefined,
      ...params['param'],
    },
    ...(options || {}),
  });
}
postReservationTemplateEdit.traceId = 'b20f24b7b3ad6f5b295d0bbad47daaee';
