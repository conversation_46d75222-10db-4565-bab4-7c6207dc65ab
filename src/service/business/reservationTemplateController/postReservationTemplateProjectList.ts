/* eslint-disable */
import request from '@/helper/request';

/** 查询项目列表 查询项目列表 POST /business/reservation-template/project-list */
export async function postReservationTemplateProjectList(
  body: BUSINESS.TemplateProjectListParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListTemplateProjectListVo>(
    '/business/reservation-template/project-list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        traceId: postReservationTemplateProjectList.traceId,
      },
      data: body,
      ...(options || {}),
    },
  );
}
postReservationTemplateProjectList.traceId = '6eb2b7a0650d4962a1e9cf8d0c93a62b';
