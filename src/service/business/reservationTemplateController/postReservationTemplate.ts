/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /business/reservation-template */
export async function postReservationTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.postReservationTemplateParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RLong>('/business/reservation-template', {
    method: 'POST',
    headers: {
      traceId: postReservationTemplate.traceId,
    },
    params: {
      ...params,
      param: undefined,
      ...params['param'],
    },
    ...(options || {}),
  });
}
postReservationTemplate.traceId = 'c62942d387ee611827dab402a417b7e6';
