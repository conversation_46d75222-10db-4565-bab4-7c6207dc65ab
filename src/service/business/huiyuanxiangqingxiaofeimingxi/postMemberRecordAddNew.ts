/* eslint-disable */
import request from '@/helper/request';

/** 会员卡-开单 会员卡-开单 POST /business/member/record/addNew */
export async function postMemberRecordAddNew(
  body: BUSINESS.TMemberOperRecordPerAddVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/record/addNew', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberRecordAddNew.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberRecordAddNew.traceId = '22ea730e955b6ad533b886f10856c601';
