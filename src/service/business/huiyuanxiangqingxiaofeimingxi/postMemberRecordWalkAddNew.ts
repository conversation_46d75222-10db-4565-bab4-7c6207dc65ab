/* eslint-disable */
import request from '@/helper/request';

/** 散客-开单 散客-开单 POST /business/member/record/walkAddNew */
export async function postMemberRecordWalkAddNew(
  body: BUSINESS.TMemberOperRecordPerAddVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/record/walkAddNew', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberRecordWalkAddNew.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberRecordWalkAddNew.traceId = '1bf72124ed26d6df4b4c1ca015ac6c0e';
