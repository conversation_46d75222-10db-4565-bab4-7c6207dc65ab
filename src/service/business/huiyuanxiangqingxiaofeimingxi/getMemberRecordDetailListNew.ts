/* eslint-disable */
import request from '@/helper/request';

/** 会员详情-消费明细-查询会员操作记录列表 会员详情-消费明细-查询会员操作记录列表 GET /business/member/record/detailListNew */
export async function getMemberRecordDetailListNew(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getMemberRecordDetailListNewParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RMapObjectObject>('/business/member/record/detailListNew', {
    method: 'GET',
    headers: {
      traceId: getMemberRecordDetailListNew.traceId,
    },
    params: {
      ...params,
      detailVo: undefined,
      ...params['detailVo'],
    },
    ...(options || {}),
  });
}
getMemberRecordDetailListNew.traceId = '********************************';
