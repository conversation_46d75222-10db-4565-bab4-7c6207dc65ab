/* eslint-disable */
import request from '@/helper/request';

/** 会员卡-续卡 会员卡-续卡 POST /business/member/record/renewal */
export async function postMemberRecordRenewal(
  body: BUSINESS.TMemberRenewalVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/member/record/renewal', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemberRecordRenewal.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemberRecordRenewal.traceId = 'e1365e6c0c1645b569ddfacfe7709334';
