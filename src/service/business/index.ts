/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as appointmentRecordController from './appointmentRecordController';
import * as appointmentTemplateController from './appointmentTemplateController';
import * as businessController from './businessController';
import * as businessEmployeeController from './businessEmployeeController';
import * as customerResourceController from './customerResourceController';
import * as dashboardController from './dashboardController';
import * as dianpuhuiyuanyuhuiyuankaguanlianguanli from './dianpuhuiyuanyuhuiyuankaguanlianguanli';
import * as gonggongguanli from './gonggongguanli';
import * as huiyuanguanli from './huiyuanguanli';
import * as huiyuankamoban from './huiyuankamoban';
import * as huiyuanxiangqingxiaofeimingxi from './huiyuanxiangqingxiaofeimingxi';
import * as reservationRecordController from './reservationRecordController';
import * as reservationTemplateController from './reservationTemplateController';
import * as shangjiaruzhudianpuguanli from './shangjiaruzhudianpuguanli';
import * as shangjiaxiangmuguanli from './shangjiaxiangmuguanli';
import * as walkInBillController from './walkInBillController';
export {
  appointmentRecordController,
  appointmentTemplateController,
  businessController,
  businessEmployeeController,
  customerResourceController,
  dashboardController,
  dianpuhuiyuanyuhuiyuankaguanlianguanli,
  gonggongguanli,
  huiyuanguanli,
  huiyuankamoban,
  huiyuanxiangqingxiaofeimingxi,
  reservationRecordController,
  reservationTemplateController,
  shangjiaruzhudianpuguanli,
  shangjiaxiangmuguanli,
  walkInBillController,
};
