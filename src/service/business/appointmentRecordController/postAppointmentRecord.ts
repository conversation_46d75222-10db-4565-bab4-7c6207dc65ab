/* eslint-disable */
import request from '@/helper/request';

/** 新增预约记录 新增预约记录 POST /business/appointment/record */
export async function postAppointmentRecord(
  body: BUSINESS.AppointmentRecord,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/appointment/record', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postAppointmentRecord.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postAppointmentRecord.traceId = '2d4dbb5eef2ca72ee3ba59b33e6bee2a';
