/* eslint-disable */
import request from '@/helper/request';

/** 逻辑删除预约记录 删除后将查询不到 DELETE /business/appointment/record/${param0} */
export async function deleteAppointmentRecordRecordId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.deleteAppointmentRecordRecordIdParams,
  options?: { [key: string]: any },
) {
  const { recordId: param0, ...queryParams } = params;
  return request<BUSINESS.RBoolean>(`/business/appointment/record/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteAppointmentRecordRecordId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteAppointmentRecordRecordId.traceId = '51d428f3e49b664cedbc3a9a7877964a';
