/* eslint-disable */
import request from '@/helper/request';

/** 得到预约记录详情 根据预约记录id查询 GET /business/appointment/record/${param0} */
export async function getAppointmentRecordRecordId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getAppointmentRecordRecordIdParams,
  options?: { [key: string]: any },
) {
  const { recordId: param0, ...queryParams } = params;
  return request<BUSINESS.RAppointmentRecord>(`/business/appointment/record/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getAppointmentRecordRecordId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getAppointmentRecordRecordId.traceId = '0b805ce9f5a5913878e531d74ce2f20f';
