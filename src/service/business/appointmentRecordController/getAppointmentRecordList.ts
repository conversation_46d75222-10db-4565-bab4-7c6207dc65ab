/* eslint-disable */
import request from '@/helper/request';

/** 查询商家预约记录 消息头中带上businessId,查询指定商家的预约记录 GET /business/appointment/record/list */
export async function getAppointmentRecordList(options?: { [key: string]: any }) {
  return request<BUSINESS.RMapObjectObject>('/business/appointment/record/list', {
    method: 'GET',
    headers: {
      traceId: getAppointmentRecordList.traceId,
    },
    ...(options || {}),
  });
}
getAppointmentRecordList.traceId = '443eb36eb5eacded0fe9c1e2034ed853';
