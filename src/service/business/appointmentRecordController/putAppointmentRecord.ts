/* eslint-disable */
import request from '@/helper/request';

/** 编辑预约记录 编辑预约记录信息 PUT /business/appointment/record */
export async function putAppointmentRecord(
  body: BUSINESS.AppointmentRecord,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RBoolean>('/business/appointment/record', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putAppointmentRecord.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putAppointmentRecord.traceId = 'd714278b291f0da07b5ab4be3de675e3';
