/* eslint-disable */
import request from '@/helper/request';

/** 入驻(免鉴权) 入驻(免鉴权) POST /business/shop/register */
export async function postShopRegister(
  body: BUSINESS.BusinessAddParam,
  logo?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (logo) {
    formData.append('logo', logo);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      formData.append(
        ele,
        typeof item === 'object' && !(item instanceof File) ? JSON.stringify(item) : item,
      );
    }
  });

  return request<BUSINESS.RBusinessAddVO>('/business/shop/register', {
    method: 'POST',
    headers: {
      traceId: postShopRegister.traceId,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
postShopRegister.traceId = '71c5f9020da7119dd450b6ce874d62af';
