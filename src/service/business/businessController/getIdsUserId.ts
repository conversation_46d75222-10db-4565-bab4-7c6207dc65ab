/* eslint-disable */
import request from '@/helper/request';

/** 查询用户绑定店铺id 查询用户绑定店铺id GET /business/shop/getBusinessIds/${param0} */
export async function getIdsUserId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params?: BUSINESS.getIdsUserIdParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListObject>(`/business/shop/getBusinessIds`, {
    method: 'GET',
    headers: {
      traceId: getIdsUserId.traceId,
    },
    params: { ...params },
    ...(options || {}),
  });
}
getIdsUserId.traceId = '8a66d6aef24469a056e4b3338e3df18a';
