/* eslint-disable */
import request from '@/helper/request';

/** 今日数据-客户流量 今日数据-客户流量 GET /business/dashboard/traffic */
export async function getDashboardTraffic(options?: { [key: string]: any }) {
  return request<BUSINESS.RTrafficVo>('/business/dashboard/traffic', {
    method: 'GET',
    headers: {
      traceId: getDashboardTraffic.traceId,
    },
    ...(options || {}),
  });
}
getDashboardTraffic.traceId = '51fde6bbbc88cbbf01859cd70b9a6cbd';
