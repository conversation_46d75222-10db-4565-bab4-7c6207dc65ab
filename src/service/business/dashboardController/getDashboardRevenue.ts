/* eslint-disable */
import request from '@/helper/request';

/** 今日数据-商家收入 今日数据-商家收入 GET /business/dashboard/revenue */
export async function getDashboardRevenue(options?: { [key: string]: any }) {
  return request<BUSINESS.RRevenueVo>('/business/dashboard/revenue', {
    method: 'GET',
    headers: {
      traceId: getDashboardRevenue.traceId,
    },
    ...(options || {}),
  });
}
getDashboardRevenue.traceId = '51ea4888b62914c4fa9cfe86f77fa2c8';
