/* eslint-disable */
import request from '@/helper/request';

/** 获取项目列表(不分页) 获取项目列表(不分页) POST /business/shop-project/all */
export async function postShopProjectAll(
  body: BUSINESS.ProjectAllParam,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RListBusinessProject>('/business/shop-project/all', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postShopProjectAll.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postShopProjectAll.traceId = 'ea26c5fccc9c56af9e66eb5d39cb2e96';
