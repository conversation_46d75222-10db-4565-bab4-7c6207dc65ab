/* eslint-disable */
import request from '@/helper/request';

/** 获取项目类目列表(类目列表只读) 获取项目类目列表(类目列表只读) GET /business/shop-project/all-category */
export async function getShopProjectAllCategory(options?: { [key: string]: any }) {
  return request<BUSINESS.RListCategoryListVo>('/business/shop-project/all-category', {
    method: 'GET',
    headers: {
      traceId: getShopProjectAllCategory.traceId,
    },
    ...(options || {}),
  });
}
getShopProjectAllCategory.traceId = 'a0628e4a5f930426746aedec85834182';
