/* eslint-disable */
import request from '@/helper/request';

/** 根据会员卡查询会员信息 根据会员卡ID查询会员信息 GET /business/relation/get-member */
export async function getRelationGetMember(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESS.getRelationGetMemberParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RMemberVo>('/business/relation/get-member', {
    method: 'GET',
    headers: {
      traceId: getRelationGetMember.traceId,
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
getRelationGetMember.traceId = '0a0ee0b1b6caa33fa047fb0e2d56b755';
