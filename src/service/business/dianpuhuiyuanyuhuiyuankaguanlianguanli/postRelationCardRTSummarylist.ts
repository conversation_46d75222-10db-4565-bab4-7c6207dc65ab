/* eslint-disable */
import request from '@/helper/request';

/** 查询店铺会员与会员卡类型统计清单 根据条件查询店铺会员与会员卡关联列表 POST /business/relation/cardRTSummarylist */
export async function postRelationCardRTSummarylist(
  body: BUSINESS.TMemberCardRelationResultVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESS.RTableDataInfo>('/business/relation/cardRTSummarylist', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRelationCardRTSummarylist.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRelationCardRTSummarylist.traceId = 'ddfc2596ffd0d08412c20da6202ee6ac';
