/* eslint-disable */
import request from '@/helper/request';

/** 新增散客账单 新增散客账单 POST /business/walkIn/bill */
export async function postWalkInBill(body: BUSINESS.WalkInBill, options?: { [key: string]: any }) {
  return request<BUSINESS.RBoolean>('/business/walkIn/bill', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postWalkInBill.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postWalkInBill.traceId = '54ace4e3dba4bbb8aa1571948e558bba';
