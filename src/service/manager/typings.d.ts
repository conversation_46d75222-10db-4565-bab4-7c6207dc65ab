/* eslint-disable */
declare namespace MANAGER {
  type AjaxResult = {
    error?: boolean;
    success?: boolean;
    warn?: boolean;
    empty?: boolean;
  };

  type deleteConfigConfigIdsParams = {
    configIds: number[];
  };

  type deleteDeptDeptIdParams = {
    deptId: number;
  };

  type deleteDictDataDictCodesParams = {
    dictCodes: number[];
  };

  type deleteDictTypeDictIdsParams = {
    dictIds: number[];
  };

  type deleteLogininforInfoIdsParams = {
    infoIds: number[];
  };

  type deleteMenuMenuIdParams = {
    menuId: number;
  };

  type deleteNoticeNoticeIdsParams = {
    noticeIds: number[];
  };

  type deleteOnlineTokenIdParams = {
    tokenId: string;
  };

  type deleteOperlogOperIdsParams = {
    operIds: number[];
  };

  type deletePostPostIdsParams = {
    postIds: number[];
  };

  type deleteRoleRoleIdsParams = {
    roleIds: number[];
  };

  type deleteUserUserIdsParams = {
    userIds: number[];
  };

  type getConfigConfigIdParams = {
    configId: number;
  };

  type getConfigConfigKeyConfigKeyParams = {
    configKey: string;
  };

  type getConfigListParams = {
    config: MANAGER.SysConfig;
  };

  type getDeptDeptIdParams = {
    deptId: number;
  };

  type getDeptListExcludeDeptIdParams = {
    deptId: number;
  };

  type getDeptListParams = {
    dept: MANAGER.SysDept;
  };

  type getDictDataDictCodeParams = {
    dictCode: number;
  };

  type getDictDataListParams = {
    dictData: MANAGER.SysDictData;
  };

  type getDictDataTypeDictTypeParams = {
    dictType: string;
  };

  type getDictTypeDictIdParams = {
    dictId: number;
  };

  type getDictTypeListParams = {
    dictType: MANAGER.SysDictType;
  };

  type getLogininforListParams = {
    logininfor: MANAGER.SysLogininfor;
  };

  type getLogininforUnlockUserNameParams = {
    userName: string;
  };

  type getMenuListParams = {
    menu: MANAGER.SysMenu;
  };

  type getMenuMenuIdParams = {
    menuId: number;
  };

  type getMenuRoleMenuTreeselectRoleIdParams = {
    roleId: number;
  };

  type getMenuTreeselectParams = {
    menu: MANAGER.SysMenu;
  };

  type getNoticeListParams = {
    notice: MANAGER.SysNotice;
  };

  type getNoticeNoticeIdParams = {
    noticeId: number;
  };

  type getOnlineListParams = {
    ipaddr: string;
    userName: string;
  };

  type getOperlogListParams = {
    operLog: MANAGER.SysOperLog;
  };

  type getPostListParams = {
    post: MANAGER.SysPost;
  };

  type getPostPostIdParams = {
    postId: number;
  };

  type getRoleAuthUserAllocatedListParams = {
    user: MANAGER.SysUser;
  };

  type getRoleAuthUserUnallocatedListParams = {
    user: MANAGER.SysUser;
  };

  type getRoleDeptTreeRoleIdParams = {
    roleId: number;
  };

  type getRoleListParams = {
    role: MANAGER.SysRole;
  };

  type getRoleRoleIdParams = {
    roleId: number;
  };

  type getUserAuthRoleUserIdParams = {
    userId: number;
  };

  type getUserDeptTreeParams = {
    dept: MANAGER.SysDept;
  };

  type getUserInfoUsernameParams = {
    username: string;
  };

  type getUserListParams = {
    user: MANAGER.SysUser;
  };

  type getUserUserIdParams = {
    userId: number;
  };

  type LoginUser = {
    token?: string;
    userid?: number;
    username?: string;
    loginTime?: number;
    expireTime?: number;
    ipaddr?: string;
    permissions?: string[];
    roles?: string[];
    sysUser?: MANAGER.SysUser;
  };

  type postConfig_openAPI_exportParams = {
    config: MANAGER.SysConfig;
  };

  type postDictData_openAPI_exportParams = {
    dictData: MANAGER.SysDictData;
  };

  type postDictType_openAPI_exportParams = {
    dictType: MANAGER.SysDictType;
  };

  type postLogininfor_openAPI_exportParams = {
    logininfor: MANAGER.SysLogininfor;
  };

  type postOperlog_openAPI_exportParams = {
    operLog: MANAGER.SysOperLog;
  };

  type postPost_openAPI_exportParams = {
    post: MANAGER.SysPost;
  };

  type postRole_openAPI_exportParams = {
    role: MANAGER.SysRole;
  };

  type postUser_openAPI_exportParams = {
    user: MANAGER.SysUser;
  };

  type postUserImportDataParams = {
    file: string;
    updateSupport: boolean;
  };

  type putRoleAuthUserCancelAllParams = {
    roleId: number;
    userIds: number[];
  };

  type putRoleAuthUserSelectAllParams = {
    roleId: number;
    userIds: number[];
  };

  type putUserAuthRoleParams = {
    userId: number;
    roleIds: number[];
  };

  type RBoolean = {
    code?: number;
    msg?: string;
    data?: boolean;
  };

  type RInteger = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RLoginUser = {
    code?: number;
    msg?: string;
    data?: MANAGER.LoginUser;
  };

  type RLong = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type SysConfig = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    configId?: number;
    configName: string;
    configKey: string;
    configValue: string;
    configType?: string;
  };

  type SysDept = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    deptId?: number;
    parentId?: number;
    ancestors?: string;
    deptName: string;
    orderNum: number;
    leader?: string;
    phone?: string;
    email?: string;
    status?: string;
    delFlag?: string;
    parentName?: string;
    children?: MANAGER.SysDept[];
  };

  type SysDictData = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    dictCode?: number;
    dictSort?: number;
    dictLabel: string;
    dictValue: string;
    dictType: string;
    cssClass?: string;
    listClass?: string;
    isDefault?: string;
    status?: string;
    default?: boolean;
  };

  type SysDictType = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    dictId?: number;
    dictName: string;
    dictType: string;
    status?: string;
  };

  type SysLogininfor = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    infoId?: number;
    userName?: string;
    status?: string;
    ipaddr?: string;
    msg?: string;
    accessTime?: string;
  };

  type SysMenu = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    menuId?: number;
    menuName: string;
    parentName?: string;
    parentId?: number;
    orderNum: number;
    path?: string;
    component?: string;
    query?: string;
    routeName?: string;
    isFrame?: string;
    isCache?: string;
    menuType: string;
    visible?: string;
    status?: string;
    perms?: string;
    icon?: string;
    children?: MANAGER.SysMenu[];
  };

  type SysNotice = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    noticeId?: number;
    noticeTitle: string;
    noticeType?: string;
    noticeContent?: string;
    status?: string;
  };

  type SysOperLog = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    operId?: number;
    title?: string;
    businessType?: number;
    businessTypes?: number[];
    method?: string;
    requestMethod?: string;
    operatorType?: number;
    operName?: string;
    deptName?: string;
    operUrl?: string;
    operIp?: string;
    operParam?: string;
    jsonResult?: string;
    status?: number;
    errorMsg?: string;
    operTime?: string;
    costTime?: number;
  };

  type SysPost = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    postId?: number;
    postCode: string;
    postName: string;
    postSort: number;
    status?: string;
    flag?: boolean;
  };

  type SysRole = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    roleId?: number;
    roleName: string;
    roleKey: string;
    roleSort: number;
    dataScope?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
    status?: string;
    delFlag?: string;
    flag?: boolean;
    menuIds?: number[];
    deptIds?: number[];
    permissions?: string[];
    admin?: boolean;
  };

  type SysUser = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    userId?: number;
    deptId?: number;
    userName: string;
    nickName?: string;
    realName?: string;
    userType?: string;
    weixinOpenid?: string;
    sessionKey?: string;
    riskLevel?: number;
    email?: string;
    phonenumber?: string;
    sex?: string;
    avatar?: string;
    password?: string;
    status?: string;
    delFlag?: string;
    loginIp?: string;
    loginDate?: string;
    dept?: MANAGER.SysDept;
    roles?: MANAGER.SysRole[];
    roleIds?: number[];
    postIds?: number[];
    roleId?: number;
    admin?: boolean;
  };

  type SysUserRole = {
    userId?: number;
    roleId?: number;
  };

  type TableDataInfo = {
    total?: number;
    data?: Record<string, any>[];
    code?: number;
    msg?: string;
  };
}
