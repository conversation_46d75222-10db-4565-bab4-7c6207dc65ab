/* eslint-disable */
import request from '@/helper/request';

/** 会员详情-会员卡-查询指定店铺指定卡种下各会员卡清单 会员详情-会员卡-查询指定店铺指定卡种下各会员卡清单 GET /business/relation/cardRTMemberList */
export async function getRelationCardRTMemberList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESSRELATION.getRelationCardRTMemberListParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RMapObjectObject>('/business/relation/cardRTMemberList', {
    method: 'GET',
    headers: {
      traceId: getRelationCardRTMemberList.traceId,
    },
    params: {
      ...params,
      resultVo: undefined,
      ...params['resultVo'],
    },
    ...(options || {}),
  });
}
getRelationCardRTMemberList.traceId = '03113e8b2b147325810f08fbd7057af2';
