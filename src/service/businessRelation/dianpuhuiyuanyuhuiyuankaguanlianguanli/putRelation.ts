/* eslint-disable */
import request from '@/helper/request';

/** 修改店铺会员与会员卡关联,修改会员卡 修改一条店铺会员与会员卡关联记录,修改会员卡 PUT /business/relation */
export async function putRelation(
  body: BUSINESSRELATION.TMemberCardRelationVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RBoolean>('/business/relation', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putRelation.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putRelation.traceId = 'd4e445a91828eb081b8e6abc72e3c39c';
