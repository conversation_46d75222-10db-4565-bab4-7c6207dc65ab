/* eslint-disable */
import request from '@/helper/request';

/** 查询指定店铺指定卡种下各会员卡清单 查询指定店铺指定卡种下各会员卡清单 POST /business/relation/cardRTMemberList */
export async function postRelationCardRTMemberList(
  body: BUSINESSRELATION.TMemberCardRelationResultVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RTableDataInfo>('/business/relation/cardRTMemberList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRelationCardRTMemberList.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRelationCardRTMemberList.traceId = 'd0c63f8d126998964e1780695567611a';
