/* eslint-disable */
import request from '@/helper/request';

/** 新增店铺会员与会员卡关联 新增一条店铺会员与会员卡关联记录 POST /business/relation */
export async function postRelation(
  body: BUSINESSRELATION.TMemberCardRelation,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RBoolean>('/business/relation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRelation.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRelation.traceId = '57a883820a87cb98cd5dcded41f9a61d';
