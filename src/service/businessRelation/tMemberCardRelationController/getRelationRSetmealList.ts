/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /business/relation/rSetmealList */
export async function getRelationRSetmealList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BUSINESSRELATION.getRelationRSetmealListParams,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RListTMemberCardRelation>('/business/relation/rSetmealList', {
    method: 'GET',
    headers: {
      traceId: getRelationRSetmealList.traceId,
    },
    params: {
      ...params,
      tMemberCardRelation: undefined,
      ...params['tMemberCardRelation'],
    },
    ...(options || {}),
  });
}
getRelationRSetmealList.traceId = '1e73dc56bc047a552e46a2c1113becd4';
