/* eslint-disable */
import request from '@/helper/request';

/** 查询店铺会员与会员卡类型统计清单 根据条件查询店铺会员与会员卡关联列表 POST /business/relation/cardRTSummarylist */
export async function postRelationCardRTSummarylist(
  body: BUSINESSRELATION.TMemberCardRelationResultVo,
  options?: { [key: string]: any },
) {
  return request<BUSINESSRELATION.RListTMemberCardRelationResultVo>(
    '/business/relation/cardRTSummarylist',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        traceId: postRelationCardRTSummarylist.traceId,
      },
      data: body,
      ...(options || {}),
    },
  );
}
postRelationCardRTSummarylist.traceId = '91874adccbad0970451c5fbd7bf4baf3';
