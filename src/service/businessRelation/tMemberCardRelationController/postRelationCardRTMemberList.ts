/* eslint-disable */
import request from '@/helper/request';

/** 查询指定店铺指定卡种下各会员卡清单 查询指定店铺指定卡种下各会员卡清单 POST /business/relation/cardRTMemberList */
export async function postRelationCardRTMemberList(
  params: BUSINESSRELATION.TMemberCardRelationResultVo,
  options?: { [key: string]: any }
) {
  return request<BUSINESSRELATION.RListTMemberCardRelation>(
    '/business/relation/cardRTMemberList',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        traceId: postRelationCardRTMemberList.traceId,
      },
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
postRelationCardRTMemberList.traceId = '04cf3b8572c4fb86b8ded0619214d0ab';
