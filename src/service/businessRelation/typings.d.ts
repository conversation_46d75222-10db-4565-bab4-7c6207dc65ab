/* eslint-disable */
declare namespace BUSINESSRELATION {
  type AppointmentRecord = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    recordId?: number;
    templateId?: number;
    employeeId?: number;
    businessId?: number;
    arriveNum?: number;
    appointmentTime?: string;
    status?: number;
    customerRemarks?: string;
    merchantRemarks?: string;
    employee?: BUSINESSRELATION.BusinessEmployee;
  };

  type AppointmentTemplate = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 预约模板主键 */
    templateId?: number;
    templateName?: string;
    /** 预约模板所属商家ID */
    businessId?: number;
    /** 接单模式 */
    takeOrdersMode: string;
    /** 预约间隔时长 */
    intervalDuration?: string;
    /** 是否需要收集预约时间 */
    appointmentTimeSwitch?: boolean;
    /** 是否需要收集预约人数 */
    appointmentPeopleNumSwitch?: boolean;
    /** 是否需要收集备注 */
    remarkSwitch?: boolean;
    /** 预约次数 */
    appointmentNum?: number;
    /** 同时最多可接待人数 */
    meanwhileReceptionNum?: number;
    /** 状态 */
    status?: number;
    /** 预约通知手机号 */
    notifyPhone: string;
    /** 是否开启微信通知 */
    wxNotifySwitch?: boolean;
    /** 是否开启短信通知 */
    noteNotifySwitch?: boolean;
    /** 每周可预约的日期 */
    weekCanAppointmentDate?: string;
    /** 每天可预约的时间段 */
    dayCanAppointmentTime?: string;
    /** 项目列表 */
    projectList?: BUSINESSRELATION.BusinessProject[];
    /** 员工列表 */
    employeeList?: BUSINESSRELATION.BusinessEmployee[];
  };

  type BanParam = {
    /** id（客资记录id） */
    id: number;
    /** 拉黑原因 */
    banReason?: string;
    /** 拉黑备注 */
    banRemark?: string;
  };

  type BusinessAddVo = {
    /** token */
    token?: string;
    /** 用户id */
    userId?: number;
    /** 商家id */
    businessId?: number;
    /** 二维码地址 */
    qrcodeUrl?: string;
  };

  type BusinessEmployee = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 员工id */
    employeeId?: number;
    /** 员工对应sys_user的id */
    userId?: number;
    /** 商家id */
    businessId?: number;
    /** 员工昵称 */
    employeeNickname?: string;
    /** 员工工作电话 */
    employeePhone?: string;
    /** 员工头像 */
    avatarUrl?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 上班：ON_DUTY  下班：OFF_DUTY */
    onDutyStatus?: string;
    /** 职位 */
    position?: string;
    /** 职级：高级、中级、初级 */
    rank?: string;
    /** 入职时间 */
    hiredate?: string;
    /** 班次名称：白班 DAY_SHIFT、夜班 NIGHT_SHIFT */
    shiftName?: string;
    /** 是否全天班次：1是，0否 */
    shiftWholeDay?: string;
    /** 班次开始时间 */
    shiftStartTime?: string;
    /** 班次结束时间 */
    shiftEndTime?: string;
    /** 休息日类型：1单休，2双休，3自定义 */
    weeklyRestType?: string;
    /** 休息日:例如[一,二,三,四,五,六,七] */
    weeklyRestDay?: string;
    /** 数据加载模式：all:0-全加载不分页；page:1-分页； */
    pageQueryModel?: number;
  };

  type BusinessEmployeeInfoVo = {
    /** 员工id */
    employeeId?: number;
    /** 员工对应sys_user的id */
    userId?: string;
    /** 商家id */
    businessId?: number;
    /** 员工昵称 */
    employeeNickname?: string;
    /** 员工工作电话 */
    employeePhone?: string;
    /** 员工头像 */
    avatarUrl?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 上班：ON_DUTY  下班：OFF_DUTY */
    onDutyStatus?: string;
    /** 职位 */
    position?: string;
    /** 职级：高级、中级、初级 */
    rank?: string;
    /** 入职时间 */
    hiredate?: string;
    /** 班次名称：白班 DAY_SHIFT、夜班 NIGHT_SHIFT */
    shiftName?: string;
    /** 是否全天班次：1是，0否 */
    shiftWholeDay?: string;
    /** 班次开始时间 */
    shiftStartTime?: string;
    /** 班次结束时间 */
    shiftEndTime?: string;
    /** 休息日类型：1单休，2双休，3自定义 */
    weeklyRestType?: string;
    /** 休息日:例如[一,二,三,四,五,六,七] */
    weeklyRestDay?: string;
    /** 数据加载模式：all:0-全加载不分页；page:1-分页； */
    pageQueryModel?: number;
    /** 性别 */
    sex?: number;
  };

  type BusinessEmployeeVO = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 员工id */
    employeeId?: number;
    /** 员工对应sys_user的id */
    userId?: number;
    /** 商家id */
    businessId?: number;
    /** 员工昵称 */
    employeeNickname?: string;
    /** 员工工作电话 */
    employeePhone?: string;
    /** 员工头像 */
    avatarUrl?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 上班：ON_DUTY  下班：OFF_DUTY */
    onDutyStatus?: string;
    /** 职位 */
    position?: string;
    /** 职级：高级、中级、初级 */
    rank?: string;
    /** 入职时间 */
    hiredate?: string;
    /** 班次名称：白班 DAY_SHIFT、夜班 NIGHT_SHIFT */
    shiftName?: string;
    /** 是否全天班次：1是，0否 */
    shiftWholeDay?: string;
    /** 班次开始时间 */
    shiftStartTime?: string;
    /** 班次结束时间 */
    shiftEndTime?: string;
    /** 休息日类型：1单休，2双休，3自定义 */
    weeklyRestType?: string;
    /** 休息日:例如[一,二,三,四,五,六,七] */
    weeklyRestDay?: string;
    /** 数据加载模式：all:0-全加载不分页；page:1-分页； */
    pageQueryModel?: number;
    /** 性别 */
    sex?: number;
  };

  type BusinessFile = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    id?: number;
    businessId?: number;
    businessCategory?: number;
    fileType?: number;
    fileName?: string;
    fileUrl?: string;
    fileSize?: number;
  };

  type BusinessLicense = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 商家证照id */
    licenseId?: number;
    /** 对应商家表id */
    businessId?: number;
    /** 证照类型 */
    licenseType?: string;
    /** 证照号码、统一社会信用代码 */
    licenseIdentifier?: string;
    /** 证照图片url */
    licenseImageUrl?: string;
    /** 证照名称 */
    licenseName?: string;
    /** 有效期类型：0永久有效，1：参考有效日期 */
    licenseValidityType?: number;
    /** 有效期 */
    licenseValidityDate?: string;
    /** 企业名称 */
    enterpriseName?: string;
    /** 法人代表 */
    legalPerson?: string;
    /** 法人证件号码 */
    legalPersonIdentifier?: string;
    /** 法人证件正面图片url */
    legalPersonIdentifierFront?: string;
    /** 法人证件反面图片url */
    legalPersonIdentifierBack?: string;
    /** 法人证件有效期类型:0永久有效，1：参考有效日期 */
    legalPersonIdentifierValidityType?: number;
    /** 法人证件有效期 */
    legalPersonIdentifierValidityDate?: string;
    /** 开户银行 */
    depositBank?: string;
    /** 银行账号 */
    bankAccount?: string;
    /** 企业税号 */
    enterpriseTaxNum?: string;
  };

  type BusinessParamVo = {
    businessId?: number;
    /** 商户名 */
    businessName: string;
    /** 联系人 */
    contactUser: string;
    /** 联系电话 */
    contactPhone: string;
    /** 所在省市区 */
    region?: string;
    /** 所在详细地址 */
    address?: string;
    /** 行业分类 */
    businessCategory?: string;
    /** 营业开始时间 */
    businessStartHours?: string;
    /** 营业结束时间 */
    businessEndHours?: string;
    /** 登陆密码 */
    password: string;
    /** 推荐人推荐码 */
    referralCode?: string;
    createBy?: string;
    updateBy?: string;
  };

  type BusinessProject = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 商家项目id */
    projectId?: number;
    /** 对应商家表id */
    businessId?: number;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 项目简介 */
    projectBrief?: string;
    /** 项目详情 */
    projectDetail?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 项目原价 */
    origPrice?: number;
    /** 项目优惠价 */
    discPrice?: number;
    /** 项目主图 */
    image?: string;
  };

  type BusinessVO = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 商家id */
    businessId?: number;
    /** 商家名称 */
    businessName?: string;
    /** logo url */
    logo?: string;
    /** 商家所在省市区 */
    region?: string;
    /** 商家详细地址 */
    address?: string;
    /** 商家管理员，对应sys_user的id */
    businessManagerId?: number;
    /** 描述 */
    businessDesc?: string;
    /** 营业状态：1正常营业，2停业，3注销 */
    businessStatus?: string;
    /** 营业开始时间：如9:00 */
    businessStartHours?: string;
    /** 营业结束时间：如24:00 */
    businessEndHours?: string;
    /** 行业分类 */
    businessCategory?: string;
    /** 联系电话 */
    contactPhone?: string;
    /** 联系人 */
    contactUser?: string;
    /** 微信收款码 */
    weChatPaymentCodeQR?: string;
    /** 支付宝收款码 */
    aliPaymentCodeQR?: string;
    /** 推荐码 */
    referralCode?: string;
    /** 商家二维码地址 */
    qrcodeUrl?: string;
    /** 商户文件列表 */
    businessFileList?: BUSINESSRELATION.BusinessFile[];
  };

  type CardBindQrCodeParam = {
    /** 会员卡ID */
    memberCardRelationId: number;
  };

  type CardResetQrCodeParam = {
    /** 会员卡ID */
    memberCardRelationId: number;
  };

  type CardTemplateAddParam = {
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType: string;
    cardName: string;
    /** 卡零售价（元） */
    retailPrice: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 卡状态（0：禁用，1：启用） */
    status?: number;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 会员卡项目列表 */
    projects?: BUSINESSRELATION.CardTemplateProjectParam[];
  };

  type CardTemplateEditPara = {
    /** 主键，会员卡模板唯一标识 */
    id: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType: string;
    cardName: string;
    /** 卡零售价（元） */
    retailPrice: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 卡状态（0：禁用，1：启用） */
    status?: number;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    projects?: BUSINESSRELATION.CardTemplateProjectParam[];
  };

  type CardTemplateProjectParam = {
    /** 项目ID */
    projectId: number;
    /** 排序 */
    seq: number;
    id?: number;
  };

  type CategoryListVo = {
    /** 类目ID */
    categoryId?: number;
    /** 类目名称 */
    categoryName?: string;
    /** 类目描述 */
    categoryDesc?: string;
    /** 父类目ID */
    parentCategoryId?: number;
    /** 子类目 */
    children?: BUSINESSRELATION.CategoryListVo[];
  };

  type ContactVo = {
    /** 预约记录id */
    id?: number;
    /** 客户名称（联系人）名称 */
    nickName?: string;
    /** 联系方式手机 */
    phonenumber?: string;
    /** 联系方式微信 */
    wechatid?: string;
  };

  type CustomerResourceBasicInfoVo = {
    /** id */
    id?: number;
    /** 会员id */
    memberId?: number;
    /** 头像地址 */
    avatar?: string;
    /** 会员名称 */
    nickName?: string;
    /** 手机号 */
    phoneNumber?: string;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 生日 */
    birthday?: string;
    /** 客户来源（0:电话沟通；1：小红书；2:微信沟通;3:其他） */
    memberSource?: number;
    /** 推荐人 */
    referrerName?: string;
    /** 客户意向(0:无；1:低；2:中；3:高) */
    intention?: number;
    /** 顾问id */
    consultantId?: number;
    /** 顾问名称 */
    consultantName?: string;
    /** 标签 */
    tags?: string[];
    /** 备注 */
    remark?: string;
  };

  type CustomerResourceEditParam = {
    /** 客资ID */
    id: number;
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 推荐人 */
    referrerName?: string;
    /** 意向度客户意向(0:无；1:低；2:中；3:高) */
    intention?: number;
    /** 顾问id */
    consultantId?: number;
    /** 标签 */
    tags?: string;
    /** 备注 */
    remark?: string;
  };

  type CustomerResourceFollowUpRecordVo = {
    /** 跟进时间 */
    followUpTime?: string;
    /** 跟进人 */
    nickName?: string;
    /** 标题 */
    title?: string;
    /** 跟进内容(集合中有几条数据就展示几行) */
    contents?: string[];
    /** 所属门店 */
    businessName?: string;
  };

  type CustomerResourceInfoVo = {
    /** id */
    id?: number;
    /** 会员名称 */
    nickName?: string;
    /** 客户意向(0:无；1:低；2:中；3:高) */
    intention?: number;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 状态：0待联系，1已联系，2已到店 */
    status?: number;
    /** 头像地址 */
    avatar?: string;
    /** 会员id */
    memberId?: number;
  };

  type CustomerResourceListParam = {
    /** 模糊查询关键字（手机号 或 会员姓名 */
    searchKey?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态：0待联系，1已联系，2已到店 */
    statuses?: number[];
  };

  type CustomerResourceListVo = {
    /** id */
    id?: number;
    /** 会员名称 */
    nickName?: string;
    /** 客户意向(0:无；1:低；2:中；3:高) */
    intention?: number;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 状态：0待联系，1已联系，2已到店 */
    status?: number;
    /** 联系方式(掩码) */
    phonenumberMasked?: string;
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 留资时间 */
    leadTime?: string;
    /** 留资门店 */
    businessName?: string;
  };

  type deleteAppointmentRecordRecordIdParams = {
    recordId: number;
  };

  type deleteAppointmentTemplateTemplateIdParams = {
    templateId: number;
  };

  type deleteEmployeeEmployeeIdParams = {
    employeeId: number;
  };

  type deleteEmployeeStatisticsEmployeeIdParams = {
    employeeId: number;
  };

  type deleteMemberRecordIdsParams = {
    ids: number[];
  };

  type deleteMemberRemoveParams = {
    memberId: string;
  };

  type deleteMemCardtemplateTemplateIdsParams = {
    templateIds: number[];
  };

  type deleteRelationMemberCardRelationIdsParams = {
    /** 会员与会员卡关联ID列表 */
    memberCardRelationIds: number[];
  };

  type deleteRsetmealMemberCardRelationSetmealIdsParams = {
    /** 会员与会员卡的项目关联ID列表 */
    memberCardRelationSetmealIds: number[];
  };

  type deleteShopFile_openAPI_deleteParams = {
    id: number;
  };

  type deleteShopProjectProjectIdParams = {
    projectId: number;
  };

  type deleteTsetmealMemberCardTemplateSetmealIdsParams = {
    /** 会员卡模板对应的项目关联ID列表 */
    memberCardTemplateSetmealIds: number[];
  };

  type deleteWalkInBillIdParams = {
    billId: number;
  };

  type deleteWalkInBillIdsParams = {
    billIds: number[];
  };

  type FollowUpRecordAddParam = {
    /** 客资ID */
    memberResourcesId: number;
    /** 跟进内容 */
    content?: string;
    /** 意向度客户意向(0:无；1:低；2:中；3:高) */
    intention: number;
    /** 跟进方式（0:电话跟进；1:短信跟进；2:微信跟进；3:其他） */
    followUpMethod: number;
    /** 下次跟进时间 */
    nextFollowTime?: string;
  };

  type getAppointmentRecordRecordIdParams = {
    recordId: number;
  };

  type getAppointmentTemplateTemplateIdParams = {
    templateId: number;
  };

  type getCustomerResourceBasicCustomerResourceIdParams = {
    customerResourceId: number;
  };

  type getCustomerResourceCustomerResourceIdParams = {
    customerResourceId: number;
  };

  type getCustomerResourceFollowUpRecordCustomerResourceIdParams = {
    customerResourceId: number;
  };

  type getEmployeeEmployeeIdParams = {
    employeeId: number;
  };

  type getEmployeeListParams = {
    businessEmployee: BUSINESSRELATION.BusinessEmployee;
  };

  type getMemberDataDetailParams = {
    detailVo: BUSINESSRELATION.TMemberDetailVo;
  };

  type getMemberDetaillistParams = {
    detailVo: BUSINESSRELATION.TMemberDetailVo;
  };

  type getMemberFirstPinYinListParams = {
    detailVo: BUSINESSRELATION.TMemberDetailVo;
  };

  type getMemberListParams = {
    tMember: BUSINESSRELATION.TMember;
  };

  type getMemberMemberIdParams = {
    memberId: string;
  };

  type getMemberRecordDetailListNewParams = {
    detailVo: BUSINESSRELATION.TMemberOperRecordDetailVo;
  };

  type getMemberRecordDetaillistParams = {
    detailVo: BUSINESSRELATION.TMemberOperRecordDetailVo;
  };

  type getMemberRecordIdParams = {
    id: number;
  };

  type getMemberRecordListParams = {
    tMemberOperRecord: BUSINESSRELATION.TMemberOperRecordDetailVo;
  };

  type getMemCardtemplateBusineeTempListParams = {
    tMemberCardTemplate: BUSINESSRELATION.TMemberCardTemplate;
  };

  type getMemCardtemplateDetaillistParams = {
    tMemberCardTemplate: BUSINESSRELATION.TMemberCardTemplate;
  };

  type getMemCardtemplateListParams = {
    tMemberCardTemplate: BUSINESSRELATION.TMemberCardTemplate;
  };

  type getMemCardtemplateTemplateIdParams = {
    templateId: number;
  };

  type getRelationCardRTMemberListParams = {
    resultVo: BUSINESSRELATION.TMemberCardRelationResultVo;
  };

  type getRelationGetMemberParams = {
    /** 会员卡ID */
    memberCardRelationId: number;
  };

  type getRelationGetQrcodeMemberCardRelationIdParams = {
    /** 会员卡ID */
    memberCardRelationId: number;
  };

  type getRelationListParams = {
    /** 查询条件 */
    tMemberCardRelation: BUSINESSRELATION.TMemberCardRelation;
  };

  type getRelationMemberCardRelationIdParams = {
    /** 会员与会员卡关联ID */
    memberCardRelationId: number;
  };

  type getRelationRSetmealListParams = {
    tMemberCardRelation: BUSINESSRELATION.TMemberCardRelation;
  };

  type getReservationRecordGetContactReservationRecordIdParams = {
    reservationRecordId: number;
  };

  type getReservationRecordReservationRecordIdParams = {
    reservationRecordId: number;
  };

  type getRsetmealListParams = {
    /** 查询条件 */
    tMemberCardRelationSetmeal: BUSINESSRELATION.TMemberCardRelationSetmeal;
  };

  type getRsetmealMemberCardRelationSetmealIdParams = {
    /** 会员与会员卡的项目关联ID */
    memberCardRelationSetmealId: number;
  };

  type getShopGetBusinessIdsParams = {
    userId?: number;
  };

  type getShopLicenseParams = {
    businessId?: number;
  };

  type getTsetmealListParams = {
    /** 查询条件 */
    tMemberCardTemplateSetmeal: BUSINESSRELATION.TMemberCardTemplateSetmeal;
  };

  type getTsetmealMemberCardTemplateSetmealIdParams = {
    /** 会员卡模板对应的项目关联ID */
    memberCardTemplateSetmealId: number;
  };

  type getWalkInBillIdParams = {
    billId: number;
  };

  type getWalkInListParams = {
    bill: BUSINESSRELATION.WalkInBill;
  };

  type MemberCardTemplateInfoVo = {
    /** 会员卡模板id */
    id?: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    cardName: string;
    /** 卡零售价（元） */
    retailPrice?: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 卡状态（0：禁用，1：启用） */
    status?: number;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建人 */
    createBy?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 修改人 */
    updateBy?: string;
    /** 项目集合 */
    projects?: BUSINESSRELATION.MemberCardTemplateProjectInfoVo[];
  };

  type MemberCardTemplateProjectInfoVo = {
    /** 预约模版项目关系id */
    id?: number;
    /** 项目id */
    projectId?: number;
    /** 项目名称 */
    projectName?: string;
  };

  type MemberTypeGetParam = {
    /** 手机号 */
    phone: string;
  };

  type MemberVo = {
    /** 会员id */
    memberId?: string;
    /** 昵称 */
    nickName: string;
    /** 头像地址 */
    avatar?: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡片名称 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: number;
    /** 最后到店日期 */
    lastStoreDate?: string;
  };

  type MsgSendParamVo = {
    /** 消息类型 */
    msgType: number;
    /** 商家id */
    businessId?: number;
    /** 商家名称 */
    businessName?: string;
    /** 商家电话 */
    businessPhone?: string;
    会员卡号?: string;
    /** 服务名称 */
    serviceName?: string;
    客户会员名称?: string;
    /** 客户会员实收金额 */
    customerAmount?: number;
    /** 客户会员有效期 */
    customerValidDate?: string;
    /** 客户会员赠送金额（元） */
    customerGiftAmount?: number;
    /** 客户会员预约时间 */
    customerAppointmentDate?: string;
    /** 客户会员划卡金额(消费金额) */
    customerSpendingAmount?: number;
    /** 客户会员划卡金额(消费金额) */
    customerPracticalAmount?: number;
    /** 客户会员支付时间 */
    customerPayTime?: string;
    /** 客户会员支付方式 */
    customerPayWay?: string;
  };

  type postMember_openAPI_exportParams = {
    tMember: BUSINESSRELATION.TMember;
  };

  type postMemberRecord_openAPI_exportParams = {
    tMemberOperRecord: BUSINESSRELATION.TMemberOperRecordDetailVo;
  };

  type postMemCardtemplate_openAPI_exportParams = {
    tMemberCardTemplate: BUSINESSRELATION.TMemberCardTemplate;
  };

  type postRelation_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardRelation: BUSINESSRELATION.TMemberCardRelation;
  };

  type postReservationTemplateEditParams = {
    param: BUSINESSRELATION.TemplateAddParam;
  };

  type postReservationTemplateParams = {
    param: BUSINESSRELATION.TemplateAddParam;
  };

  type postRsetmeal_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardRelationSetmeal: BUSINESSRELATION.TMemberCardRelationSetmeal;
  };

  type postShopFileUploadParams = {
    file: string;
    businessCategory: number;
    fileType: number;
  };

  type postTsetmeal_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardTemplateSetmeal: BUSINESSRELATION.TMemberCardTemplateSetmeal;
  };

  type postWalkIn_openAPI_exportParams = {
    bill: BUSINESSRELATION.WalkInBill;
  };

  type ProjectAddParam = {
    /** 商家表id */
    businessId?: number;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 项目名称 */
    projectName: string;
    /** 项目原价 */
    origPrice?: number;
    /** 项目优惠价 */
    discPrice?: number;
    /** 项目主图 */
    image?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectAllParam = {
    /** 商家ID */
    businessId: number;
    /** 分类ID */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectEditParam = {
    /** 商家项目id，自增主键 */
    projectId: number;
    /** 项目名称 */
    projectName: string;
    /** 项目原价 */
    origPrice?: number;
    /** 项目优惠价 */
    discPrice?: number;
    /** 项目主图 */
    image?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectListParam = {
    /** 商家ID */
    businessId: number;
    /** 分类ID */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type putEmployeeChangeStatusParams = {
    employee: BUSINESSRELATION.BusinessEmployee;
  };

  type R = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RAppointmentRecord = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.AppointmentRecord;
  };

  type RAppointmentTemplate = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.AppointmentTemplate;
  };

  type RBoolean = {
    code?: number;
    msg?: string;
    data?: boolean;
  };

  type RBusinessAddVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessAddVo;
  };

  type RBusinessEmployeeInfoVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessEmployeeInfoVo;
  };

  type RBusinessFile = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessFile;
  };

  type RBusinessLicense = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessLicense;
  };

  type RBusinessVO = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessVO;
  };

  type RContactVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.ContactVo;
  };

  type RCustomerResourceBasicInfoVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.CustomerResourceBasicInfoVo;
  };

  type RCustomerResourceInfoVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.CustomerResourceInfoVo;
  };

  type RecordAddParam = {
    /** 顾客类型（0散客，1会员） */
    memberType: number;
    /** 来源渠道：0小程序，1公众号，2H5，3PC，4分享链接，5其他 */
    sourceChannel?: number;
    /** 预约日期 */
    reservationDate: string;
    /** 客户名称（联系人）名称 */
    nickName: string;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 联系方式手机 */
    phonenumber?: string;
    /** 联系方式微信 */
    wechatid?: string;
    /** 是否到店分配：0否，1是 */
    isAllocation: boolean;
    /** 手艺人列表（可多选或不选） */
    employees?: BUSINESSRELATION.RecordEmployeeParam[];
    /** 项目列表 */
    projects?: BUSINESSRELATION.RecordProjectParam[];
  };

  type RecordDateEditParam = {
    /** 预约记录id */
    id: number;
    /** 预约日期 */
    reservationDate: string;
  };

  type RecordEditParam = {
    /** 预约记录id */
    id: number;
    /** 顾客类型（0散客，1会员） */
    memberType: number;
    /** 预约日期 */
    reservationDate?: string;
    /** 客户名称（联系人）名称 */
    nickName: string;
    /** 联系方式手机 */
    phonenumber?: string;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 联系方式微信 */
    wechatid?: string;
    /** 是否到店分配：0否，1是 */
    isAllocation: boolean;
    /** 手艺人列表（可多选或不选） */
    employees?: BUSINESSRELATION.RecordEmployeeParam[];
    /** 项目列表 */
    projects?: BUSINESSRELATION.RecordProjectParam[];
  };

  type RecordEmployeeParam = {
    /** 预约记录手艺人id */
    id?: number;
    /** 模版项目id（如果没对手艺人指定项目，可以不传） */
    templateProjectId?: number;
    /** 模版手艺人id（手艺人和模版的关联id） */
    templateEmployeeId: number;
    /** 排序 */
    seq?: number;
  };

  type RecordListParam = {
    /** 模糊查询关键字（手机号 或 会员姓名 或 会员姓名拼音） */
    searchKey?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态：0待接单，1已接单，2已取消，3已到店，4未到店，5已完成，6已逾期 */
    statuses?: number[];
  };

  type RecordProjectParam = {
    /** 预约记录项目id */
    id?: number;
    /** 模版项目id（项目和模版的关联id） */
    templateProjectId?: number;
    /** 是否到店分配：0否，1是 */
    isAllocation: boolean;
    /** 项目原价 */
    projectOrigPrice: number;
    /** 项目优惠价 */
    projectDiscPrice: number;
    /** 项目时长 */
    projectDuration?: number;
    /** 项目购买数量 */
    buyCount: number;
    /** 排序 */
    seq?: number;
  };

  type RecordStatusEditParam = {
    /** 预约记录ID */
    reservationRecordId: number;
  };

  type ReservationRecordEmployeeInfoVo = {
    /** 预约记录手艺人关系id */
    id?: number;
    /** 模版手艺人id */
    templateEmployeeId?: number;
    /** 手艺人id */
    employeeId?: number;
    employeeNickname?: string;
    avatarUrl?: string;
    /** 排序 */
    seq?: number;
  };

  type ReservationRecordEmployeeVo = {
    /** 员工/手艺人昵称 */
    employeeNickname?: string;
    /** 员工/手艺人头像 */
    avatarUrl?: string;
    /** 排序 */
    seq?: number;
  };

  type ReservationRecordInfoVo = {
    /** 预约记录id */
    id?: number;
    /** 顾客类型：0散客，1会员 */
    memberType?: number;
    /** 来源渠道：0小程序，1公众号，2H5，3PC，4分享链接，5其他 */
    sourceChannel?: number;
    /** 预约日期 */
    reservationDate?: string;
    /** 预约状态：0待接单，1已接单，2已取消，3已到店，4未到店，5已完成，6已逾期 */
    status?: number;
    /** 顾客备注 */
    memberRemarks?: string;
    /** 商家备注 */
    businessRemarks?: string;
    /** 是否由商家发起：0否，1是 */
    isInitiatedByMerchant?: boolean;
    /** 到店人数 */
    memberCount?: number;
    /** 客户id（联系人）id */
    memberId?: number;
    /** 客户名称（联系人）名称 */
    nickName?: string;
    /** 用户性别;0=男,1=女,2=其他 */
    sex?: number;
    /** 联系方式手机 */
    phonenumber?: string;
    /** 联系方式微信 */
    wechatid?: string;
    /** 预约记录手艺人关系 */
    employees?: BUSINESSRELATION.ReservationRecordEmployeeInfoVo[];
    /** 预约记录项目关系 */
    projects?: BUSINESSRELATION.ReservationRecordProjectInfoVo[];
    /** 创建时间 */
    createTime?: string;
    /** 创建人 */
    createBy?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 修改人 */
    updateBy?: string;
    /** 是否到店分配：0否，1是 */
    isAllocation?: boolean;
  };

  type ReservationRecordListVo = {
    /** 预约记录id */
    id?: number;
    /** 顾客类型：0散客，1会员 */
    memberType?: number;
    /** 客户名称（联系人）名称 */
    nickName?: string;
    /** 联系方式(掩码) */
    phonenumberMasked?: string;
    /** 预约日期 */
    reservationDate?: string;
    /** 预约状态：0待接单，1已接单，2已取消，3已到店，4未到店，5已完成，6已逾期 */
    status?: number;
    /** 是否到店分配：0否，1是 */
    isAllocation?: boolean;
    /** 服务时间 */
    serviceTime?: string;
    /** 剩余时间 */
    remainingTime?: string;
    /** 创建人 */
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    /** 项目集合 */
    projects?: BUSINESSRELATION.ReservationRecordProjectVo[];
    /** 手艺人集合 */
    employees?: BUSINESSRELATION.ReservationRecordEmployeeVo[];
    /** 服务名称 */
    serviceName?: string;
    /** 服务价格 */
    servicePrice?: number;
  };

  type ReservationRecordProjectInfoVo = {
    /** 预约记录项目关系id */
    id?: number;
    /** 项目名称 */
    projectName?: string;
    /** 项目图片 */
    projectImage?: string;
    /** 项目id */
    projectId?: number;
    /** 模版项目id（项目和模版的关联id） */
    templateProjectId?: number;
    /** 是否到店分配：0否，1是 */
    isAllocation: boolean;
    /** 项目原价 */
    projectOrigPrice: number;
    /** 项目优惠价 */
    projectDiscPrice: number;
    /** 项目时长 */
    projectDuration?: number;
    /** 项目购买数量 */
    buyCount: number;
    /** 排序 */
    seq?: number;
  };

  type ReservationRecordProjectVo = {
    /** 项目名称 */
    projectName?: string;
    /** 项目原价 */
    projectOrigPrice?: number;
    /** 项目优惠价 */
    projectDiscPrice?: number;
    /** 排序 */
    seq?: number;
  };

  type RevenueVo = {
    /** 今日成交额 */
    depositAmount?: number;
    /** 划卡金额(消费金额) */
    spendingAmount?: number;
    /** 新客转换率 */
    conversionRate?: number;
  };

  type RInteger = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RListBusinessProject = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.BusinessProject[];
  };

  type RListCategoryListVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.CategoryListVo[];
  };

  type RListCustomerResourceFollowUpRecordVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.CustomerResourceFollowUpRecordVo[];
  };

  type RListCustomerResourceListVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.CustomerResourceListVo[];
  };

  type RListObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>[];
  };

  type RListReservationRecordListVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.ReservationRecordListVo[];
  };

  type RListString = {
    code?: number;
    msg?: string;
    data?: string[];
  };

  type RListTemplateProjectEmployeeVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TemplateProjectEmployeeVo[];
  };

  type RListTemplateProjectListVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TemplateProjectListVo[];
  };

  type RListTMember = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMember[];
  };

  type RListTMemberCardRelation = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardRelation[];
  };

  type RListTMemberCardTemplate = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardTemplate[];
  };

  type RListTMemberCardTemplateSetmeal = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardTemplateSetmeal[];
  };

  type RListWalkInBill = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.WalkInBill[];
  };

  type RLong = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RMapObjectObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RMemberCardTemplateInfoVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.MemberCardTemplateInfoVo;
  };

  type RMemberTypeEnum = {
    code?: number;
    msg?: string;
    data?: 'GUEST' | 'MEMBER';
  };

  type RMemberVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.MemberVo;
  };

  type RReservationRecordInfoVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.ReservationRecordInfoVo;
  };

  type RRevenueVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.RevenueVo;
  };

  type RString = {
    code?: number;
    msg?: string;
    data?: string;
  };

  type RTMember = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMember;
  };

  type RTMemberCardRelation = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardRelation;
  };

  type RTMemberCardRelationSetmeal = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardRelationSetmeal;
  };

  type RTMemberCardTemplateSetmeal = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberCardTemplateSetmeal;
  };

  type RTMemberDataDetailVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberDataDetailVo;
  };

  type RTMemberOperRecord = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TMemberOperRecord;
  };

  type RTrafficVo = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.TrafficVo;
  };

  type RWalkInBill = {
    code?: number;
    msg?: string;
    data?: BUSINESSRELATION.WalkInBill;
  };

  type TemplateAddParam = {
    /** 预约模版名称 */
    templateName: string;
    /** 服务方式：0到店分配，1选择手艺人 */
    serviceType?: number;
    /** 接单模式：0手动接单，1自动接单 */
    acceptMode?: number;
    /** 预约间隔时长（分钟） */
    reservationInterval?: number;
    /** 预约通知手机号（默认当前登录用户） */
    reservationNotifyPhone?: string;
    /** 预约库存来源：0手动设置，1自动计算 */
    reservationStockSource?: number;
    /** 预约提前时间限制（分钟） */
    reservationAdvanceTime?: number;
    /** 最晚保留时间限制（分钟） */
    reservationLatestTime?: number;
    /** 是否开启微信通知：0否，1是 */
    isWechatNotify?: boolean;
    /** 是否开启短信通知：0否，1是 */
    isSmsNotify?: boolean;
    /** 是否开启电话通知：0否，1是 */
    isPhoneNotify?: boolean;
    /** 手艺人权限（是否允许查看c端用户手机号）：0否，1是 */
    isEmployeePhone?: boolean;
    /** 备注 */
    remarks?: string;
    /** 预约开关/状态：0启用，1禁用 */
    status?: boolean;
    /** 预约二维码 */
    reservationQrCode?: string;
    /** 排序 */
    seq?: number;
    /** 预约模版项目 */
    projects?: BUSINESSRELATION.TemplateProjectParam[];
    /** 预约模版手艺人 */
    employees?: BUSINESSRELATION.TemplateEmployeeParam[];
    /** 预约模版时间段 */
    times?: BUSINESSRELATION.TemplateDateParam[];
  };

  type TemplateDateParam = true;

  type TemplateEmployeeParam = true;

  type TemplateEmployeesListParam = {
    /** 预约模版id集合(可以不传，默认查询所有) */
    templateIds?: number[];
    /** 员工/手艺人昵称 */
    employeeNickname?: string;
  };

  type TemplateProjectEmployeeVo = {
    /** 模版手艺人id（手艺人和模版的关联id） */
    templateEmployeeId?: number;
    employeeNickname?: string;
    avatarUrl?: string;
    /** 手艺人标签集合 */
    employeeTags?: string[];
    /** 排序 */
    seq?: number;
  };

  type TemplateProjectListParam = {
    /** 预约模版id集合(可以不传，默认查询所有) */
    templateIds?: number[];
    /** 项目名称 */
    projectName?: string;
  };

  type TemplateProjectListVo = {
    /** 预约模版项目ID */
    templateProjectId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 项目主图 */
    image?: string;
    /** 项目原价 */
    projectOrigPrice?: number;
    /** 项目优惠价 */
    projectDiscPrice?: number;
    /** 项目时长 */
    projectDuration?: number;
    /** 排序 */
    seq?: number;
    /** 手艺人列表（暂无，可多选或不选） */
    employees?: BUSINESSRELATION.TemplateProjectEmployeeVo[];
  };

  type TemplateProjectParam = true;

  type TMember = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 商铺用户类别，0:散客；1:会员 */
    memberType: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
    /** 到期日期 */
    endDate?: string;
    /** 拉黑状态；1:正常；0:拉黑 */
    banStatus?: string;
    /** 散客转会员时间 */
    transferTime?: string;
    /** 第一个汉字首字母 */
    firstPinYin?: string;
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 会员二维码 */
    qrcodeUrl?: string;
    /** 顾客标签 */
    memberTag?: string;
  };

  type TMemberCardRelation = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡片名称 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: number;
    /** 折扣 */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 积分 */
    giftIntegral?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型 */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 是否删除：0否，1是 */
    isdelete?: number;
    /** 卡片类型 */
    cardType?: string;
    /** 会员卡子项目 */
    rsetmealList?: BUSINESSRELATION.TMemberCardRelationSetmeal[];
    template?: BUSINESSRELATION.TMemberCardTemplate;
  };

  type TMemberCardRelationEditVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员与会员卡关联表id */
    memberCardRelationId: number;
    /** 会员id */
    memberId: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板唯一标识 */
    templateId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 状态：0禁用，1启用；退卡设置为0 */
    status: number;
  };

  type TMemberCardRelationResultVo = {
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    updateBy?: string;
    /** 更新时间 */
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 数量 */
    cnt?: number;
    /** 主键，会员卡模板唯一标识 */
    templateId?: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 卡名字 */
    cardName?: string;
    /** 会员卡-卡零售价（元） */
    retailPrice?: number;
    /** 会员卡-卡内余额（元） */
    cardBalance?: number;
    /** 会员卡-赠送金额（元） */
    giftAmount?: number;
    /** 会员卡-赠送卡券数量 */
    giftCoupon?: number;
    /** 会员卡-赠送积分数量 */
    giftIntegral?: number;
    /** 会员卡-折扣值（如8.8折） */
    discountRate?: number;
    /** 会员卡-卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 会员卡-赠送次数（计数卡使用） */
    giftTimes?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 会员卡-有效天数（按天数计算时使用） */
    validDays?: number;
    /** 会员卡-有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 会员卡-截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 会员卡-备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 会员卡-会员权益（顾客可见） */
    customerRemark?: string;
    /** 卡片类型描述 */
    cardDesc?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 会员姓名 */
    nickName?: string;
    /** 会员电话 */
    phonenumber?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 是否删除：0否，1是 */
    isdelete?: number;
  };

  type TMemberCardRelationSetmeal = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员与会员卡的项目关联表id */
    memberCardRelationSetmealId?: number;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 项目名称 */
    item?: string;
    /** 项目金额 */
    projectAmount?: number;
    /** 单位 */
    unit?: string;
    /** 是否删除：0否，1是 */
    isdelete?: number;
  };

  type TMemberCardRelationVo = {
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 操作时间 */
    operationTime?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板唯一标识 */
    templateId?: string;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 折扣值（如8.8折） */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 操作类型1充值，2消费,3扣除，4赠送,5更新（给项目卡用） */
    operationType?: string;
    /** 积分数量 */
    integral?: number;
    /** 操作积分数量 */
    giftIntegral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 操作金额 */
    amount?: number;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 余额 */
    opBalance?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 项目卡的各子项目内容合集 */
    rsetmealList?: BUSINESSRELATION.TMemberCardRelationSetmeal[];
  };

  type TMemberCardTemplate = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 主键，会员卡模板唯一标识 */
    templateId?: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡零售价（元） */
    retailPrice?: number;
    /** 卡内余额（元） */
    cardBalance?: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 赠送卡券数量 */
    giftCoupon?: number;
    /** 赠送积分数量 */
    giftIntegral?: number;
    /** 折扣值（如8.8折） */
    discountValue?: number;
    /** 卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 赠送次数（计数卡使用） */
    giftTimes?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 有效天数（按天数计算时使用） */
    validDays?: number;
    /** 有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 会员权益（顾客可见） */
    customerRemark?: string;
    /** 卡片类型描述 */
    cardDesc?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 商家ID */
    businessId?: string;
    /** 状态（0：禁用、1：启用） */
    status?: number;
    /** 会员卡模板子项目 */
    setmealList?: BUSINESSRELATION.TMemberCardTemplateSetmeal[];
    /** 会员卡数量 */
    cnt?: number;
    tsetmealList?: BUSINESSRELATION.TMemberCardTemplateSetmeal[];
  };

  type TMemberCardTemplateSetmeal = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员卡的项目关联表id */
    memberCardTemplateSetmealId?: number;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 项目名称 */
    item?: string;
    项目id?: number;
    /** 项目金额 */
    projectAmount?: number;
    /** 单位 */
    unit?: string;
    /** 排序 */
    seq?: number;
    /** 是否删除：0否，1是 */
    isdelete?: number;
  };

  type TMemberDataDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 用户ID */
    memberId?: string;
    /** 顾问 */
    advisor?: string;
    /** 美容师 */
    beautician?: string;
    beauticianId?: string;
    /** 顾问Id  */
    advisorId?: string;
    /** 商家Id  */
    businessId?: string;
    /** 累计充值  */
    totalRecharge?: number;
    /** 累计项目消费，次汇总统计  */
    totalConsumCnt?: number;
    /** 累计消费  */
    totalConsum?: number;
    /** 是否高频  */
    highFrequency?: number;
    /** 操作类型，预留字段  */
    operationType?: number;
    /** 操作描述，预留字段  */
    operationDesc?: string;
    /** 累计操作金额，预留字段  */
    totalAmount?: number;
    /** 累计操作次数 ，预留字段 */
    totalCountNumber?: number;
    /** 累计操作卡券数量，预留字段  */
    totalCoupon?: number;
    /** 累计操作积分，预留字段  */
    totalIntegral?: number;
  };

  type TMemberDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 商铺用户类别，0:散客；1:会员 */
    memberType: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
    /** 到期日期 */
    endDate?: string;
    /** 拉黑状态；1:正常；0:拉黑 */
    banStatus?: string;
    /** 散客转会员时间 */
    transferTime?: string;
    /** 第一个汉字首字母 */
    firstPinYin?: string;
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 会员二维码 */
    qrcodeUrl?: string;
    /** 顾客标签 */
    memberTag?: string;
    /** 用户名 */
    userName?: string;
    /** 会员真实姓名 */
    realName?: string;
    /** 微信登陆OPEN ID */
    weixinOpenid?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 数据加载模式：all:0-全加载不分页；page:1-分页； */
    pageQueryModel?: number;
  };

  type TMemberOperRecord = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1充值，2消费,3扣除，4赠送,5更新（给项目卡用）） */
    operationType?: number;
    /** 操作描述，预留字段（1充值，2消费,3扣除，4赠送,5更新（给项目卡用）  */
    operationDesc?: string;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
    /** 会员关系卡表ID */
    memberCardRelationId?: string;
    /** 卡券数量 */
    coupon?: number;
    /** 次数,每个 项目的消费次数 */
    countNumber?: number;
    /** 操作积分 */
    giftIntegral?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 实收金额 */
    receivedAmount?: number;
    /** 对应的商家id */
    businessId?: string;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 对应d_category字典表描述 */
    categoryItems?: string;
    /** 判断是否是同一批操作的项目 */
    categoryUid?: string;
    /** 判断是否是同一批操作的项目,入参合计 */
    categoryUids?: string[];
  };

  type TMemberOperRecordBP = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员操作记录--项目消费明细表id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 会员操作记录id，主键 */
    recordId?: number;
    /** 次数,每个 项目的消费次数 */
    count: number;
    /** 对应d_category字典表id */
    categoryId: number;
    /** 项目ID */
    projectId: number;
    /** 对应商家表id */
    businessId?: number;
  };

  type TMemberOperRecordDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1充值，2消费,3扣除，4赠送,5更新（给项目卡用）） */
    operationType?: number;
    /** 操作描述 */
    operationDesc?: string;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
    /** 会员关系卡表ID */
    memberCardRelationId?: string;
    /** 卡券数量 */
    coupon?: number;
    /** 次数,每个 项目的消费次数 */
    countNumber?: number;
    /** 操作积分 */
    giftIntegral?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 实收金额 */
    receivedAmount?: number;
    /** 商户ID */
    businessId?: string;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 对应d_category字典表描述 */
    categoryItems?: string;
    /** 判断是否是同一批操作的项目 */
    categoryUid?: string;
    /** 判断是否是同一批操作的项目,入参合计 */
    categoryUids?: string[];
    /** 员工昵称 */
    enickName?: string;
    /** 员工真实姓名 */
    erealName?: string;
    /** 员工用户名 */
    euserName?: string;
    /** 会员卡类型ID */
    memberCardTypeId?: string;
    /** 会员昵称 */
    nickName?: string;
    /** 会员手机号 */
    phonenumber?: string;
    /** 会员卡类型ID */
    templateId?: string;
    /** 当前可用余额 */
    latestAmount?: number;
    /** 当前可用积分 */
    latestIntegral?: number;
    /** 当前可用卡券数量 */
    latestCoupon?: number;
    /** 当前可用次数 */
    latestCountNumber?: number;
  };

  type TMemberOperRecordPerAddVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员卡类型 */
    cardType?: string;
    /** 会员卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 会员ID,会员开单时必填；非会员时不是必填项 */
    memberId?: number;
    /** 会员卡类型ID */
    memberCardTypeId?: string;
    /** 会员类型,0-散客；1:会员 */
    memberType?: number;
    /** 会员卡模版ID */
    templateId?: string;
    /** 会员卡ID,可为空；游客消费该字段为空，会员有对应的会员卡信息 */
    memberCardRelationId?: string;
    /** 会员昵称 */
    nickName?: string;
    /** 会员手机号 */
    phonenumber?: string;
    /** 员工昵称 */
    enickName?: string;
    /** 员工真实姓名 */
    erealName?: string;
    /** 员工用户名 */
    euserName?: string;
    /** 员工Id */
    employeeId: string;
    /** 商户ID */
    businessId?: number;
    /** 操作描述 */
    operationDesc?: string;
    /** 储值卡：操作前余额（即当前会员卡剩余金额） */
    amount?: number;
    /** 储值卡：消费金额（非金额相关卡，设置为0） */
    giftAmount?: number;
    /** 储值卡：会员卡余额（本次消费后余额；非金额相关卡，设置为0） */
    balance?: number;
    /** 实收金额（非金额相关卡，设置为0） */
    receivedAmount: number;
    /** 卡券：原券数量（即当前会员卡剩余卡券数量） */
    coupon?: number;
    /** 卡券：消费数量 */
    giftCoupon?: number;
    /** 次卡：次数（即当前会员卡剩余次数） */
    countNumber?: number;
    /** 次卡：消费次数 */
    giftCountNumber?: number;
    /** 积分卡：原积分（即当前会员卡剩余积分） */
    integral?: number;
    /** 积分卡：消费积分 */
    giftIntegral?: number;
    /** 消费的项目明细关系 */
    operRecordBPLists: BUSINESSRELATION.TMemberOperRecordBP[];
  };

  type TMemberOperRecordSummaryVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 商铺用户类别，0:散客；1:会员 */
    memberType: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
    /** 到期日期 */
    endDate?: string;
    /** 拉黑状态；1:正常；0:拉黑 */
    banStatus?: string;
    /** 散客转会员时间 */
    transferTime?: string;
    /** 第一个汉字首字母 */
    firstPinYin?: string;
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 会员二维码 */
    qrcodeUrl?: string;
    /** 顾客标签 */
    memberTag?: string;
    /** 会员累计充值金额 */
    amount?: string;
    /** 会员当月累计充值金额 */
    mmount?: string;
    /** 会员总充值次数 */
    cnt?: string;
    /** 操作类型 */
    operationType?: string;
    /** 会员卡ID */
    memberCardRelationId?: string;
    /** 实收金额 */
    receivedAmount?: number;
  };

  type TMemberRenewalVo = {
    /** 关联会员表id */
    memberId: string;
    /** 关联员工 */
    employeeId?: string;
    /** 会员卡片关系ID */
    memberCardRelationId: number;
    /** 储蓄卡，折扣卡：实收金额 */
    receivedAmount?: number;
    /** 次卡：实际充值次数 */
    receivedCount?: number;
    /** 积分卡：实际充值积分 */
    receivedIntegral?: number;
    /** 券卡：实际充值优惠券数 */
    receivedCoupon?: number;
    /** 添加时间 */
    operationTime?: string;
    /** 操作类型，6:原卡续费 */
    operationType: number;
    /** 剩余积分；确认累计，传原卡剩余积分，否则传0 */
    integral?: number;
    /** 卡片赠送积分；（由该卡种，卡片赠送积分构成，如卡面300积分，赠送30，则传30） */
    giftIntegral?: number;
    /** 余额天数；确认累计，传原卡剩余天数+该卡种的卡面天数+赠送天数；否则卡种的卡面天数+赠送天数（卡面30天+赠送3天） */
    endDate?: string;
    /** 套餐卡，会员卡项目必传；传 0.会员卡项目关系ID：member_card_relation_setmeal_id   1.会员卡关系表ID：member_card_relation_id   2.商家ID：business_id   3.会员卡模版ID：template_id   4.项目名称：item   5.项目金额/计数：projectAmount   6.单位：unit */
    rsetmealList?: BUSINESSRELATION.TMemberCardRelationSetmeal[];
    /** 剩余次数；确认累计，传原卡剩余次数，否则传该0 */
    countAmount?: number;
    /** 卡片赠送次数（由该卡种，卡片赠送次数构成，如卡面100元/5次，赠送1次，则传1） */
    giftCountAmount?: number;
    /** 剩余金额；确认累计，传原卡剩余金额，否则传该0 */
    amount?: number;
    /** 卡片赠送金额（由该卡种，卡片赠送金额构成，如卡面200，赠送20，则传20） */
    giftAmount?: number;
    /** 剩余优惠券；确认累计，传原卡剩余优惠券数，否则传该0 */
    coupon?: number;
    /** 卡片赠送优惠券（由该卡种，卡片赠送优惠券数，如卡面10，赠送1，则传1） */
    giftCoupon?: number;
    /** 会员卡模版ID */
    templateId?: number;
    oamount?: number;
    ointegral?: number;
    ocountAmount?: number;
    ocoupon?: number;
  };

  type TMemberVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 用户名 */
    userName?: string;
    /** 客户名字 */
    nickName?: string;
    /** 商铺用户类别，0:；1:会员 */
    memberType?: string;
    /** 会员真实姓名 */
    realName?: string;
    /** 微信登陆OPEN ID */
    weixinOpenid?: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex?: number;
    /** 生日 */
    birthday?: string;
    /** 备注 */
    remarks?: string;
    /** 卡片零售金额 */
    retailPrice?: number;
    /** 实收金额 */
    amount?: number;
    /** 关联员工 */
    employeeId?: string;
    /** 操作时间 */
    operationTime?: string;
    /** 操作类型：1充值；2消费 */
    operationType?: string;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板唯一标识 */
    templateId?: string;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 折扣值（如8.8折） */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 操作积分数量 */
    giftIntegral?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 散客转会员时间 */
    transferTime?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 模板卡的操作金额 */
    giftAmount?: number;
    /** 折扣值（如8.8折） */
    discountValue?: number;
    /** 卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 操作次数（计数卡使用） */
    giftTimes?: number;
    /** 有效天数（按天数计算时使用） */
    validDays?: number;
    /** 有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 会员权益（顾客可见） */
    customerRemark?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 拉黑状态；1:正常；0:拉黑 */
    banStatus?: string;
    /** 项目卡的各子项目内容合集 */
    setmealList?: BUSINESSRELATION.TMemberCardRelationSetmeal[];
    /** 会员来源 1:上门客人；2:员工带人；3:抖音客户 */
    memberSource?: number;
    /** 会员二维码 */
    qrcodeUrl?: string;
    /** 顾客标签 */
    memberTag?: string;
  };

  type TrafficVo = {
    /** 到客数 */
    guestCount?: number;
    /** 会员数 */
    memberCount?: number;
    /** 散客数 */
    nonMemberCount?: number;
  };

  type WalkInBill = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    billId?: number;
    businessId?: number;
    amount?: number;
    transactionTime?: string;
    associatedEmployeeId?: number;
    projectIds?: string;
  };
}
