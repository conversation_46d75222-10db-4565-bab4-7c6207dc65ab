/* eslint-disable */
import request from '@/helper/request';

/** 会员管理-录入会员：新增会员详细信息列表 会员管理-录入会员：新增会员详细信息列表 POST /business/member */
export async function postMember(body: MEMBER.TMemberVo, options?: { [key: string]: any }) {
  return request<MEMBER.RBoolean>('/business/member', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMember.traceId = '05c18a1c35a6b79545df17e45e895854';
