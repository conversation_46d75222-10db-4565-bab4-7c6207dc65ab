/* eslint-disable */
import request from '@/helper/request';

/** 修改会员详细信息列表 修改会员详细信息列表 PUT /business/member */
export async function putMember(body: MEMBER.TMember, options?: { [key: string]: any }) {
  return request<MEMBER.RBoolean>('/business/member', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      traceId: putMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
putMember.traceId = '950d6080b69cd3998eaf9e19a94d1bd1';
