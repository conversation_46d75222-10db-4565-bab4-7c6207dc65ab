/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /auth/register */
export async function register(body: AUTH.RegisterBody, options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/system/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: register.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
register.traceId = 'ebcfd08c95c56452aecc9a0bce23991f';
