/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /auth/login */
export async function login(body: AUTH.LoginBody, options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/system/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: login.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
login.traceId = '46e59e3acb3cddf2efb57fba2f8b8807';
