/* eslint-disable */
import request from '@/helper/request';

/** 发送短信验证码 根据手机号发送短信验证码 POST /system/auth/send-sms-code */
export async function postAuthSendSmsCode(
  body: AUTH.SmsCodeSendParam,
  options?: { [key: string]: any },
) {
  return request<AUTH.RBoolean>('/system/auth/send-sms-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postAuthSendSmsCode.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postAuthSendSmsCode.traceId = 'd02aed787beae2001f6b5ddcc865d80a';
