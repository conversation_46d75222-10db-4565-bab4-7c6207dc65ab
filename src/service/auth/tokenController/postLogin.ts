/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /auth/login */
export async function postLogin(body: AUTH.LoginBody, options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postLogin.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postLogin.traceId = 'df5efa3a9fb7d1cd064008de6fd47118';
