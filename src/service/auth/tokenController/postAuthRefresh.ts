/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /system/auth/refresh */
export async function postAuthRefresh(options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/system/auth/refresh', {
    method: 'POST',
    headers: {
      traceId: postAuthRefresh.traceId,
    },
    ...(options || {}),
  });
}
postAuthRefresh.traceId = '********************************';
