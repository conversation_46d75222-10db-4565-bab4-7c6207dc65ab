/* eslint-disable */
import request from '@/helper/request';

/** 短信验证码登陆 根据用户手机号和短信进行登陆 POST /system/auth/login/sms/business */
export async function postLoginSmsBusiness(
  body: AUTH.SmsLoginParam,
  options?: { [key: string]: any },
) {
  return request<AUTH.RObject>('/system/auth/login/sms/business', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postLoginSmsBusiness.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postLoginSmsBusiness.traceId = '2c7d469c8fea85c371c940e6c324bc37';
