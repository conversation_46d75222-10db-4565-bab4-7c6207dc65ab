/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /auth/login */
export async function businessLoginWx(body: AUTH.WeiXinLoginParam, options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/system/auth/login/wx/business', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: businessLoginWx.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
businessLoginWx.traceId = '46e59e3acb3cddf2efb57fba2f8b8324';
