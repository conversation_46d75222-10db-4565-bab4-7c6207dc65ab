/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 POST /system/auth/login */
export async function postAuthLogin(body: AUTH.LoginBody, options?: { [key: string]: any }) {
  return request<AUTH.RObject>('/system/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postAuthLogin.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postAuthLogin.traceId = 'd9f5609a16f7839e986bff8a28c51c4b';
