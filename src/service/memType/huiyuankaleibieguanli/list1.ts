/* eslint-disable */
import request from '@/helper/request';

/** 查询会员卡类型列表 查询会员卡类别列表 GET /mem/type/list */
export async function list1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEMTYPE.list1Params,
  options?: { [key: string]: any },
) {
  return request<MEMTYPE.TableDataInfo>('/business/member/type/list', {
    method: 'GET',
    headers: {
      traceId: list1.traceId,
    },
    params: {
      ...params,
      tMemberCardType: undefined,
      ...params['tMemberCardType'],
    },
    ...(options || {}),
  });
}
list1.traceId = '39d77dea4288ae250fd7d4de510d3748';
