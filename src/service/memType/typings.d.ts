/* eslint-disable */
declare namespace MEMTYPE {
  type AjaxResult = {
    error?: boolean;
    success?: boolean;
    warn?: boolean;
    empty?: boolean;
  };

  type export1Params = {
    tMemberOperRecord: MEMTYPE.TMemberOperRecord;
  };

  type export2Params = {
    tMember: MEMTYPE.TMember;
  };

  type export3Params = {
    tMemberCardTemplate: MEMTYPE.TMemberCardTemplate;
  };

  type exportUsingPOSTParams = {
    tMemberCardType: MEMTYPE.TMemberCardType;
  };

  type getInfo1Params = {
    id: number;
  };

  type getInfo2Params = {
    memberId: string;
  };

  type getInfo3Params = {
    templateId: number;
  };

  type getInfoParams = {
    cardTypeId: string;
  };

  type list1Params = {
    tMemberCardType: MEMTYPE.TMemberCardType;
  };

  type list2Params = {
    tMemberOperRecord: MEMTYPE.TMemberOperRecord;
  };

  type list3Params = {
    tMember: MEMTYPE.TMember;
  };

  type list4Params = {
    tMemberCardTemplate: MEMTYPE.TMemberCardTemplate;
  };

  type remove1Params = {
    ids: number[];
  };

  type remove2Params = {
    memberIds: string[];
  };

  type remove3Params = {
    templateIds: number[];
  };

  type removeParams = {
    cardTypeIds: string[];
  };

  type TableDataInfo = {
    total?: number;
    data?: Record<string, any>[];
    code?: number;
    msg?: string;
  };

  type TMember = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 余额 */
    balance?: number;
    /** 对应会员卡类型id */
    memberCardTypeId: string;
    /** 有效期 */
    validityDate?: string;
    /** 注册时间 */
    registerTime?: string;
    /** 主卡id，对应member_id */
    mainCardId?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    昵称: string;
    手机号码: string;
    '用户性别;0=男,1=女,2=未知': number;
    头像地址?: string;
    /** 生日 */
    birthday?: string;
  };

  type TMemberCardTemplate = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    templateId?: number;
    cardType?: string;
    cardName?: string;
    retailPrice?: number;
    cardBalance?: number;
    giftAmount?: number;
    giftCoupon?: number;
    giftIntegral?: number;
    discountValue?: number;
    cardTimes?: number;
    giftTimes?: number;
    validityType?: string;
    validDays?: number;
    validMonths?: number;
    endDate?: string;
    staffRemark?: string;
    customerRemark?: string;
  };

  type TMemberCardType = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    /** 会员卡类型ID */
    cardTypeId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 卡片名称 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: number;
    /** 卡零售价，元= */
    retailPrice?: number;
    /** 折扣 */
    discountRate?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注提醒（仅工作人员可见） */
    staffRemark?: string;
    /** 会员权益(顾客可见) */
    customerRemark?: string;
  };

  type TMemberDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 余额 */
    balance?: number;
    /** 对应会员卡类型id */
    memberCardTypeId: string;
    /** 有效期 */
    validityDate?: string;
    /** 注册时间 */
    registerTime?: string;
    /** 主卡id，对应member_id */
    mainCardId?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    昵称: string;
    手机号码: string;
    '用户性别;0=男,1=女,2=未知': number;
    头像地址?: string;
    /** 生日 */
    birthday?: string;
    /** 用户名 */
    userName?: string;
    /** 会员真实姓名 */
    realName?: string;
    /** 微信登陆OPEN ID */
    weixinOpenid?: string;
    /** 卡片等级 */
    cardLevel?: string;
  };

  type TMemberOperRecord = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1=充值，2消费） */
    operationType?: number;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
  };

  type TMemberOperRecordDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1=充值，2消费） */
    operationType?: number;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
    /** 员工昵称 */
    enickName?: string;
    /** 员工真实姓名 */
    erealName?: string;
    /** 员工用户名 */
    euserName?: string;
  };
}
