/* eslint-disable */
import request from '@/helper/request';

/** 获取会员详细信息列表 获取会员详细信息列表 GET /mem/member/${param0} */
export async function getInfo2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.getInfo2Params,
  options?: { [key: string]: any },
) {
  const { memberId: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/member/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getInfo2.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getInfo2.traceId = '2dc8a4616bfe4e829dc0669e1474a50d';
