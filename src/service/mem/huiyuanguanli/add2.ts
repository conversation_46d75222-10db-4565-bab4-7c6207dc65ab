/* eslint-disable */
import request from '@/helper/request';

/** 新增会员详细信息列表 新增会员详细信息列表 POST /mem/member */
export async function add2(body: MEN.TMember, options?: { [key: string]: any }) {
  return request<MEN.AjaxResult>('/business/member', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: add2.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
add2.traceId = 'd3e2abe64ee6381a3dc70e9c0d9d3ac8';
