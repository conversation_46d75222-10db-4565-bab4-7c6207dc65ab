/* eslint-disable */
import request from '@/helper/request';

/** 获取会员详细信息列表 获取会员详细信息列表 GET /business/mem/member/${param0} */
export async function getMemMemberMemberId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.getMemMemberMemberIdParams,
  options?: { [key: string]: any },
) {
  const { memberId: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/mem/member/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getMemMemberMemberId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getMemMemberMemberId.traceId = 'bdb747290ae0e70d48feeeb2b8bee324';
