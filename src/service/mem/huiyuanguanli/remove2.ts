/* eslint-disable */
import request from '@/helper/request';

/** 删除会员详细信息列表 删除会员详细信息列表 DELETE /mem/member/${param0} */
export async function remove2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.remove2Params,
  options?: { [key: string]: any },
) {
  const { memberIds: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/member/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: remove2.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
remove2.traceId = '0b85549f08768c4623516645edff5f0d';
