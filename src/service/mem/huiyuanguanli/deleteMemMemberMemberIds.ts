/* eslint-disable */
import request from '@/helper/request';

/** 删除会员详细信息列表 删除会员详细信息列表 DELETE /business/mem/member/${param0} */
export async function deleteMemMemberMemberIds(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.deleteMemMemberMemberIdsParams,
  options?: { [key: string]: any },
) {
  const { memberIds: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/mem/member/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteMemMemberMemberIds.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteMemMemberMemberIds.traceId = 'a83adc77227b736321e2276c2b71b81d';
