/* eslint-disable */
import request from '@/helper/request';

/** 新增会员详细信息列表 新增会员详细信息列表 POST /business/mem/member */
export async function postMemMember(body: MEN.TMemberVo, options?: { [key: string]: any }) {
  return request<MEN.AjaxResult>('/business/mem/member', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemMember.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemMember.traceId = '750e6d8cf0dc01fdbd624b642a04b397';
