/* eslint-disable */
import request from '@/helper/request';

/** 查询会员卡类型明细列表 查询会员卡类别明细列表 POST /business/mem/type/detaillist */
export async function postMemTypeDetaillist(
  body: MEN.TMemberCardType,
  options?: { [key: string]: any },
) {
  return request<MEN.TableDataInfo>('/business/mem/type/detaillist', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemTypeDetaillist.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemTypeDetaillist.traceId = '65b5a7c89f49f10c8fd518b746751f46';
