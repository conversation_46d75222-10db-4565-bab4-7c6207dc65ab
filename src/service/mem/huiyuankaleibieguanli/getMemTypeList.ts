/* eslint-disable */
import request from '@/helper/request';

/** 查询会员卡类型列表 查询会员卡类别列表 GET /business/mem/type/list */
export async function getMemTypeList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.getMemTypeListParams,
  options?: { [key: string]: any },
) {
  return request<MEN.TableDataInfo>('/business/mem/type/list', {
    method: 'GET',
    headers: {
      traceId: getMemTypeList.traceId,
    },
    params: {
      ...params,
      tMemberCardType: undefined,
      ...params['tMemberCardType'],
    },
    ...(options || {}),
  });
}
getMemTypeList.traceId = 'a1b90c17030621a2aec4b234d5ccfc17';
