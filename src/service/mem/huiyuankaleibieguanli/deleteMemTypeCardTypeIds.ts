/* eslint-disable */
import request from '@/helper/request';

/** 删除会员卡类别信息列表 删除会员卡类别信息列表 DELETE /business/mem/type/${param0} */
export async function deleteMemTypeCardTypeIds(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.deleteMemTypeCardTypeIdsParams,
  options?: { [key: string]: any },
) {
  const { cardTypeIds: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/mem/type/${param0}`, {
    method: 'DELETE',
    headers: {
      traceId: deleteMemTypeCardTypeIds.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
deleteMemTypeCardTypeIds.traceId = '11b0c77808653fcb8edf0609224d0d1b';
