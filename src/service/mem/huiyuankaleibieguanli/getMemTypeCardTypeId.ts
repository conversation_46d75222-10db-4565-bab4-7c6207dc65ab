/* eslint-disable */
import request from '@/helper/request';

/** 根据ID获取会员卡类别信息列表 根据ID获取会员卡类别信息列表 GET /business/mem/type/${param0} */
export async function getMemTypeCardTypeId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.getMemTypeCardTypeIdParams,
  options?: { [key: string]: any },
) {
  const { cardTypeId: param0, ...queryParams } = params;
  return request<MEN.AjaxResult>(`/business/mem/type/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getMemTypeCardTypeId.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getMemTypeCardTypeId.traceId = '926ea4a404a5b41434943534c85eea1f';
