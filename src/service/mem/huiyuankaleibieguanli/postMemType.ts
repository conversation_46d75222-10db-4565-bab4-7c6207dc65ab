/* eslint-disable */
import request from '@/helper/request';

/** 新增会员卡类别信息列表 新增会员卡类别信息列表 POST /business/mem/type */
export async function postMemType(body: MEN.TMemberCardType, options?: { [key: string]: any }) {
  return request<MEN.AjaxResult>('/business/mem/type', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postMemType.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postMemType.traceId = 'fe18aade4ec1655a1d9f04cb0e3e0155';
