/* eslint-disable */
import request from '@/helper/request';

/** 查询会员操作记录列表，按照商家+用户+卡类型汇总，传参（businessId，memberCardTypeId） 查询会员操作记录列表，按照商家+用户+卡类型汇总，传参（businessId，memberCardTypeId） POST /mem/record/detaillistSummary */
export async function postRecordDetaillistSummary(
  body: MEN.TMemberOperRecordDetailVo,
  options?: { [key: string]: any },
) {
  return request<MEN.TableDataInfo>('/business/member/record/detaillistSummary', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRecordDetaillistSummary.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRecordDetaillistSummary.traceId = '08ca4a374faee3d9a5f7889bcd8810af';
