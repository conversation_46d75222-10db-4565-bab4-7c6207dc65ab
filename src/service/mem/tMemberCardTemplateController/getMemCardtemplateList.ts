/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /business/mem/cardtemplate/list */
export async function getMemCardtemplateList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MEN.getMemCardtemplateListParams,
  options?: { [key: string]: any },
) {
  return request<MEN.RListTMemberCardTemplate>('/business/mem/cardtemplate/list', {
    method: 'GET',
    headers: {
      traceId: getMemCardtemplateList.traceId,
    },
    params: {
      ...params,
      tMemberCardTemplate: undefined,
      ...params['tMemberCardTemplate'],
    },
    ...(options || {}),
  });
}
getMemCardtemplateList.traceId = '25331fc1a9dc396583533ca884f51cf7';
