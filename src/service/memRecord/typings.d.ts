/* eslint-disable */
declare namespace MEMRECORD {
  type AjaxResult = {
    error?: boolean;
    success?: boolean;
    warn?: boolean;
    empty?: boolean;
  };

  type AppointmentRecord = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    recordId?: number;
    templateId?: number;
    employeeId?: number;
    businessId?: number;
    arriveNum?: number;
    appointmentTime?: string;
    status?: string;
    customerRemarks?: string;
    merchantRemarks?: string;
    employee?: MEMRECORD.BusinessEmployee;
  };

  type AppointmentTemplate = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    templateId?: number;
    businessId?: number;
    takeOrdersMode?: string;
    intervalDuration?: string;
    appointmentTimeSwitch?: boolean;
    appointmentPeopleNumSwitch?: boolean;
    remarkSwitch?: boolean;
    appointmentNum?: number;
    meanwhileReceptionNum?: number;
    status?: boolean;
    notifyPhone?: string;
    wxNotifySwitch?: boolean;
    noteNotifySwitch?: boolean;
    weekCanAppointmentDate?: string;
    dayCanAppointmentTime?: string;
    projectList?: MEMRECORD.BusinessProject[];
    employeeList?: MEMRECORD.BusinessEmployee[];
  };

  type Business = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 商家id */
    businessId?: number;
    /** 商家名称 */
    businessName?: string;
    /** logo url */
    logo?: string;
    /** 商家地址 */
    address?: string;
    /** 商家管理员，对应sys_user的id */
    businessManagerId?: number;
    /** 描述 */
    businessDesc?: string;
    /** 营业时间 */
    businessHours?: string;
    /** 联系电话 */
    contactPhone?: string;
    /** 联系人 */
    contactUser?: string;
    /** 微信收款码 */
    weChatPaymentCodeQR?: string;
    /** 支付宝收款码 */
    aliPaymentCodeQR?: string;
    /** 推荐码 */
    referralCode?: string;
  };

  type BusinessAddParam = {
    /** 商户名 */
    businessName: string;
    /** 所在地址 */
    address?: string;
    /** 联系人 */
    contactUser: string;
    /** 联系电话 */
    contactPhone: string;
    /** 登陆密码 */
    password: string;
    /** 推荐码 */
    referralCode?: string;
  };

  type BusinessAddVO = {
    /** token */
    token?: string;
    /** 用户id */
    userId?: number;
    /** 商家id */
    businessId?: number;
  };

  type BusinessEditParam = {
    /** 商家ID */
    businessId: number;
    /** 商户名 */
    businessName: string;
    /** 所在地址 */
    address?: string;
    /** 联系人 */
    contactUser: string;
    /** 联系电话 */
    contactPhone: string;
    /** 商家描述 */
    businessDesc?: string;
    /** 商家logo */
    logo?: string;
    /** 微信收款码 */
    weChatPaymentCodeQR?: string;
    /** 支付宝收款码 */
    aliPaymentCodeQR?: string;
    /** 营业时间 */
    businessHours?: string;
  };

  type BusinessEmployee = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    employeeId?: number;
    userId?: number;
    businessId?: number;
    employeeNickname?: string;
    employeePhone?: string;
    avatarUrl?: string;
    status?: number;
    onDutyStatus?: string;
    position?: string;
    hiredate?: string;
  };

  type BusinessEmployeeVO = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    employeeId?: number;
    userId?: number;
    businessId?: number;
    employeeNickname?: string;
    employeePhone?: string;
    avatarUrl?: string;
    status?: number;
    onDutyStatus?: string;
    position?: string;
    hiredate?: string;
    username?: string;
    sex?: string;
    roleList?: MEMRECORD.SysRole[];
  };

  type BusinessLicense = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 商家证照id */
    licenseId?: number;
    /** 对应商家表id */
    businessId?: number;
    /** 证据类型 */
    licenseType?: string;
    /** 企业名称 */
    enterpriseName?: string;
    /** 法人代表 */
    legalPerson?: string;
    /** 证件号码 */
    idNumber?: string;
    /** 有效期 */
    validityDate?: string;
    /** 证照图片url */
    imageUrl?: string;
    /** 备注 */
    remarks?: string;
  };

  type BusinessProject = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    projectId?: number;
    /** 对应商家表id */
    businessId?: number;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 项目简介 */
    projectBrief?: string;
    /** 项目详情 */
    projectDetail?: string;
    /** 项目价格 */
    projectPrice?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type CategoryListVo = {
    /** 类目ID */
    categoryId?: number;
    /** 类目名称 */
    categoryName?: string;
    /** 类目描述 */
    categoryDesc?: string;
    /** 父类目ID */
    parentCategoryId?: number;
    /** 子类目 */
    children?: MEMRECORD.CategoryListVo[];
  };

  type deleteAppointmentRecordRecordIdParams = {
    recordId: number;
  };

  type deleteAppointmentTemplateTemplateIdParams = {
    templateId: number;
  };

  type deleteEmployeeEmployeeIdParams = {
    employeeId: number;
  };

  type deleteEmployeeStatisticsEmployeeIdParams = {
    employeeId: number;
  };

  type deleteMemCardtemplateTemplateIdsParams = {
    templateIds: number[];
  };

  type deleteMemMemberMemberIdsParams = {
    memberIds: string[];
  };

  type deleteMemTypeCardTypeIdsParams = {
    cardTypeIds: string[];
  };

  type deleteRecordIdsParams = {
    ids: number[];
  };

  type deleteRelationMemberCardRelationIdsParams = {
    /** 会员与会员卡关联ID列表 */
    memberCardRelationIds: number[];
  };

  type deleteRsetmealMemberCardRelationSetmealIdsParams = {
    /** 会员与会员卡的项目关联ID列表 */
    memberCardRelationSetmealIds: number[];
  };

  type deleteShopLicenseLicenseIdsParams = {
    licenseIds: number[];
  };

  type deleteShopProjectProjectIdParams = {
    projectId: number;
  };

  type deleteTsetmealMemberCardTemplateSetmealIdsParams = {
    /** 会员卡模板对应的项目关联ID列表 */
    memberCardTemplateSetmealIds: number[];
  };

  type getAppointmentRecordRecordIdParams = {
    recordId: number;
  };

  type getAppointmentTemplateTemplateIdParams = {
    templateId: number;
  };

  type getEmployeeEmployeeIdParams = {
    employeeId: number;
  };

  type getEmployeeListParams = {
    businessEmployee: MEMRECORD.BusinessEmployee;
  };

  type getMemCardtemplateBusineeTempListParams = {
    tMemberCardTemplate: MEMRECORD.TMemberCardTemplate;
  };

  type getMemCardtemplateDetaillistParams = {
    tMemberCardTemplate: MEMRECORD.TMemberCardTemplate;
  };

  type getMemCardtemplateListParams = {
    tMemberCardTemplate: MEMRECORD.TMemberCardTemplate;
  };

  type getMemCardtemplateTemplateIdParams = {
    templateId: number;
  };

  type getMemMemberListParams = {
    tMember: MEMRECORD.TMember;
  };

  type getMemMemberMemberIdParams = {
    memberId: string;
  };

  type getMemTypeCardTypeIdParams = {
    cardTypeId: string;
  };

  type getMemTypeListParams = {
    tMemberCardType: MEMRECORD.TMemberCardType;
  };

  type getRecordIdParams = {
    id: number;
  };

  type getRecordListParams = {
    tMemberOperRecord: MEMRECORD.TMemberOperRecordDetailVo;
  };

  type getRelationListParams = {
    /** 查询条件 */
    tMemberCardRelation: MEMRECORD.TMemberCardRelation;
  };

  type getRelationMemberCardRelationIdParams = {
    /** 会员与会员卡关联ID */
    memberCardRelationId: number;
  };

  type getRelationRSetmealListParams = {
    tMemberCardRelation: MEMRECORD.TMemberCardRelation;
  };

  type getRsetmealListParams = {
    /** 查询条件 */
    tMemberCardRelationSetmeal: MEMRECORD.TMemberCardRelationSetmeal;
  };

  type getRsetmealMemberCardRelationSetmealIdParams = {
    /** 会员与会员卡的项目关联ID */
    memberCardRelationSetmealId: number;
  };

  type getShopBusinessIdParams = {
    businessId: number;
  };

  type getShopGetBusinessIduserIdParams = {
    userId: number;
  };

  type getShopLicenseBusinessIdParams = {
    businessId: number;
  };

  type getTsetmealListParams = {
    /** 查询条件 */
    tMemberCardTemplateSetmeal: MEMRECORD.TMemberCardTemplateSetmeal;
  };

  type getTsetmealMemberCardTemplateSetmealIdParams = {
    /** 会员卡模板对应的项目关联ID */
    memberCardTemplateSetmealId: number;
  };

  type LicenseAddParam = {
    /**  对应商家表id */
    businessId?: number;
    /** 证件类型 */
    licenseType: string;
    /** 法人代表 */
    legalPerson: string;
    /** 证件号码 */
    idNumber: string;
    /** 有效期 */
    validityDate?: string;
    /** 证照图片url */
    imageUrl: string;
    /** 备注 */
    remarks?: string;
  };

  type LicenseEditParam = {
    /** 商家证照id */
    licenseId?: number;
    /** 证件类型 */
    licenseType: string;
    /** 法人代表 */
    legalPerson: string;
    /** 证件号码 */
    idNumber: string;
    /** 有效期 */
    validityDate?: string;
    /** 证照图片url */
    imageUrl: string;
    /** 备注 */
    remarks?: string;
  };

  type postMemCardtemplate_openAPI_exportParams = {
    tMemberCardTemplate: MEMRECORD.TMemberCardTemplate;
  };

  type postMemMember_openAPI_exportParams = {
    tMember: MEMRECORD.TMember;
  };

  type postMemType_openAPI_exportParams = {
    tMemberCardType: MEMRECORD.TMemberCardType;
  };

  type postRecord_openAPI_exportParams = {
    tMemberOperRecord: MEMRECORD.TMemberOperRecordDetailVo;
  };

  type postRelation_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardRelation: MEMRECORD.TMemberCardRelation;
  };

  type postRsetmeal_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardRelationSetmeal: MEMRECORD.TMemberCardRelationSetmeal;
  };

  type postTsetmeal_openAPI_exportParams = {
    /** 查询条件 */
    tMemberCardTemplateSetmeal: MEMRECORD.TMemberCardTemplateSetmeal;
  };

  type ProjectAddParam = {
    /** 商家表id */
    businessId?: number;
    /** 对应d_category字典表id */
    categoryId?: number;
    /** 项目名称 */
    projectName: string;
    /** 项目简介 */
    projectBrief?: string;
    /** 项目详情 */
    projectDetail?: string;
    /** 项目价格 */
    projectPrice?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectAllParam = {
    /** 商家ID */
    businessId: number;
    /** 分类ID */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectEditParam = {
    /** 商家项目id，自增主键 */
    projectId?: number;
    /** 项目名称 */
    projectName: string;
    /** 项目简介 */
    projectBrief?: string;
    /** 项目详情 */
    projectDetail?: string;
    /** 项目价格 */
    projectPrice?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type ProjectListParam = {
    /** 商家ID */
    businessId: number;
    /** 分类ID */
    categoryId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
  };

  type R = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RAppointmentRecord = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.AppointmentRecord;
  };

  type RAppointmentTemplate = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.AppointmentTemplate;
  };

  type RBoolean = {
    code?: number;
    msg?: string;
    data?: boolean;
  };

  type RBusiness = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.Business;
  };

  type RBusinessAddVO = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.BusinessAddVO;
  };

  type RBusinessEmployeeVO = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.BusinessEmployeeVO;
  };

  type RInteger = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RListBusinessLicense = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.BusinessLicense[];
  };

  type RListBusinessProject = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.BusinessProject[];
  };

  type RListCategoryListVo = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.CategoryListVo[];
  };

  type RLong = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RMapObjectObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RTableDataInfo = {
    code?: number;
    msg?: string;
    data?: MEMRECORD.TableDataInfo;
  };

  type SysRole = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    roleId?: number;
    roleName: string;
    roleKey: string;
    roleSort: number;
    dataScope?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
    status?: string;
    flag?: boolean;
    menuIds?: number[];
    deptIds?: number[];
    permissions?: string[];
    admin?: boolean;
  };

  type TableDataInfo = {
    total?: number;
    data?: Record<string, any>[];
    code?: number;
    msg?: string;
  };

  type TMember = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
  };

  type TMemberCardRelation = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡片名称 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: number;
    /** 折扣 */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 积分 */
    giftIntegral?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型 */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 是否删除：0否，1是 */
    isdelete?: number;
    /** 卡片类型 */
    cardType?: string;
    /** 会员卡子项目 */
    rsetmealList?: MEMRECORD.TMemberCardRelationSetmeal[];
  };

  type TMemberCardRelationResultVo = {
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    updateBy?: string;
    /** 更新时间 */
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 数量 */
    cnt?: number;
    /** 主键，会员卡模板唯一标识 */
    templateId?: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡零售价（元） */
    retailPrice?: number;
    /** 卡内余额（元） */
    cardBalance?: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 赠送卡券数量 */
    giftCoupon?: number;
    /** 赠送积分数量 */
    giftIntegral?: number;
    /** 折扣值（如8.8折） */
    discountRate?: number;
    /** 卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 赠送次数（计数卡使用） */
    giftTimes?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 有效天数（按天数计算时使用） */
    validDays?: number;
    /** 有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 会员权益（顾客可见） */
    customerRemark?: string;
    /** 卡片类型描述 */
    cardDesc?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 会员姓名 */
    nickName?: string;
    /** 会员电话 */
    phonenumber?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 是否删除：0否，1是 */
    isdelete?: number;
    discountValue?: number;
  };

  type TMemberCardRelationSetmeal = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员与会员卡的项目关联表id */
    memberCardRelationSetmealId?: number;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 项目名称 */
    item?: string;
    /** 项目金额 */
    projectAmount?: number;
    /** 单位 */
    unit?: string;
    /** 是否删除：0否，1是 */
    isdelete?: number;
  };

  type TMemberCardRelationVo = {
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 操作时间 */
    operationTime?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板唯一标识 */
    templateId?: string;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 折扣值（如8.8折） */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 操作类型1充值，2消费,3扣除，4赠送,5更新（给项目卡用） */
    operationType?: string;
    /** 积分数量 */
    integral?: number;
    /** 操作积分数量 */
    giftIntegral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 操作金额 */
    amount?: number;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 余额 */
    opBalance?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 项目卡的各子项目内容合集 */
    rsetmealList?: MEMRECORD.TMemberCardRelationSetmeal[];
  };

  type TMemberCardTemplate = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 主键，会员卡模板唯一标识 */
    templateId?: number;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡零售价（元） */
    retailPrice?: number;
    /** 卡内余额（元） */
    cardBalance?: number;
    /** 赠送金额（元） */
    giftAmount?: number;
    /** 赠送卡券数量 */
    giftCoupon?: number;
    /** 赠送积分数量 */
    giftIntegral?: number;
    /** 折扣值（如8.8折） */
    discountValue?: number;
    /** 卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 赠送次数（计数卡使用） */
    giftTimes?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 有效天数（按天数计算时使用） */
    validDays?: number;
    /** 有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 会员权益（顾客可见） */
    customerRemark?: string;
    /** 卡片类型描述 */
    cardDesc?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 商家ID */
    businessId?: string;
    /** 会员卡模板子项目 */
    setmealList?: MEMRECORD.TMemberCardTemplateSetmeal[];
    /** 项目明细 */
    gettSetmealList?: MEMRECORD.TMemberCardTemplateSetmeal[];
  };

  type TMemberCardTemplateSetmeal = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    /** 删除者 */
    deleteBy?: string;
    /** 删除时间 */
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员卡的项目关联表id */
    memberCardTemplateSetmealId?: number;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板id */
    templateId?: string;
    /** 项目名称 */
    item?: string;
    /** 项目金额 */
    projectAmount?: number;
    /** 单位 */
    unit?: string;
    /** 是否删除：0否，1是 */
    isdelete?: number;
  };

  type TMemberCardType = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员卡类型ID */
    cardTypeId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 卡片名称 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: number;
    /** 卡零售价，元= */
    retailPrice?: number;
    /** 折扣 */
    discountRate?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注提醒（仅工作人员可见） */
    staffRemark?: string;
    /** 会员权益(顾客可见) */
    customerRemark?: string;
  };

  type TMemberDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
    /** 用户名 */
    userName?: string;
    /** 会员真实姓名 */
    realName?: string;
    /** 微信登陆OPEN ID */
    weixinOpenid?: string;
    /** 卡片等级 */
    cardLevel?: string;
  };

  type TMemberOperRecord = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1充值，2消费,3扣除，4赠送,5更新（给项目卡用）） */
    operationType?: number;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
    /** 会员关系卡表ID */
    memberCardRelationId?: string;
    /** 卡券数量 */
    coupon?: number;
    /** 次数 */
    countNumber?: number;
    /** 操作积分 */
    giftIntegral?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
  };

  type TMemberOperRecordDetailVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员操作记录id，主键 */
    id?: number;
    /** 关联会员表id，外键 */
    memberId: string;
    /** 操作类型（1充值，2消费,3扣除，4赠送,5更新（给项目卡用）） */
    operationType?: number;
    /** 操作金额 */
    amount?: number;
    /** 操作时间 */
    operationTime?: string;
    /** 类目id集合 */
    categoryIds?: string;
    /** 关联员工id，外键，内部员工表 */
    employeeId?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 操作金额 */
    giftAmount?: number;
    /** 余额 */
    opBalance?: number;
    /** 会员关系卡表ID */
    memberCardRelationId?: string;
    /** 卡券数量 */
    coupon?: number;
    /** 次数 */
    countNumber?: number;
    /** 操作积分 */
    giftIntegral?: number;
    /** 是否初始化 */
    initRecord?: number;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 员工昵称 */
    enickName?: string;
    /** 员工真实姓名 */
    erealName?: string;
    /** 员工用户名 */
    euserName?: string;
    /** 商户ID */
    businessId?: string;
    /** 会员卡类型ID */
    memberCardTypeId?: string;
    /** 会员昵称 */
    nickName?: string;
    /** 会员手机号 */
    phonenumber?: string;
    /** 会员卡类型ID */
    templateId?: string;
    /** 操作描述 */
    operationDesc?: string;
    /** 当前可用余额 */
    getlatestAmount?: number;
    /** 当前可用积分 */
    getlatestIntegral?: number;
    /** 当前可用卡券数量 */
    getlatestCoupon?: number;
    /** 当前可用次数 */
    getlatestCountNumber?: number;
  };

  type TMemberOperRecordSummaryVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 会员ID */
    memberId?: string;
    /** 会员对应的sys_user的id */
    userId: string;
    /** 会员所属商家的id */
    businessId: string;
    /** 余额 */
    balance?: number;
    /** 注册时间 */
    registerTime?: string;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 备注 */
    remarks?: string;
    /** 昵称 */
    nickName: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 头像地址 */
    avatar?: string;
    /** 生日 */
    birthday?: string;
    /** 会员累计充值金额 */
    amount?: string;
    /** 会员当月累计充值金额 */
    mmount?: string;
    /** 会员总充值次数 */
    cnt?: string;
    /** 操作类型 */
    operationType?: string;
    /** 会员卡ID */
    memberCardRelationId?: string;
  };

  type TMemberVo = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    /** 用户名 */
    userName?: string;
    /** 客户名字 */
    nickName: string;
    /** 会员真实姓名 */
    realName?: string;
    /** 微信登陆OPEN ID */
    weixinOpenid?: string;
    /** 手机号码 */
    phonenumber: string;
    /** 用户性别;0=男,1=女,2=未知 */
    sex: number;
    /** 生日 */
    birthday?: string;
    /** 备注 */
    remarks?: string;
    /** 卡片零售金额 */
    retailPrice?: number;
    /** 实收金额 */
    amount?: number;
    /** 关联员工 */
    employeeId?: string;
    /** 操作时间 */
    operationTime?: string;
    /** 操作类型：1充值；2消费 */
    operationType?: string;
    /** 积分 */
    integral?: number;
    /** 操作卡券数量 */
    giftCoupon?: number;
    /** 操作次数 */
    giftCountNumber?: number;
    /** 会员与会员卡关联表id */
    memberCardRelationId?: number;
    /** 会员id */
    memberId?: string;
    /** 对应的商家id */
    businessId?: string;
    /** 会员卡模板唯一标识 */
    templateId?: string;
    /** 卡类型（储值卡、折扣卡、计数卡、计时卡等） */
    cardType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡名字 */
    cardName?: string;
    /** 卡面url */
    cardImageUrl?: string;
    /** 卡片等级 */
    cardLevel?: string;
    /** 折扣值（如8.8折） */
    discountRate?: number;
    /** 余额 */
    balance?: number;
    /** 卡券数量 */
    coupon?: number;
    /** 次数（计数卡使用） */
    countNumber?: number;
    /** 操作积分数量 */
    giftIntegral?: number;
    /** 状态：0禁用，1启用 */
    status?: number;
    /** 卡有效期类型（长期有效、按天数计算、按月份计算、按截止日期计算） */
    validityType?: string;
    /** 截止日期（按截止日期计算时使用） */
    endDate?: string;
    /** 备注提醒（工作人员可见） */
    staffRemark?: string;
    /** 模板卡的操作金额 */
    giftAmount?: number;
    /** 折扣值（如8.8折） */
    discountValue?: number;
    /** 卡内次数（计数卡使用） */
    cardTimes?: number;
    /** 操作次数（计数卡使用） */
    giftTimes?: number;
    /** 有效天数（按天数计算时使用） */
    validDays?: number;
    /** 有效月份（按月份计算时使用） */
    validMonths?: number;
    /** 会员权益（顾客可见） */
    customerRemark?: string;
    /** 是否短信通知：0，否；1，是 */
    isSmsSubscribe?: number;
    /** 项目卡的各子项目内容合集 */
    setmealList?: MEMRECORD.TMemberCardRelationSetmeal[];
  };

  type WalkInBill = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    billId?: number;
    businessId?: number;
    amount?: number;
    transactionTime?: string;
    associatedEmployeeId?: number;
    projectIds?: string;
  };
}
