/* eslint-disable */
import request from '@/helper/request';

/** 查询当前会员操作汇总记录 查询当前会员操作汇总记录 POST /business/record/memberlistSummary */
export async function postRecordMemberlistSummary(
  body: MEMRECORD.TMemberOperRecordSummaryVo,
  options?: { [key: string]: any },
) {
  return request<MEMRECORD.TableDataInfo>('/business/member/record/memberlistSummary', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRecordMemberlistSummary.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRecordMemberlistSummary.traceId = '782e0a53402ef33756304d1b8a8a6675';
