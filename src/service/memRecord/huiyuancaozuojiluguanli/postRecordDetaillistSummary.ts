/* eslint-disable */
import request from '@/helper/request';

/** 查询会员操作记录列表，按照商家+用户+卡类型汇总，传参（businessId，memberCardTypeId） 查询会员操作记录列表，按照商家+用户+卡类型汇总，传参（businessId，memberCardTypeId） POST /mem/record/detaillistSummary */
export async function postRecordDetaillistSummary(
  body: MEMRECORD.TMemberOperRecordDetailVo,
  options?: { [key: string]: any },
) {
  return request<MEMRECORD.TableDataInfo>('/business/member/record/detaillistSummary', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRecordDetaillistSummary.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRecordDetaillistSummary.traceId = '17186d0f9f4cdad4fcb717badb6917f0';
