/* eslint-disable */
import request from '@/helper/request';

/** 查询会员操作记录列表 查询会员操作记录列表 POST /business/record/detaillist */
export async function postRecordDetaillist(
  body: MEMRECORD.TMemberOperRecordDetailVo,
  options?: { [key: string]: any },
) {
  return request<MEMRECORD.TableDataInfo>('/business/member/record/detaillist', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      traceId: postRecordDetaillist.traceId,
    },
    data: body,
    ...(options || {}),
  });
}
postRecordDetaillist.traceId = 'e226b5311c915dbcfabc9c23deb00ab5';
