/* eslint-disable */
declare namespace SYSTEM {
  type AjaxResult = {
    error?: boolean;
    success?: boolean;
    warn?: boolean;
    empty?: boolean;
  };

  type deleteConfigConfigIdsParams = {
    configIds: number[];
  };

  type deleteDeptDeptIdParams = {
    deptId: number;
  };

  type deleteDictDataDictCodesParams = {
    dictCodes: number[];
  };

  type deleteDictTypeDictIdsParams = {
    dictIds: number[];
  };

  type deleteLogininforInfoIdsParams = {
    infoIds: number[];
  };

  type deleteMenuMenuIdParams = {
    menuId: number;
  };

  type deleteNoticeNoticeIdsParams = {
    noticeIds: number[];
  };

  type deleteOnlineTokenIdParams = {
    tokenId: string;
  };

  type deleteOperlogOperIdsParams = {
    operIds: number[];
  };

  type deletePostPostIdsParams = {
    postIds: number[];
  };

  type deleteRoleRoleIdsParams = {
    roleIds: number[];
  };

  type deleteUserUserIdsParams = {
    userIds: number[];
  };

  type getConfigConfigIdParams = {
    configId: number;
  };

  type getConfigConfigKeyConfigKeyParams = {
    configKey: string;
  };

  type getConfigListParams = {
    config: SYSTEM.SysConfig;
  };

  type getDeptDeptIdParams = {
    deptId: number;
  };

  type getDeptListExcludeDeptIdParams = {
    deptId: number;
  };

  type getDeptListParams = {
    dept: SYSTEM.SysDept;
  };

  type getDictDataDictCodeParams = {
    dictCode: number;
  };

  type getDictDataListParams = {
    dictData: SYSTEM.SysDictData;
  };

  type getDictDataTypeDictTypeParams = {
    dictType: string;
  };

  type getDictTypeDictIdParams = {
    dictId: number;
  };

  type getDictTypeListParams = {
    dictType: SYSTEM.SysDictType;
  };

  type getLogininforListParams = {
    logininfor: SYSTEM.SysLogininfor;
  };

  type getLogininforUnlockUserNameParams = {
    userName: string;
  };

  type getMenuListParams = {
    menu: SYSTEM.SysMenu;
  };

  type getMenuMenuIdParams = {
    menuId: number;
  };

  type getMenuRoleMenuTreeselectRoleIdParams = {
    roleId: number;
  };

  type getMenuTreeselectParams = {
    menu: SYSTEM.SysMenu;
  };

  type getNoticeListParams = {
    notice: SYSTEM.SysNotice;
  };

  type getNoticeNoticeIdParams = {
    noticeId: number;
  };

  type getOnlineListParams = {
    ipaddr: string;
    userName: string;
  };

  type getOperlogListParams = {
    operLog: SYSTEM.SysOperLog;
  };

  type getPostListParams = {
    post: SYSTEM.SysPost;
  };

  type getPostPostIdParams = {
    postId: number;
  };

  type getRegionCityAdcodeParams = {
    adcode: string;
  };

  type getRegionDistrictAdcodeParams = {
    adcode: string;
  };

  type getRoleAuthUserAllocatedListParams = {
    user: SYSTEM.SysUser;
  };

  type getRoleAuthUserUnallocatedListParams = {
    user: SYSTEM.SysUser;
  };

  type getRoleDeptTreeRoleIdParams = {
    roleId: number;
  };

  type getRoleListParams = {
    role: SYSTEM.SysRole;
  };

  type getRoleRoleIdParams = {
    roleId: number;
  };

  type getUserAuthRoleUserIdParams = {
    userId: number;
  };

  type getUserDeptTreeParams = {
    dept: SYSTEM.SysDept;
  };

  type getUserInfoUsernameParams = {
    username: string;
  };

  type getUserListParams = {
    user: SYSTEM.SysUser;
  };

  type getUserUserIdParams = {
    userId: number;
  };

  type LoginBody = {
    username?: string;
    password?: string;
  };

  type LoginUser = {
    token?: string;
    userid?: number;
    username?: string;
    loginTime?: number;
    expireTime?: number;
    ipaddr?: string;
    permissions?: string[];
    roles?: string[];
    sysUser?: SYSTEM.SysUser;
  };

  type postConfig_openAPI_exportParams = {
    config: SYSTEM.SysConfig;
  };

  type postDictData_openAPI_exportParams = {
    dictData: SYSTEM.SysDictData;
  };

  type postDictType_openAPI_exportParams = {
    dictType: SYSTEM.SysDictType;
  };

  type postLogininfor_openAPI_exportParams = {
    logininfor: SYSTEM.SysLogininfor;
  };

  type postOperlog_openAPI_exportParams = {
    operLog: SYSTEM.SysOperLog;
  };

  type postPost_openAPI_exportParams = {
    post: SYSTEM.SysPost;
  };

  type postRole_openAPI_exportParams = {
    role: SYSTEM.SysRole;
  };

  type postUser_openAPI_exportParams = {
    user: SYSTEM.SysUser;
  };

  type postUserImportDataParams = {
    file: string;
    updateSupport: boolean;
  };

  type putRoleAuthUserCancelAllParams = {
    roleId: number;
    userIds: number[];
  };

  type putRoleAuthUserSelectAllParams = {
    roleId: number;
    userIds: number[];
  };

  type putUserAuthRoleParams = {
    userId: number;
    roleIds: number[];
  };

  type RBoolean = {
    code?: number;
    msg?: string;
    data?: boolean;
  };

  type Region = {
    adcode?: string;
    name?: string;
    center?: string;
    level?: string;
    citycode?: string[];
    districts?: SYSTEM.Region[];
  };

  type RegisterBody = {
    username?: string;
    password?: string;
  };

  type RInteger = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RLoginUser = {
    code?: number;
    msg?: string;
    data?: SYSTEM.LoginUser;
  };

  type RLong = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RRegion = {
    code?: number;
    msg?: string;
    data?: SYSTEM.Region;
  };

  type RSysUser = {
    code?: number;
    msg?: string;
    data?: SYSTEM.SysUser;
  };

  type SmsCodeSendParam = {
    /** 手机号 */
    phonenumber: string;
  };

  type SmsLoginParam = {
    /** 手机号 */
    phonenumber: string;
    /** 短信验证码 */
    smscode: string;
    /** code-同微信登录接口 */
    code?: string;
    /** 头像地址-同微信登录接口 */
    avatarUrl?: string;
    /** 性别-同微信登录接口 */
    gender?: number;
    /** 昵称-同微信登录接口 */
    nickName?: string;
  };

  type SysConfig = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    configId?: number;
    configName: string;
    configKey: string;
    configValue: string;
    configType?: string;
  };

  type SysDept = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    deptId?: number;
    parentId?: number;
    ancestors?: string;
    deptName: string;
    orderNum: number;
    leader?: string;
    phone?: string;
    email?: string;
    status?: string;
    parentName?: string;
    children?: SYSTEM.SysDept[];
  };

  type SysDictData = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    dictCode?: number;
    dictSort?: number;
    dictLabel: string;
    dictValue: string;
    dictType: string;
    cssClass?: string;
    listClass?: string;
    isDefault?: string;
    status?: string;
    default?: boolean;
  };

  type SysDictType = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    dictId?: number;
    dictName: string;
    dictType: string;
    status?: string;
  };

  type SysLogininfor = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    infoId?: number;
    userName?: string;
    status?: string;
    ipaddr?: string;
    msg?: string;
    accessTime?: string;
  };

  type SysMenu = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    menuId?: number;
    menuName: string;
    parentName?: string;
    parentId?: number;
    orderNum: number;
    path?: string;
    component?: string;
    query?: string;
    routeName?: string;
    isFrame?: string;
    isCache?: string;
    menuType: string;
    visible?: string;
    status?: string;
    perms?: string;
    icon?: string;
    children?: SYSTEM.SysMenu[];
  };

  type SysNotice = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    noticeId?: number;
    noticeTitle: string;
    noticeType?: string;
    noticeContent?: string;
    status?: string;
  };

  type SysOperLog = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    operId?: number;
    title?: string;
    businessType?: number;
    businessTypes?: number[];
    method?: string;
    requestMethod?: string;
    operatorType?: number;
    operName?: string;
    deptName?: string;
    operUrl?: string;
    operIp?: string;
    operParam?: string;
    jsonResult?: string;
    status?: number;
    errorMsg?: string;
    operTime?: string;
    costTime?: number;
  };

  type SysPost = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    postId?: number;
    postCode: string;
    postName: string;
    postSort: number;
    status?: string;
    flag?: boolean;
  };

  type SysRole = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    roleId?: number;
    roleName: string;
    roleKey: string;
    roleSort: number;
    dataScope?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
    status?: string;
    flag?: boolean;
    menuIds?: number[];
    deptIds?: number[];
    permissions?: string[];
    admin?: boolean;
  };

  type SysUser = {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    delFlag?: boolean;
    deleteBy?: string;
    deleteTime?: string;
    params?: Record<string, any>;
    userId?: number;
    deptId?: number;
    userName?: string;
    nickName?: string;
    realName?: string;
    userType?: string;
    weixinOpenid?: string;
    sessionKey?: string;
    riskLevel?: number;
    email?: string;
    phonenumber?: string;
    sex?: string;
    avatar?: string;
    password?: string;
    status?: string;
    loginIp?: string;
    loginDate?: string;
    dept?: SYSTEM.SysDept;
    roles?: SYSTEM.SysRole[];
    roleIds?: number[];
    postIds?: number[];
    roleId?: number;
    admin?: boolean;
  };

  type SysUserRole = {
    userId?: number;
    roleId?: number;
  };

  type TableDataInfo = {
    total?: number;
    data?: Record<string, any>[];
    code?: number;
    msg?: string;
  };

  type WeiXinLoginParam = {
    code?: string;
    encryptedData?: string;
    iv?: string;
    nickName?: string;
    avatarUrl?: string;
    gender?: number;
  };
}
