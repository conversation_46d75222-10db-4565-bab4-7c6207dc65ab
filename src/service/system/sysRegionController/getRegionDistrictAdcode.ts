/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /system/region/district/${param0} */
export async function getRegionDistrictAdcode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: SYSTEM.getRegionDistrictAdcodeParams,
  options?: { [key: string]: any },
) {
  const { adcode: param0, ...queryParams } = params;
  return request<SYSTEM.RRegion>(`/system/region/district/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getRegionDistrictAdcode.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getRegionDistrictAdcode.traceId = '2be53634c89dbc5a389b7b1a2b86ea6d';
