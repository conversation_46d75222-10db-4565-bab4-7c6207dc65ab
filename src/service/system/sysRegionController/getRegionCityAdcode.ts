/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /system/region/city/${param0} */
export async function getRegionCityAdcode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: SYSTEM.getRegionCityAdcodeParams,
  options?: { [key: string]: any },
) {
  const { adcode: param0, ...queryParams } = params;
  return request<SYSTEM.RRegion>(`/system/region/city/${param0}`, {
    method: 'GET',
    headers: {
      traceId: getRegionCityAdcode.traceId,
    },
    params: { ...queryParams },
    ...(options || {}),
  });
}
getRegionCityAdcode.traceId = 'e0be22c302727cc5ee933652ec5471d9';
