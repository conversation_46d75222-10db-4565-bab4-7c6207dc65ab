/* eslint-disable */
import request from '@/helper/request';

/** 此处后端没有提供注释 GET /system/region/province */
export async function getRegionProvince(options?: { [key: string]: any }) {
  return request<SYSTEM.RRegion>('/system/region/province', {
    method: 'GET',
    headers: {
      traceId: getRegionProvince.traceId,
    },
    ...(options || {}),
  });
}
getRegionProvince.traceId = '299e0cff3c022cec4cb9aaee5f456c01';
