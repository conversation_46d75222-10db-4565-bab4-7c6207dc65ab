export default defineAppConfig({
  pages: [
    "view/page/single/guide/guide-page",
    "view/page/tab/home/<USER>",
    "view/page/single/login/login-page",
    "view/page/single/password-recovery/password-recovery-page",
    'view/page/single/walkIn-bill-add/walkIn-bill-add-page'

  ],
  "subpackages": [
    {
      "root": "shop",
      "pages": [
        "register-business/register-business-page", //商家入驻
        "shop-management/shop-management-page", // 店铺管理
        "enterprise-certification/enterprise-certification-page", // 企业认证
        "shop-edit/shop-edit-page", // 编辑店铺信息
      ]
    },
    {
      "root": "reservation",
      "pages": [
        "reservation-management/reservation-management-page", // 预约管理
        "appointment/appointment-page", // 添加预约
      ]
    },
    {
      "root": "member",
      "pages": [
        "member-center/member-center-page", // 会员中心
        "member-detail/member-detail-page", // 会员详情
        "addmember/addmember-page", // 新增会员
        "newcard-add/newcard-add-page", // 开卡
        'renew-member/renew-member-page', // 续卡
        'member-card-detail/member-card-detail-page', // 会员卡详情（二级）
        'order-management/order-management-page', // 开单结账
        "billing-settlement/billing-settlement-page", // 开单结账
        'member-billing-detail/member-billing-detail-page', // 会员开单详情页面
        'select-card/select-card-page', // 选择会员卡
      ]
    },
    {
      "root": "member-card-template",
      "pages": [
        "template-list/template-list-page", // 会员卡模板列表
        "create-member-card/create-member-card-page", // 创建会员卡模板
        'member-card-template-detail/member-card-template-detail-page', // 会员卡模板详情 (路径已更新)
      ]
    },
    {
      "root": "employee",
      "pages": [
        "staff-management/staff-management-page", // 员工管理
        "staff-add-staff/staff-add-staff-page", // 新增员工
        "staff-shift/staff-shift-page", // 员工排班
      ]
    },
    {
      "root": "customer-resources",
      "pages": [
        'customer-center/customer-center-page',
        'black-list-reason/black-list-reason-page',
        'customer-detail/customer-detail-page',
        'add-follow-up/add-follow-up-page',
        'edit-customer/edit-customer-page'
      ]
    }

  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    navigationStyle: 'custom', // 添加这行，使用自定义导航栏
    backgroundColor: '#fff',
  },
  lazyCodeLoading: 'requiredComponents',
  requiredPrivateInfos: ['chooseLocation', 'getLocation'],
  permission: {
    'scope.userLocation': {
      desc: '您的位置信息将用于小程序位置接口的效果展示',
    },
  },
  requiredBackgroundModes: ['location'],

});
