// 性别
export const SEX_RANGE = [
    { label: "男", value: 0 },
    { label: "女", value: 1 },
    { label: "未知", value: 2 }
]

// 店员职位
export const EMPLOYEE_POSITION = [
    { label: "店长", value: "店长" },
    { label: "店员", value: "店员" },
    { label: "手艺人", value: "手艺人" }
]

// 店员级别
export const EMPLOYEE_RANK = [
    { label: "高级", value: "高级" },
    { label: "中级", value: "中级" },
    { label: "初级", value: "初级" }
]

// 店员排班状态
export const SEX_SCHEDULE_STATUS = [
    { label: "上班", value: "ON_DUTY" },
    { label: "下班", value: "OFF_DUTY" }
]



/**
 * 获取 range 目标文本
 * @param range 
 * @param value 
 * @returns 
 */
export const getRangeItemLabel = (range: Array<{ label: string, value: string | number }>, value?: string | number) => {
    if (value == null || value == undefined || !value.toString().trim()) {
        return ""
    }
    const exist = range.find(item => item.value === value);
    return exist?.label ?? ""
}