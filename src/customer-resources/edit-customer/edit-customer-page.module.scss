.pageContainer {
  padding-bottom: 100px; // Space for fixed footer, increased from 80px
  background-color: #f7f8fa;
  min-height: 100vh;
}

.scrollView {
  height: 100%; // Ensures it tries to fill its parent container from PageWithNav
  // Taro's ScrollView with the scrollY prop handles the actual scrolling mechanism.
}

.section {
  background-color: #fff;
  margin-bottom: 10px;
  &:first-child {
    margin-top: 10px;
  }
}

.sectionTitle {
  font-size: 13px;
  color: #999;
  padding: 12px 15px;
  display: block;
  border-bottom: 1px solid #f0f0f0;
}

.formItem,
.formItemColumn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 15px;
  font-size: 15px;
  border-bottom: 1px solid #f0f0f0;
  min-height: 24px; // Ensure consistent height

  &:last-child {
    border-bottom: none;
  }
}

.formItemColumn {
  flex-direction: column;
  align-items: flex-start;
  .label {
    margin-bottom: 10px;
  }
}

.label {
  color: #333;
  margin-right: 15px;
  white-space: nowrap;
  .required {
    color: #ff4d4f;
    margin-left: 2px;
  }
}

.input,
.textarea {
  flex: 1;
  text-align: right;
  font-size: 15px;
  color: #333;
  width: 100%; // for textarea in column layout
}

.textarea {
  height: 190px; // Set fixed height as requested
  padding: 8px;
  border-radius: 4px;
  text-align: left;
  margin-top: 5px;
}

.valueArea {
  display: flex;
  align-items: center;
  color: #333;
  flex: 1;
  justify-content: flex-end;
}

.pickerValue {
  color: #333;
}

.placeholder {
  color: #bbb;
}

.arrow {
  font-family: sans-serif; // More reliable arrow
  color: #bbb;
  margin-left: 8px;
  font-size: 16px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #eee;
}

.phoneNote {
  font-size: 12px;
  color: #999;
  padding: 0 15px 10px;
  background-color: #fff;
}

.genderSelector {
  display: flex;
  flex: 1;
  justify-content: flex-end;
}

.genderOption {
  padding: 5px 15px;
  border: 1px solid #ddd;
  border-radius: 15px;
  margin-left: 10px;
  font-size: 14px;
  color: #666;
  background-color: #f7f8fa;

  &.selected {
    background-color: #ffeacc; // Light yellow for selected (matches design more or less)
    border-color: #ffc300;
    color: #ff8c00; // Darker yellow/orange for text
  }
}

.tagContainer {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 5px; 
  padding-bottom: 5px; // Spacing for tags
}

.tagOption {
  padding: 5px 10px;
  border: 1px solid #eee;
  border-radius: 3px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  background-color: #f7f8fa;

  &.selected {
    background-color: #ffeacc; // Light yellow
    border-color: #ffc300;
    color: #ff8c00;
  }
}

.charCount {
  font-size: 12px;
  color: #999;
  text-align: right;
  padding: 0 15px 10px;
  background-color: #fff; // If textarea section needs distinct bg
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 10px 15px calc(10px + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.footerButton {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
  background-color: #FDD244;
  color: #fff;

  &.cardButton {
    background-color: #f0f0f0;
    color: #333;
    margin-right: 10px;
  }

  &.saveButton {
    background-color: #ffc300; // Main yellow
    color: #fff;

    &.disabled {
      background-color: #ffdDAA; // Lighter yellow when disabled
      opacity: 0.7;
    }
  }
}

.phoneItemContainer {
  // Using existing .formItem styles for flex and align-items
  // justify-content: space-between; is already on .formItem
}

.phoneInput {
  flex-grow: 1; // Allow input to take available space
  margin-right: 10px; // Space before the call button
}

.callButton {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  background-color: #f7f8fa;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background-color: #eee;
  }
} 