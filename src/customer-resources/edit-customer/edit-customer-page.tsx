import React, { useState, useEffect } from 'react';
import { View, Text, Input, Textarea, Image, Picker, ScrollView } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import EmployeeSelector, { EmployeeItem } from "@/view/component/employee-selector";
import { getEmployeeList } from '@/service/business/businessEmployeeController'
import { getCustomerResourceBasicCustomerResourceId, putCustomerResource } from '@/service/business/customerResourceController';
import styles from './edit-customer-page.module.scss';



interface CustomerResourceBasicInfoVo {
  id?: number;
  memberId?: number;
  avatar?: string;
  nickName?: string;
  phoneNumber?: string;
  sex?: number;
  birthday?: string;
  memberSource?: number; // 客户来源（0:电话沟通；1：小红书；2:微信沟通;3:其他）
  referrerName?: string;
  intention?: number;
  consultantId?: number;
  consultantName?: string;
  tags?: string[]; // Assuming API returns string array for GET, but design shows array for UI
  remark?: string;
}

interface CustomerResourceEditParam {
  id: number;
  memberSource?: number; // 会员来源 1:上门客人；2:员工带人；3:抖音客户 (NOTE: values differ from BasicInfoVo)
  referrerName?: string;
  intention?: number;
  consultantId?: number;
  tags?: string; // API likely expects a single string (e.g., comma-separated)
  remark?: string;
  // Fields like nickName, phoneNumber, sex, birthday, avatar are MISSING here.
}

// FormData for the page, based on the more comprehensive BasicInfoVo for editing experience
interface FormData extends Partial<Omit<CustomerResourceBasicInfoVo, 'tags'> & { tags: string[] }> {}

const GENDER_OPTIONS = [
  { label: '男', value: 0 },
  { label: '女', value: 1 },
  { label: '未知', value: 2 },
];

// Options for GET display (from CustomerResourceBasicInfoVo)
const SOURCE_CHANNEL_OPTIONS_DISPLAY = [
  { label: '电话沟通', value: 0 },
  { label: '小红书', value: 1 },
  { label: '微信沟通', value: 2 },
  { label: '其他', value: 3 },
];


const INTENTION_OPTIONS = [
  { label: '未知', value: 0 },
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 },
];

const AVAILABLE_TAGS = ['轻医美', '有痘', '敏感肌', '容易过敏'];

const EditCustomerPage: React.FC = () => {
  const router = useRouter();
  const customerId = router.params.id;

  const [formData, setFormData] = useState<FormData>({});
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEmployeeSelectorVisible, setIsEmployeeSelectorVisible] = useState(false);
  const [employeeList, setEmployeeList] = useState<EmployeeItem[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<BUSINESS.RecordEmployeeParam[]>([]);
  const [initialCustomerData, setInitialCustomerData] = useState<CustomerResourceBasicInfoVo | null>(null);


  useEffect(() => {
    if (customerId) {
      const fetchDetails = async () => {
        setLoading(true);
        try {
          const response = await getCustomerResourceBasicCustomerResourceId({ customerResourceId: Number(customerId) });
          
          if (response) {
            const customerData: CustomerResourceBasicInfoVo = response;
            setInitialCustomerData(customerData); // Store for useEffect dependency
            setFormData({
              ...customerData,
              tags: Array.isArray(customerData.tags) ? customerData.tags : [], 
            });
          }
        } catch (error) {
          console.error("获取客户详情失败:", error);
          Taro.showToast({ title: '获取客户详情失败', icon: 'none' });
        } finally {
          setLoading(false);
        }
      };
      fetchDetails();
    } else {
      Taro.showToast({ title: '无效的客户ID', icon: 'none' });
      setLoading(false);
    }
  }, [customerId]);

  useEffect(() => {
    // 获取手艺人列表 (consultants)
		getEmployeeList({} as any).then(res => { // Assuming {} as any is placeholder for actual params if needed
			if (res?.data) { // User's code indicates getEmployeeList response has a 'data' property
				setEmployeeList(res.data.map(item => ({
					employeeId: item.employeeId,
					employeeNickname: item.employeeNickname,
					avatarUrl: item.avatarUrl,
					position: item.position
				})));
			}
		});
  }, [])

  // Effect to initialize selectedEmployees for EmployeeSelector when customer data and employee list are loaded
  useEffect(() => {
    if (initialCustomerData && employeeList.length > 0) {
      if (initialCustomerData.consultantId) {
        const consultantId = initialCustomerData.consultantId;
        // Ensure the consultant exists in the fetched list before trying to select
        const consultantExistsInList = employeeList.some(emp => emp.employeeId === consultantId);
        if (consultantExistsInList) {
           setSelectedEmployees([{ templateEmployeeId: consultantId }]);
        } else {
          setSelectedEmployees([]);
        }
        if (!formData.consultantName || formData.consultantId !== initialCustomerData.consultantId) {
          if (initialCustomerData.consultantName) {
             handleInputChange('consultantName', initialCustomerData.consultantName);
          } else if (consultantExistsInList) { // If name wasn't in initial data, try to get from employeeList
            const consultantFromList = employeeList.find(emp => emp.employeeId === consultantId);
            if (consultantFromList?.employeeNickname) {
              handleInputChange('consultantName', consultantFromList.employeeNickname);
            }
          }
        }
      } else {
        // No consultantId on customer, clear selection
        setSelectedEmployees([]);
        if (formData.consultantId || formData.consultantName) {
          handleInputChange('consultantId', undefined);
          handleInputChange('consultantName', undefined);
        }
      }
    }
  }, [initialCustomerData, employeeList]); // MODIFIED DEPENDENCY ARRAY

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (event) => {
    handleInputChange('birthday', event.detail.value);
  };

  const handleGenderChange = (value: number) => {
    handleInputChange('sex', value);
  };

  const openPicker = (type: 'memberSource' | 'intention') => { // Removed 'consultantId'
    let rangeData: { label: string, value: number }[] = [];
    if (type === 'memberSource') rangeData = SOURCE_CHANNEL_OPTIONS_DISPLAY;
    else if (type === 'intention') rangeData = INTENTION_OPTIONS;
    // Removed consultantId logic from here

    Taro.showActionSheet({
      itemList: rangeData.map(opt => opt.label),
      success: (res) => {
        if (res.tapIndex >= 0 && res.tapIndex < rangeData.length) {
          handleInputChange(type, rangeData[res.tapIndex].value);
        }
      },
    });
  };
  
  const handleTagToggle = (tag: string) => {
    const currentTags = formData.tags || [];
    const newTags = currentTags.includes(tag) 
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    handleInputChange('tags', newTags);
  };

  // This is the new handler for the EmployeeSelector
  const handleEmployeeSelect = (pickedEmployees: EmployeeItem[]) => {
    if (pickedEmployees && pickedEmployees.length > 0) {
      const selectedConsultant = pickedEmployees[0]; // Assuming single selection for consultant
      console.log(selectedConsultant, '----selectedConsultant')
      handleInputChange('consultantId', selectedConsultant.employeeId);
      handleInputChange('consultantName', selectedConsultant.employeeNickname);
      setSelectedEmployees([{ 
          templateEmployeeId: selectedConsultant.employeeId,
          // isPrimary: true, // Set if your EmployeeSelector/backend logic needs it
      }]);
    } else { // No employee selected or selection cleared
      handleInputChange('consultantId', undefined);
      handleInputChange('consultantName', undefined);
      setSelectedEmployees([]);
    }
    setIsEmployeeSelectorVisible(false);
  };

  const handleAvatarUpload = () => {
    Taro.chooseImage({
      count: 1, // 默认9
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        // Update avatar in formData for immediate preview
        handleInputChange('avatar', tempFilePath);
      },
      fail: (err) => {
        console.log('Choose image failed:', err);
        Taro.showToast({ title: '选择图片失败', icon: 'none' });
      }
    });
  };

  const handleSave = async () => {
    if (!customerId) { Taro.showToast({ title: '客户ID无效', icon: 'none' }); return; }
    if (!formData.nickName?.trim()) { Taro.showToast({ title: '请输入客户姓名', icon: 'none' }); return; }
    if (!formData.phoneNumber?.trim() || formData.phoneNumber.trim().length !== 11) { Taro.showToast({ title: '请输入11位有效手机号码', icon: 'none' }); return; }
    if (formData.sex === undefined) { Taro.showToast({ title: '请选择性别', icon: 'none' }); return; }

    setIsSubmitting(true);
    try {
      const payload = {
        id: Number(customerId),
        // Fields from CustomerResourceEditParam definition
        memberSource: formData.memberSource,
        referrerName: formData.referrerName,
        intention: formData.intention,
        consultantId: formData.consultantId,
        tags: formData.tags?.join(','), // API for edit expects string
        remark: formData.remark,

        // Additional basic info fields from the form (potentially not part of CustomerResourceEditParam)
        nickName: formData.nickName,
        phoneNumber: formData.phoneNumber,
        sex: formData.sex,
        birthday: formData.birthday,
        avatar: formData.avatar, // Assuming API can handle avatar URL string
      };
      const response = await putCustomerResource(payload as any);

      // Check if response is a valid object and has a success code
      
      if (response) {
        Taro.showToast({ title: '保存成功', icon: 'success' });
        Taro.setStorageSync('refreshCustomerDetailOnReturn', true);
        Taro.setStorageSync('refreshCustomerListOnReturn', true); 
        setTimeout(() => Taro.navigateBack(), 1500);
      }
    } catch (error: any) { // Consider typing 'error' more specifically if error structure is known
      console.error("保存客户信息失败:", error);
      // Use error.msg if available from a structured error response, otherwise a generic message
      Taro.showToast({ title: error?.msg || '保存操作失败', icon: 'none' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCurrentDate = () => {
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  };
  
  if (loading) {
    return <PageWithNav title="编辑客资"><View style={{padding: '20px', textAlign: 'center'}}>加载中...</View></PageWithNav>;
  }

  return (
    <PageWithNav title="编辑客资">
      <ScrollView scrollY className={styles.scrollView}>
        <View className={styles.pageContainer}>
          <View className={styles.section}>
            <Text className={styles.sectionTitle}>基本信息</Text>
            <View className={styles.formItem}>
              <Text className={styles.label}>头像</Text>
              <View className={styles.valueArea} onClick={handleAvatarUpload}>
                <Image src={formData.avatar || 'https://via.placeholder.com/40?text=...'} className={styles.avatar} />
              </View>
            </View>
            <View className={styles.formItem}>
              <Text className={styles.label}>客户姓名<Text className={styles.required}>*</Text></Text>
              <Input className={styles.input} placeholder="请输入客户姓名" value={formData.nickName || ''} onInput={(e) => handleInputChange('nickName', e.detail.value)} />
            </View>
            <View className={styles.formItem}>
              <Text className={styles.label}>手机号码<Text className={styles.required}>*</Text></Text>
              <Input className={styles.input} type="number" placeholder="请输入11位有效手机号码" value={formData.phoneNumber || ''} maxlength={11} onInput={(e) => handleInputChange('phoneNumber', e.detail.value)} />
            </View>
            <Text className={styles.phoneNote}>注：该手机号用于登录客户端</Text>
            <View className={styles.formItem}>
              <Text className={styles.label}>性别<Text className={styles.required}>*</Text></Text>
              <View className={styles.genderSelector}>
                {GENDER_OPTIONS.map(opt => (
                  <View key={opt.value} className={`${styles.genderOption} ${formData.sex === opt.value ? styles.selected : ''}`} onClick={() => handleGenderChange(opt.value)}>
                    {opt.label}
                  </View>
                ))}
              </View>
            </View>
            <Picker mode="date" onChange={handleDateChange} value={formData.birthday || getCurrentDate()} end={getCurrentDate()}>
              <View className={styles.formItem}>
                <Text className={styles.label}>生日</Text>
                <View className={styles.valueArea}>
                  <Text className={formData.birthday ? styles.pickerValue : styles.placeholder}>{formData.birthday || '请选择生日'}</Text>
                  <Text className={styles.arrow}>&gt;</Text>
                </View>
              </View>
            </Picker>
          </View>

          <View className={styles.section}>
            <Text className={styles.sectionTitle}>跟进信息</Text>
            <View className={styles.formItem} onClick={() => openPicker('memberSource')}>
              <Text className={styles.label}>来源渠道</Text>
              <View className={styles.valueArea}>
                <Text className={formData.memberSource !== undefined ? styles.pickerValue : styles.placeholder}>{SOURCE_CHANNEL_OPTIONS_DISPLAY.find(o => o.value === formData.memberSource)?.label || '请选择'}</Text>
                <Text className={styles.arrow}>&gt;</Text>
              </View>
            </View>
            <View className={styles.formItem}>
              <Text className={styles.label}>推荐人</Text>
              <Input className={styles.input} placeholder="请输入推荐人" value={formData.referrerName || ''} onInput={(e) => handleInputChange('referrerName', e.detail.value)} />
            </View>
            <View className={styles.formItem} onClick={() => openPicker('intention')}>
              <Text className={styles.label}>意向度</Text>
              <View className={styles.valueArea}>
                <Text className={formData.intention !== undefined ? styles.pickerValue : styles.placeholder}>{INTENTION_OPTIONS.find(o => o.value === formData.intention)?.label || '请选择'}</Text>
                <Text className={styles.arrow}>&gt;</Text>
              </View>
            </View>
            <View className={styles.formItem} onClick={() => setIsEmployeeSelectorVisible(true)}>
              <Text className={styles.label}>顾问</Text>
              <View className={styles.valueArea}>
                <Text className={formData.consultantName ? styles.pickerValue : styles.placeholder}>{formData.consultantName || '请选择顾问'}</Text>
                <Text className={styles.arrow}>&gt;</Text>
              </View>
            </View>
            <View className={`${styles.formItem} ${styles.formItemColumn}`}>
              <Text className={styles.label}>标签</Text>
              <View className={styles.tagContainer}>
                {AVAILABLE_TAGS.map(tag => (
                  <View key={tag} className={`${styles.tagOption} ${(formData.tags || []).includes(tag) ? styles.selected : ''}`} onClick={() => handleTagToggle(tag)}>
                    {tag}
                  </View>
                ))}
              </View>
            </View>
          </View>

          <View className={styles.section} style={{ marginBottom: '10px' }}>
            <Text className={styles.sectionTitle}>备注</Text>
            <Textarea className={styles.textarea} placeholder="请输入备注" value={formData.remark || ''} onInput={(e) => handleInputChange('remark', e.detail.value)} maxlength={200} />
            <View className={styles.charCount}>{(formData.remark || '').length}/200</View>
          </View>
        </View>
      </ScrollView>
      <View className={styles.footer}>
        {/* <View className={`${styles.footerButton} ${styles.cardButton}`} onClick={() => Taro.showToast({title:'办卡功能待实现', icon:'none'})}>办卡</View> */}
        <View 
          className={`${styles.footerButton} ${styles.saveButtonFullWidth}`}
          onClick={!isSubmitting ? handleSave : undefined}
        >
          {isSubmitting ? '保存中...' : '保存'}
        </View>
      </View>
      {/* 手艺人选择器 (Used for Consultants here) */}
			<EmployeeSelector
				visible={isEmployeeSelectorVisible}
				onClose={() => setIsEmployeeSelectorVisible(false)}
				onSelect={handleEmployeeSelect} // connect the new handler
				selectedEmployees={employeeList.filter(item => 
					selectedEmployees.some(selected => selected.templateEmployeeId === item.employeeId)
				)}
				employees={employeeList}
			/>
    </PageWithNav>
  );
};

export default EditCustomerPage; 