import React, { useState, useEffect } from 'react';
import { View, Text, Textarea, Picker } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';

import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import { postCustomerResourceAddFollowUpRecord } from '@/service/business/customerResourceController';
// import type { FollowUpRecordAddParam } from '@/service/business/typings'; // Removed import to avoid lint error

import styles from './add-follow-up-page.module.scss';

// Manually define the interface based on API requirements
interface FollowUpRecordAddParam {
  memberResourcesId: number;
  content?: string;
  intention: number;
  followUpMethod: number;
  nextFollowTime?: string;
}

interface Option {
  label: string;
  value: number;
}

const INTENTION_OPTIONS: Option[] = [
  { label: '未知', value: 0 },
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 },
];

const FOLLOW_UP_METHOD_OPTIONS: Option[] = [
  { label: '电话跟进', value: 0 },
  { label: '短信跟进', value: 1 },
  { label: '微信跟进', value: 2 },
  { label: '其他', value: 3 },
];

// Default form data structure based on API
const initialFormData: Partial<FollowUpRecordAddParam> = {
  content: '',
  intention: 0, // Default to '未知'
  followUpMethod: 0, // Default to '电话跟进'
  nextFollowTime: '' // Initialize as empty, user will pick
};

const AddFollowUpPage: React.FC = () => {
  const router = useRouter();
  const [customerResourceId, setCustomerResourceId] = useState<number | null>(null);
  const [formData, setFormData] = useState<Partial<FollowUpRecordAddParam>>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current date in YYYY-MM-DD format for Picker default or range
  const getCurrentDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  useEffect(() => {
    if (router.params.id) {
      setCustomerResourceId(Number(router.params.id));
    }
  }, [router.params.id]);

  const handleInputChange = (field: keyof FollowUpRecordAddParam, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (event) => {
    handleInputChange('nextFollowTime', event.detail.value);
  };

  const handleSave = async () => {
    if (!customerResourceId) {
      Taro.showToast({ title: '客户ID缺失', icon: 'none' });
      return;
    }
    if (!formData.content?.trim()) {
      Taro.showToast({ title: '请输入沟通内容', icon: 'none' });
      return;
    }
    // Intention and followUpMethod are already numbers due to picker logic
    if (formData.intention === undefined || formData.followUpMethod === undefined) {
        Taro.showToast({ title: '请选择意向度和跟进方式', icon: 'none' });
        return;
    }

    setIsSubmitting(true);
    try {
      const payload: FollowUpRecordAddParam = {
        memberResourcesId: customerResourceId,
        content: formData.content,
        intention: formData.intention as number, // Already a number
        followUpMethod: formData.followUpMethod as number, // Already a number
        nextFollowTime: formData.nextFollowTime || undefined, // Send undefined if empty
      };
      
      const res = await postCustomerResourceAddFollowUpRecord(payload);
      // Assuming res is RLong, and a truthy res (or res.data if applicable) means success
      if (res) { // Adjust this check based on actual API response structure for success
        Taro.showToast({ title: '保存成功', icon: 'success' });
        Taro.setStorageSync('refreshFollowUpOnDetail', true); // Set flag before navigating back
        setTimeout(() => {
            Taro.navigateBack();
        }, 1500);
      } else {
        // Attempt to get error message from res if available
        const errorMsg = (res as any)?.msg || '保存失败，请重试';
        Taro.showToast({ title: errorMsg, icon: 'none' });
      }
    } catch (error) {
      console.error('保存跟进记录失败:', error);
      Taro.showToast({ title: '保存失败，网络错误', icon: 'none' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openPicker = (type: 'intention' | 'followUpMethod') => {
    let range: Option[] = [];
    let currentLabel = '';

    if (type === 'intention') {
      range = INTENTION_OPTIONS;
      currentLabel = range.find(opt => opt.value === formData.intention)?.label || '请选择';
    } else if (type === 'followUpMethod') {
      range = FOLLOW_UP_METHOD_OPTIONS;
      currentLabel = range.find(opt => opt.value === formData.followUpMethod)?.label || '请选择';
    }

    Taro.showActionSheet({
      itemList: range.map(opt => opt.label),
      success: function (res) {
        if (res.tapIndex >= 0 && res.tapIndex < range.length) {
            handleInputChange(type, range[res.tapIndex].value);
        }
      },
      fail: function (res) {
        console.log(res.errMsg);
      }
    });
  };

  return (
    <PageWithNav title="添加跟进记录">
      <View className={styles.pageContainer}>
        <View className={styles.formSection}>
          <Textarea
            className={styles.communicationInput}
            placeholder="请输入沟通内容"
            value={formData.content}
            onInput={(e) => handleInputChange('content', e.detail.value)}
            maxlength={200} // Maxlength is from design, API doesn't specify
            autoHeight
          />
          <View className={styles.charCount}>{formData.content?.length || 0}/200</View>
          {/* Media buttons removed as per user changes */}
        </View>

        <View className={styles.optionsSection}>
          <View className={styles.optionItem} onClick={() => openPicker('intention')}>
            <Text className={styles.optionLabel}>意向度</Text>
            <View className={styles.optionValueContainer}>
              <Text className={styles.optionValue}>{
                INTENTION_OPTIONS.find(opt => opt.value === formData.intention)?.label || '请选择'
              }</Text>
              {/* Arrow icon removed */}
            </View>
          </View>
          <View className={styles.optionItem} onClick={() => openPicker('followUpMethod')}>
            <Text className={styles.optionLabel}>跟进方式</Text>
            <View className={styles.optionValueContainer}>
              <Text className={styles.optionValue}>{
                FOLLOW_UP_METHOD_OPTIONS.find(opt => opt.value === formData.followUpMethod)?.label || '请选择'
              }</Text>
              {/* Arrow icon removed */}
            </View>
          </View>
          {/* followUpStatus removed as it's not in API params */}
          <Picker mode="date" onChange={handleDateChange} value={formData.nextFollowTime || getCurrentDate()} start={getCurrentDate()}>
            <View className={styles.optionItem}>
              <Text className={styles.optionLabel}>下次跟进时间</Text>
              <View className={styles.optionValueContainer}>
                <Text className={styles.optionValue}>{formData.nextFollowTime || '请选择'}</Text>
                {/* Arrow icon removed */}
              </View>
            </View>
          </Picker>
        </View>

        <View className={styles.saveButtonContainer}>
          <View className={`${styles.saveButton} ${isSubmitting ? styles.disabled : ''}`} onClick={!isSubmitting ? handleSave : undefined}>
            {isSubmitting ? '提交中...' : '保存'}
          </View>
        </View>
      </View>
    </PageWithNav>
  );
};

export default AddFollowUpPage; 