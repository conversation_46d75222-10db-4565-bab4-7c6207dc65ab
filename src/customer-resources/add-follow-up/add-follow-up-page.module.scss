.pageContainer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 44px - env(safe-area-inset-top)); // 减去导航栏高度
  background-color: #f7f7f7;
}

.formSection {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.communicationInput {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 5px;
}

.charCount {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-bottom: 10px;
}

.mediaButtons {
  display: flex;
  align-items: center;
}

.mediaButton {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #eee;
  border-radius: 15px;
  margin-right: 10px;
  font-size: 13px;
  color: #666;
  background-color: #f7f8fa;

  .mediaIcon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
}

.optionsSection {
  background-color: #fff;
}

.optionItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 15px;

  &:last-child {
    border-bottom: none;
  }

  .optionLabel {
    color: #333;
  }

  .optionValueContainer {
    display: flex;
    align-items: center;
    color: #888;
  }
  
  .optionValue {
    margin-right: 8px;
  }

  .arrowIcon {
    width: 12px;
    height: 12px;
  }
}

.saveButtonContainer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px 20px calc(15px + env(safe-area-inset-bottom));
  background-color: #f7f7f7; // 与页面背景色一致或稍作区分
  // border-top: 1px solid #eee;
}

.saveButton {
  background-color: #FFC300; // 主题黄色
  color: #fff;
  text-align: center;
  padding: 12px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  &:active {
    background-color: #e6b000; // 点击效果
  }
} 