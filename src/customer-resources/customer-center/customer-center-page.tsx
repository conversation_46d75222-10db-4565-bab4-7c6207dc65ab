import Taro from '@tarojs/taro';
import React, { useState, useEffect } from "react";
import { View, Text, ScrollView, Picker, Input } from "@tarojs/components";
import PageWithNav from "@/view/component/page-with-nav/page-with-nav.component";
import { postCustomerResourceList } from '@/service/business/customerResourceController';
// import type { RListCustomerResourceListVo } from '@/service/business/typings';
import styles from "./customer-center-page.module.scss";

// 客资状态枚举
const STATUS_LIST = [
  { label: "全部", value: -1 },
  { label: "待联系", value: 0 },
  { label: "已联系", value: 1 },
  { label: "已到店", value: 2 },
];

// 意向度枚举
const INTENTION_MAP = {
  0: { label: "无", class: styles.cardTag + " " + styles.unknown },
  1: { label: "低", class: styles.cardTag + " " + styles.low },
  2: { label: "中", class: styles.cardTag + " " + styles.mid },
  3: { label: "高", class: styles.cardTag + " " + styles.high },
};

// 会员来源枚举
const SOURCE_MAP = {
  1: "上门客人",
  2: "员工带人",
  3: "抖音客户",
};

type DateOption = {
  label: string;
  value: string;
};

type FilterOption = DateOption | string;

const FILTERS = [
  {
    key: 'date',
    label: '全部',
    options: [
      { label: '全部', value: 'all' },
      { label: '近七日', value: 'last7days' },
      { label: '今日', value: 'today' },
      { label: '本月', value: 'thisMonth' }
    ] as DateOption[],
  },
  {
    key: 'source',
    label: '全部来源',
    options: ['全部来源', ...Object.values(SOURCE_MAP)],
  },
  {
    key: 'intention',
    label: '意向度',
    options: ['全部意向度', ...Object.values(INTENTION_MAP).map(item => item.label)],
  },
];

const CustomerCenterPage: React.FC = () => {
  const [status, setStatus] = useState(-1);
  const [filterIndexes, setFilterIndexes] = useState([0, 0, 0]);
  const [search, setSearch] = useState("");
  const [list, setList] = useState<BUSINESS.CustomerResourceListVo[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取列表数据
  const fetchList = async () => {
    try {
      setLoading(true);
      const params: BUSINESS.CustomerResourceListParam = {
        searchKey: search || undefined,
        statuses: status === -1 ? undefined : [status],
      };
      
      // 处理日期过滤
      const dateOption = FILTERS[0].options[filterIndexes[0]] as DateOption;
      if (dateOption.value !== 'all') {
        const now = new Date();
        const endDate = now.toISOString().split('T')[0];
        let startDate = '';
        
        switch (dateOption.value) {
          case 'today':
            startDate = endDate;
            break;
          case 'thisMonth':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
            break;
          case 'last7days':
            startDate = new Date(now.setDate(now.getDate() - 7)).toISOString().split('T')[0];
            break;
        }
        
        params.startDate = startDate;
        params.endDate = endDate;
      }

      const res = await postCustomerResourceList(params);
      if (res) {
        let filteredList = res;
        
        // 处理来源过滤
        if (filterIndexes[1] !== 0) {
          const sourceValue = Object.entries(SOURCE_MAP).find(([_, label]) => 
            label === FILTERS[1].options[filterIndexes[1]]
          )?.[0];
          if (sourceValue) {
            filteredList = filteredList.filter(item => 
              item.memberSource === Number(sourceValue)
            );
          }
        }
        
        // 处理意向度过滤
        if (filterIndexes[2] !== 0) {
          const intentionValue = Object.entries(INTENTION_MAP).find(([_, map]) => 
            map.label === FILTERS[2].options[filterIndexes[2]]
          )?.[0];
          if (intentionValue) {
            filteredList = filteredList.filter(item => 
              item.intention === Number(intentionValue)
            );
          }
        }
        
        setList(filteredList);
      }
    } catch (error) {
      console.error('获取客资列表失败:', error);
      Taro.showToast({ title: '获取数据失败', icon: 'none' });
    } finally {
      setLoading(false);
    }
  };

  // 监听过滤条件变化
  useEffect(() => {
    fetchList();
  }, [status, search, filterIndexes]);

  // 处理下拉选择
  const handleFilterChange = (i, e) => {
    const newIndexes = [...filterIndexes];
    newIndexes[i] = e.detail.value;
    setFilterIndexes(newIndexes);
  };

  // 处理搜索
  const handleSearch = (e) => {
    setSearch(e.detail.value);
  };

  // 格式化时间
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  // 拨打电话
  const makePhoneCall = (phone?: string) => {
    if (!phone) {
      Taro.showToast({ title: '电话号码不存在', icon: 'none' });
      return;
    }
    Taro.makePhoneCall({
      phoneNumber: phone,
      fail: (err) => {
        console.error('拨打电话失败:', err);
        Taro.showToast({ title: '拨打电话失败', icon: 'none' });
      }
    });
  };

  return (
    <PageWithNav title="客资中心">
      <View className={styles.customerCenterContainer}>
        {/* 顶部搜索框 */}
        <View className={styles.searchBar}>
          <Text className={styles.searchIcon}>🔍</Text>
          <Input
            className={styles.searchInput}
            placeholder="搜索姓名/联系方式"
            value={search}
            onInput={handleSearch}
            confirmType="search"
          />
          <Text className={styles.filterIcon}>筛</Text>
        </View>
        
        {/* 顶部过滤条件区 */}
        <View className={styles.filterBar}>
          {FILTERS.map((filter, i) => (
            <Picker
              mode="selector"
              range={filter.options}
              rangeKey={i === 0 ? 'label' : undefined}
              value={filterIndexes[i]}
              onChange={e => handleFilterChange(i, e)}
              key={filter.key}
            >
              <View className={styles.filterItem + (filterIndexes[i] !== 0 ? ' ' + styles.active : '')}>
                {i === 0 
                  ? String((filter.options[filterIndexes[i]] as DateOption).label)
                  : String(filter.options[filterIndexes[i]])}
                <Text className={styles.filterArrow}>▼</Text>
              </View>
            </Picker>
          ))}
        </View>

        {/* 顶部状态筛选 横向滚动 */}
        <ScrollView className={styles.statusTabs} scrollX >
          {STATUS_LIST.map(tab => (
            <View
              key={tab.value}
              className={styles.statusTab + (status === tab.value ? " " + styles.active : "")}
              onClick={() => setStatus(tab.value)}
            >
              {tab.label}
            </View>
          ))}
        </ScrollView>

        {/* 客资卡片列表 */}
        <View className={styles.cardList}>
          {loading ? (
            <View className={styles.loading}>加载中...</View>
          ) : list.length === 0 ? (
            <View className={styles.empty}>暂无数据</View>
          ) : (
            list.map(item => (
              <View className={styles.cardItem} key={item.id}>
                <View className={styles.cardHeader}>
                  <Text className={styles.cardTitle}>{item.nickName}</Text>
                  <Text className={INTENTION_MAP[item.intention || 0]?.class}>
                    {INTENTION_MAP[item.intention || 0]?.label}
                  </Text>
                </View>
                <Text className={styles.cardStatus} style={{ color: getStatusColor(item.status) }}>
                  {STATUS_LIST.find(s => s.value === item.status)?.label}
                </Text>
                <View className={styles.cardInfo} onClick={() => makePhoneCall(item.phonenumberMasked)}>
                  <Text className={styles.phoneNumber}>{item.phonenumberMasked}</Text>
                </View>
                <View className={styles.cardInfoRow}>
                  <View className={styles.cardInfo}>客资来源：{SOURCE_MAP[item.memberSource || 1]}</View>
                  <View className={styles.cardInfo}>客资平台：{item.businessName}</View>
                </View>
                <View className={styles.cardInfo}>留资时间：{formatDate(item.leadTime || '')}</View>
                <View className={styles.cardActions}>
                  <View 
                    className={styles.moveBlackBtn + ' ' + styles.actionBtn} 
                    onClick={() => Taro.navigateTo({ url: `/customer-resources/black-list-reason/black-list-reason-page?id=${item.id}` })}
                  >
                    移入黑名单
                  </View>
                  <View 
                    className={styles.actionBtn} 
                    onClick={() => Taro.navigateTo({ url: `/customer-resources/customer-detail/customer-detail-page?id=${item.id}` })}
                  >
                    客资详情
                  </View>
                </View>
              </View>
            ))
          )}
        </View>
      </View>
    </PageWithNav>
  );
};

// 获取状态对应的颜色
const getStatusColor = (status?: number) => {
  switch (status) {
    case 0: return "#FF5F2B"; // 待联系
    case 1: return "#12D230"; // 已联系
    case 2: return "#008DFF"; // 已到店
    default: return "#999999";
  }
};

export default CustomerCenterPage; 
