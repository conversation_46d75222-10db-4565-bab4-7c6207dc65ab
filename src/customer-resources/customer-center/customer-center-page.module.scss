.customerCenterContainer {
  background: #f7f8fa;
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px 0 0 0;
}

.statusTabs {
  display: flex;
  flex-direction: row;
  background: #f7f8fa;
  border-radius: 16px;
  margin: 0 0 0 8px;
  overflow-x: auto;
  white-space: nowrap;
  padding: 4px 0 4px 4px;
  &::-webkit-scrollbar { display: none; }
}

.statusTab {
  display: inline-block;
  min-width: 64px;
  text-align: center;
  padding: 4px 12px;
  height: 30px;
  font-size: 14px;
  color: #999;
  background: transparent;
  margin-right: 8px;
  border-radius: 16px;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
  position: relative;
  z-index: 1;
}

.statusTab.active {
  color: #ffb300;
  font-weight: bold;
  background: #fffbe6;
  border-radius: 16px;
  box-shadow: none;
}

.statusTab:last-child {
  margin-right: 0;
}

.cardList {
  flex: 1;
  overflow-y: auto;
  margin: 0 16px;
  padding-bottom: 80px;
}

.cardItem {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-bottom: 16px;
  padding: 16px;
  position: relative;
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  margin-right: 8px;
}

.cardTag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px;
  margin-right: 8px;
  &.mid { background: #ffe58f; color: #ad6800; }
  &.low { background: #d9f7be; color: #389e0d; }
  &.high { background: #ffccc7; color: #cf1322; }
  &.unknown { background: #f4f4f4; color: #999; }
}

.cardStatus {
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 14px;
  font-weight: bold;
}

.cardInfo {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.cardInfoRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  .cardInfo {
    flex: 1;
    margin-bottom: 0;
    
    &:first-child {
      margin-right: 16px;
    }
  }
}

.phoneNumber {
  font-size: 14px;
  color: #999;
  cursor: pointer;
  
  &:active {
    opacity: 0.7;
  }
}

.cardActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.actionBtn {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 8px;
  background: #fff;
  color: #ffb300;
  border: 1px solid #ffe58f;
  margin-left: 8px;
}

.moveBlackBtn {
  background: #fff;
  color: #999;
  border: 1px solid #e4e4e4;
}

.filterBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 8px 8px 8px;
  background: transparent;
  margin-bottom: 2px;
  overflow-x: auto;
  white-space: nowrap;
}

.filterItem {
  display: inline-flex;
  align-items: center;
  padding: 4px 16px;
  font-size: 14px;
  color: #ffb300;
  background: #fff;
  border-radius: 12px;
  // border: 1px solid #fffbe6;
  font-weight: 500;
  margin-right: 12px;
  transition: background 0.2s, color 0.2s, border 0.2s;
  cursor: pointer;
  
  // &:first-child {
  //   margin-left: 0;
  // }
}

.filterItem.active {
  // background: #fffbe6;
  color: #ffb300;
  // border: 1px solid #ffecb3;
  font-weight: bold;
}

// .filterItem:last-child {
//   margin-right: 0;
// }

.filterArrow {
  font-size: 12px;
  margin-left: 4px;
  color: #ffb300;
}

.searchBar {
  display: flex;
  align-items: center;
  background: #fafafa;
  border-radius: 16px;
  padding: 0 12px;
  margin: 12px 12px 8px 12px;
  height: 38px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
}

.searchIcon {
  font-size: 18px;
  color: #ffb300;
  margin-right: 8px;
}

.searchInput {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #333;
  outline: none;
}

.filterIcon {
  font-size: 18px;
  color: #999;
  margin-left: 8px;
} 