.pageBg {
  height: 100vh;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f7f8fa 0%, #fff 100%);
  padding-bottom: 0;
}

.headerCard {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 16px;
  margin: 16px 16px 0 16px;
  padding: 20px 20px 16px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  position: relative;
  min-height: 120px;
}
.headerLeft {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
}
.avatar {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: #f7f7f7;
  margin-left: 18px;
  flex-shrink: 0;
  margin-top: 0;
}
.headerInfo {
  flex: 1;
}
.nameRow {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.name {
  font-size: 16px;
  font-weight: bold;
  margin-right: 8px;
}
.tag {
  font-size: 12px;
  color: #fff;
  background: #ffb300;
  border-radius: 8px;
  padding: 2px 10px;
  margin-right: 8px;
}
.status {
  font-size: 12px;
  font-weight: bold;
  border-radius: 8px;
  padding: 2px 10px;
  margin-right: 8px;
  background: #e6f7e6;
}
.consultant {
  font-size: 12px;
  color: #999;
}
.actionRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 18px;
  gap: 0;
}
.actionLeft {
  display: flex;
  gap: 8px;
}
.actionRight {
  display: flex;
  gap: 0;
}
.actionBtn {
  flex: none;
  display: flex;
  align-items: center;
  background: #fff;
  color: #ffb300;
  border: 2px solid #ffde7b;
  border-radius: 22px;
  font-size: 12px;
  font-weight: bold;
  padding: 0 12px;
  height: 26px;
  line-height: 26px;
  margin-right: 0;
  box-shadow: none;
  transition: background 0.2s, color 0.2s;
}
.actionBtn:last-child {
  margin-right: 0;
}
.iconEdit {
  display: inline-block;
  width: 20px;
  height: 20px;
  // background: url('@/assets/image/common/edit-yellow.png') no-repeat center/contain;
  margin-right: 6px;
}
.iconPhone {
  display: inline-block;
  width: 20px;
  height: 20px;
  // background: url('@/assets/image/common/phone-yellow.png') no-repeat center/contain;
  margin-right: 6px;
}
.iconCard {
  display: inline-block;
  width: 20px;
  height: 20px;
  // background: url('@/assets/image/common/card-yellow.png') no-repeat center/contain;
  margin-right: 6px;
}
.tabsRow {
  display: flex;
  margin: 24px 16px 0 16px;
  border-bottom: 2px solid #f7f7f7;
  flex-shrink: 0;
}
.tab {
  flex: 1;
  text-align: center;
  font-size: 16px;
  color: #999;
  padding-bottom: 10px;
  position: relative;
  font-weight: 500;
}
.tab.active {
  color: #ffb300;
  font-weight: bold;
}
.tab.active::after {
  content: '';
  display: block;
  width: 32px;
  height: 3px;
  background: #ffb300;
  border-radius: 2px;
  position: absolute;
  left: 50%;
  bottom: -2px;
  transform: translateX(-50%);
}
.timeline {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  margin: 16px 16px 0 16px;
}
.addFollow {
  color: #ffb300;
  background: #fffbe6;
  border-radius: 22px;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  margin-bottom: 18px;
  font-weight: bold;
  border: 1px dashed #ffb300;
}
.timelineItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
}
.timelineDot {
  width: 12px;
  height: 12px;
  background: #ffb300;
  border-radius: 50%;
  margin-right: 16px;
  margin-top: 6px;
  flex-shrink: 0;
  position: relative;
}
.timelineDot::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 115%;
  transform: translateX(-50%);
  width: 2px;
  height: 120px;
  background: repeating-linear-gradient(
    to bottom,
    #ffb300,
    #ffb300 4px,
    transparent 4px,
    transparent 8px
  );
  z-index: 1;
}
.timelineItem:last-child .timelineDot::after {
  display: none;
}
.timelineContent {
  background: #fff;
  border-radius: 12px;
  padding: 12px 16px;
  flex: 1;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.timelineTitle {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.timelineTime {
  font-size: 12px;
  color: #bbb;
  margin-bottom: 4px;
}
.timelineDesc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  white-space: pre-line;
}
.timelineStore {
  font-size: 12px;
  color: #bbb;
}

// Styles for Order List Tab Content
.orderListContainer {
  padding: 10px 15px;
}

.orderItem {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.orderServiceItem {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0; 

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.orderServiceIcon {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  margin-right: 12px;
  // background-color: #FFD700; // Example from mock, can be removed if icons are actual images
}

.orderServiceDetails {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.orderServiceName {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.orderServiceStaff {
  font-size: 13px;
  color: #888;
}

.orderServicePrice {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.orderSummary {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e8e8e8; // Dashed line for summary section
}

.orderTotalRow {
  display: flex;
  justify-content: space-between; // Aligns "合计" and amount
  align-items: center;
  margin-bottom: 8px;
}

.orderTotalLabel {
  font-size: 14px;
  color: #555;
}

.orderTotalAmount {
  font-size: 16px;
  color: #FF6A00; // Orange color for total amount
  font-weight: bold;
}

.orderConsumptionTime {
  font-size: 13px;
  color: #aaa;
  text-align: right; // Align consumption time to the right
}

.noOrders {
  text-align: center;
  color: #999;
  padding: 20px;
  font-size: 14px;
}

.loadingTimeline { // Can be reused for loading orders too
  text-align: center;
  padding: 20px;
  color: #999;
} 