import React, { useEffect, useState } from "react";
import { View, Text, Image } from "@tarojs/components";
import Taro, { useRouter, useDidShow } from '@tarojs/taro';
import PageWithNav from "@/view/component/page-with-nav/page-with-nav.component";
import memberUser from '@/assets/image/common/member-user.png';
import { getCustomerResourceCustomerResourceId, getCustomerResourceFollowUpRecordCustomerResourceId } from '@/service/business/customerResourceController';
import { getMemberRecordDetailListNew } from '@/service/business/huiyuanxiangqingxiaofeimingxi';
// import type { CustomerResourceInfoVo, CustomerResourceFollowUpRecordVo, TMemberOperRecordDetailVo } from '@/service/business/typings'; // Temporarily commented out
import styles from "./customer-detail-page.module.scss";

// 意向度映射
const INTENTION_MAP = {
  0: { text: "无", color: "#999999" }, // Default/placeholder color
  1: { text: "低", color: "#FF4D4F" }, // Example color for low
  2: { text: "中", color: "#FAAD14" }, // Example color for medium
  3: { text: "高", color: "#52C41A" }, // Example color for high
};

// 状态映射
const STATUS_MAP = {
  0: { text: "待联系", color: "#FAAD14" }, // Example color
  1: { text: "已联系", color: "#1890FF" }, // Example color
  2: { text: "已到店", color: "#52C41A" }, // Example color
};


const CustomerDetailPage: React.FC = () => {
  const router = useRouter();
  const [detail, setDetail] = useState<any | null>(null); // Using any due to typing import issue
  const [followUpRecords, setFollowUpRecords] = useState<any[]>([]); // Using any
  const [orderRecords, setOrderRecords] = useState<any[]>([]); // Initialize with empty array
  const [loading, setLoading] = useState(true);
  const [loadingFollowUp, setLoadingFollowUp] = useState(true);
  const [loadingOrders, setLoadingOrders] = useState(false); // New loading state for orders
  const [activeTab, setActiveTab] = useState('followUp'); // 'followUp' or 'orders'
  const [refreshNonce, setRefreshNonce] = useState(0); // State to trigger useEffect refresh

  useEffect(() => {
    const customerId = router.params.id;
    if (!customerId) {
      Taro.showToast({ title: '缺少客户ID', icon: 'none' });
      setLoading(false);
      setLoadingFollowUp(false);
      setLoadingOrders(false);
      return;
    }

    const fetchCustomerDetail = async () => {
      setLoading(true);
      try {
        const res = await getCustomerResourceCustomerResourceId({
          customerResourceId: Number(customerId)
        });
        if (res) { // Assuming res is RCustomerResourceInfoVo
          setDetail(res); // Access data property
        }
      } catch (error) {
        console.error('获取客户详情失败:', error);
        Taro.showToast({ title: '获取客户详情失败', icon: 'none' });
      } finally {
        setLoading(false);
      }
    };

    const fetchFollowUpRecords = async () => {
      setLoadingFollowUp(true);
      try {
        const res = await getCustomerResourceFollowUpRecordCustomerResourceId({
          customerResourceId: Number(customerId)
        });
        if (res) {
          setFollowUpRecords(res || []);
        }
      } catch (error) {
        console.error('获取跟进记录失败:', error);
        Taro.showToast({ title: '获取跟进记录失败', icon: 'none' });
      } finally {
        setLoadingFollowUp(false);
      }
    };

    const fetchOrderRecords = async () => {
      if (!customerId) return;
      setLoadingOrders(true);
      try {
        const res = await getMemberRecordDetailListNew({
          memberId: customerId,
          pageNum: 1, // Default page number
          pageSize: 20 // Default page size
        } as any); // Using 'as any' for now due to potential type complexities
        if (res && res.data) { // Assuming data is in res.data based on other usages
          setOrderRecords(res.data || []);
        } else {
          setOrderRecords([]);
        }
      } catch (error) {
        console.error('获取订单记录失败:', error);
        Taro.showToast({ title: '获取订单记录失败', icon: 'none' });
      } finally {
        setLoadingOrders(false);
      }
    };

    fetchCustomerDetail();
    if (activeTab === 'followUp') {
        fetchFollowUpRecords();
    } else if (activeTab === 'orders') {
        fetchOrderRecords();
    }
    // TODO: Add fetching logic for order records when that tab is active and data isn't mock

  }, [router.params.id, activeTab, refreshNonce]);

  useDidShow(() => {
    const shouldRefresh = Taro.getStorageSync('refreshFollowUpOnDetail');
    if (shouldRefresh) {
      Taro.removeStorageSync('refreshFollowUpOnDetail');
      if (activeTab === 'followUp') {
        setRefreshNonce(prev => prev + 1); // Trigger useEffect
      }
    }
  });

  const getIntentionText = (intentionValue?: number) => {
    return INTENTION_MAP[intentionValue || 0]?.text || '未知';
  };

  const getStatusInfo = (statusValue?: number) => {
    return STATUS_MAP[statusValue || 0] || { text: '未知', color: '#999999' };
  };
  
  const statusInfo = getStatusInfo(detail?.status);
  const intentionText = getIntentionText(detail?.intention);

  const navigateToAddFollowUp = () => {
    if (detail?.id) {
      Taro.navigateTo({
        url: `/customer-resources/add-follow-up/add-follow-up-page?id=${detail.id}`,
      });
    }
  };

  const navigateToEditCustomer = () => {
    if (detail?.id) {
      Taro.navigateTo({
        url: `/customer-resources/edit-customer/edit-customer-page?id=${detail.id}`,
      });
    }
  };

  const handleMakeCall = () => {
    const phoneNumber = detail?.phoneNumber; // Access phoneNumber from detail state
    if (phoneNumber && String(phoneNumber).trim().length > 0) { // Basic check for non-empty
      Taro.makePhoneCall({
        phoneNumber: String(phoneNumber).trim(),
        fail: (err) => {
          console.error('Make phone call failed:', err);
          // Consider a more user-friendly error message or specific handling
          Taro.showToast({ title: '拨号功能暂不可用或号码无效', icon: 'none' });
        }
      });
    } else {
      Taro.showToast({ title: '客户手机号码未提供', icon: 'none' });
    }
  };

  return (
    <PageWithNav title="客资详情">
      <View className={styles.pageBg}>
        {/* 头部卡片+操作按钮区 */}
        <View className={styles.headerCard}>
          <View className={styles.headerLeft}>
            <View className={styles.nameRow}>
              <Text className={styles.name}>{detail?.nickName || '无姓名'}</Text>
              <Text className={styles.tag}>{intentionText}</Text>
              <Text className={styles.status} style={{background: statusInfo.color + '22', color: statusInfo.color}}>{statusInfo.text}</Text>
            </View>
            {/* 顾问信息未在CustomerResourceInfoVo中提供，暂时保留或替换 */}
            <Text className={styles.consultant}>顾问：{detail?.consultantName || '暂无'}</Text> 
            <View className={styles.actionRow}>
              <View className={styles.actionLeft}>
                <View className={styles.actionBtn} onClick={navigateToEditCustomer}>编辑信息</View>
                <View className={styles.actionBtn} onClick={handleMakeCall}>拨打电话</View>
              </View>
              {/* <View className={styles.actionRight}>
                <View className={styles.actionBtn}>办卡</View>
              </View> */}
            </View>
          </View>
          <Image className={styles.avatar} src={detail?.avatar || memberUser} />
        </View>
        {/* tabs 区域 */} 
        <View className={styles.tabsRow}>
          <View className={`${styles.tab} ${activeTab === 'followUp' ? styles.active : ''}`} onClick={() => setActiveTab('followUp')}>跟进记录</View>
          <View className={`${styles.tab} ${activeTab === 'orders' ? styles.active : ''}`} onClick={() => setActiveTab('orders')}>订单信息</View>
        </View>

        {activeTab === 'followUp' && (
          <View className={styles.timeline}>
            <View className={styles.addFollow} onClick={navigateToAddFollowUp}><Text>＋ 添加跟进记录</Text></View>
            {loadingFollowUp ? (
              <View className={styles.loadingTimeline}>跟进记录加载中...</View>
            ) : followUpRecords.length > 0 ? (
              followUpRecords.map((item) => (
                <View className={styles.timelineItem} key={item.id}>
                  <View className={styles.timelineDot}></View>
                  <View className={styles.timelineContent}>
                    <View className={styles.timelineTitle}>{item.title}</View>
                    <View className={styles.timelineTime}>{item.followUpTime} {item.nickName}</View>
                    <View className={styles.timelineDesc}>{item.contents?.join('\n')}</View>
                    <View className={styles.timelineStore}>所属门店：{item.businessName}</View>
                  </View>
                </View>
              ))
            ) : (
              <View className={styles.loadingTimeline}>暂无跟进记录</View>
            )}
          </View>
        )}

        {activeTab === 'orders' && (
          <View className={styles.orderListContainer}>
            {loadingOrders ? (
              <View className={styles.loadingOrders}>订单记录加载中...</View>
            ) : orderRecords.length > 0 ? (
              orderRecords.map(order => (
                // Assuming 'order' is of type TMemberOperRecordDetailVo or similar
                // The structure of an "order" in this context seems to be a single operation/transaction
                // rather than a collection of services under one order ID like the mock data.
                // We need to adapt the display based on TMemberOperRecordDetailVo fields.
                <View key={order.id} className={styles.orderItem}>
                  {/*
                    TMemberOperRecordDetailVo fields:
                    - operationType: 1充值, 2消费, 3扣除, 4赠送, 5更新
                    - amount: 操作金额
                    - operationTime: 操作时间
                    - categoryItems: 类目名称 (e.g., "洗剪吹, 烫发") - may need parsing if it's a string
                    - enickName: 员工昵称
                    - remark: 备注
                    - giftAmount: 赠送金额
                    - receivedAmount: 实收金额
                  */}
                  <View className={styles.orderServiceItem}>
                    <View className={styles.orderServiceDetails}>
                      {/* Assuming categoryItems contains the service name(s) */}
                      <Text className={styles.orderServiceName}>{order.categoryItems || order.operationDesc || '项目详情见备注'}</Text>
                      {order.enickName && <Text className={styles.orderServiceStaff}>服务员工: {order.enickName}</Text>}
                    </View>
                    {/* Display amount based on operation type, or always display 'amount' if it's the primary one */}
                    <Text className={styles.orderServicePrice}>¥{order.amount?.toFixed(2) || '0.00'}</Text>
                  </View>
                  <View className={styles.orderSummary}>
                    <View className={styles.orderTotalRow}>
                       <Text className={styles.orderTotalLabel}>
                         {order.operationType === 1 ? '充值金额:' :
                          order.operationType === 2 ? '消费金额:' :
                          order.operationType === 3 ? '扣除金额:' :
                          order.operationType === 4 ? '赠送金额:' :
                          '操作金额:'}
                       </Text>
                       <Text className={styles.orderTotalAmount}> {order.amount?.toFixed(2) || '0.00'}元</Text>
                    </View>
                    {order.giftAmount > 0 && (
                      <View className={styles.orderTotalRow}>
                        <Text className={styles.orderTotalLabel}>赠送金额:</Text>
                        <Text className={styles.orderTotalAmount}> {order.giftAmount?.toFixed(2)}元</Text>
                      </View>
                    )}
                     {/* 'receivedAmount' might be relevant for consumption */}
                    {typeof order.receivedAmount === 'number' && order.operationType === 2 && (
                       <View className={styles.orderTotalRow}>
                         <Text className={styles.orderTotalLabel}>实收金额:</Text>
                         <Text className={styles.orderTotalAmount}> {order.receivedAmount?.toFixed(2)}元</Text>
                       </View>
                    )}
                    <Text className={styles.orderConsumptionTime}>操作时间: {order.operationTime || '无记录'}</Text>
                    {order.remark && <Text className={styles.orderRemark}>备注: {order.remark}</Text>}
                  </View>
                </View>
              ))
            ) : (
              <View className={styles.noOrders}>暂无订单记录</View>
            )}
          </View>
        )}
      </View>
    </PageWithNav>
  );
};

export default CustomerDetailPage; 