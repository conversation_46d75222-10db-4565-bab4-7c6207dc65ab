import React, { useState, useEffect } from "react";
import { View, Text, Textarea } from "@tarojs/components";
import Taro, { useRouter } from '@tarojs/taro';
import { postCustomerResourceBan } from '@/service/business/customerResourceController';
import PageWithNav from "@/view/component/page-with-nav/page-with-nav.component";
import styles from "./black-list-reason-page.module.scss";

const REASONS = [
  { label: "广告推销", value: "ad" },
  { label: "电话骚扰", value: "call" },
  { label: "其他", value: "other" },
];

const BlackListReasonPage: React.FC = () => {
  const router = useRouter();
  const [selected, setSelected] = useState("ad");
  const [otherReason, setOtherReason] = useState("");
  const [id, setId] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (router?.params?.id) {
      console.log('router.params.id', router.params.id);
      setId(router.params.id);
    }
  }, [router]);

  const handleConfirm = async () => {
    if (!id) {
      Taro.showToast({ title: '缺少ID', icon: 'none' });
      return;
    }
    if (selected === 'other' && !otherReason.trim()) {
      Taro.showToast({ title: '请输入具体原因', icon: 'none' });
      return;
    }
    setLoading(true);
    try {
      const selectedReason = REASONS.find(r => r.value === selected);
      const res = await postCustomerResourceBan({
        id: Number(id),
        banReason: selectedReason?.label || '',
        banRemark: selected === 'other' ? otherReason : undefined,
      });
      
      if (res) {
        Taro.showToast({ title: '拉黑成功', icon: 'success', duration: 1500 });
        setTimeout(() => {
          // 返回客资中心页面并刷新列表
          Taro.navigateBack();
        }, 1500);
      } else {
        throw new Error('拉黑失败');
      }
    } catch (e) {
      console.error('拉黑失败:', e);
      Taro.showToast({ title: '拉黑失败', icon: 'none' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageWithNav title="加入黑名单原因">
      <View className={styles.pageBg}>
        <View className={styles.reasonList}>
          {REASONS.map((item) => (
            <View
              key={item.value}
              className={styles.reasonItem + (selected === item.value ? ' ' + styles.active : '')}
              onClick={() => setSelected(item.value)}
            >
              <Text className={styles.reasonText + (selected === item.value ? ' ' + styles.activeText : '')}>{item.label}</Text>
              {selected === item.value && (
                <Text className={styles.checkIcon}>✔️</Text>
              )}
            </View>
          ))}
        </View>
        {selected === 'other' && (
          <View className={styles.otherBox}>
            <Textarea
              className={styles.textarea}
              placeholder="请输入具体原因"
              maxlength={50}
              value={otherReason}
              onInput={e => setOtherReason(e.detail.value)}
            />
            <View className={styles.textCount}>{otherReason.length}/50</View>
          </View>
        )}
        <View className={styles.bottomBar}>
          <View className={styles.confirmBtn} onClick={handleConfirm}>{loading ? '处理中...' : '确定'}</View>
        </View>
      </View>
    </PageWithNav>
  );
};

export default BlackListReasonPage; 