.pageBg {
  min-height: 100vh;
  background: #f7f7f9;
  display: flex;
  flex-direction: column;
}

.reasonList {
  background: #fff;
  border-radius: 0 0 12px 12px;
  margin-bottom: 12px;
  overflow: hidden;
}

.reasonItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 48px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #f2f2f2;
  background: #fff;
  position: relative;
  cursor: pointer;
}

.reasonItem:last-child {
  border-bottom: none;
}

.reasonText {
  color: #333;
  font-size: 16px;
}

.active .reasonText {
  color: #ffb300;
  font-weight: bold;
}

.checkIcon {
  color: #ffb300;
  font-size: 20px;
  margin-left: 8px;
}

.active {
  background: #fffbe6;
}

.otherBox {
  background: #fafafa;
  border-radius: 12px;
  margin: 0px 16px 0 16px;
  padding: 12px 12px 0 12px;
  position: relative;
}

.textarea {
  width: 100%;
  min-height: 90px;
  background: #f7f7f9;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  color: #bbb;
  resize: none;
  outline: none;
  margin-bottom: 4px;
  padding: 12px;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.02);
}

.textarea::placeholder {
  color: #d2d2d2;
  font-size: 16px;
}

.textCount {
  position: absolute;
  right: 16px;
  bottom: 8px;
  font-size: 12px;
  color: #bbb;
}

.bottomBar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 16px 0 24px 0;
  z-index: 10;
  display: flex;
  justify-content: center;
}

.confirmBtn {
  width: 90%;
  height: 44px;
  background: #ffde7b;
  color: #fff;
  border-radius: 22px;
  font-size: 18px;
  text-align: center;
  line-height: 44px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(255, 222, 123, 0.15);
} 