import BasicPage from "@/view/component/basic-page/basic-page.components";
import styles from "./staff-shift-page.module.scss";
import { Button, Label, PageContainer, ScrollView, Switch, View } from "@tarojs/components";
import Switcher from "@/view/component/common/Switcher";
import { useEffect, useMemo, useRef, useState } from "react";
import { AtFloatLayout, AtIcon, AtSwitch } from "taro-ui";
import TimeRangePicker from "@/view/component/common/TimeRangePicker";
import Picker from "@/view/component/common/Picker";
import { CacheStore } from "@/store/cache.store";
import useStore from "@/hook/store";
import Taro, { useDidShow } from "@tarojs/taro";

const WEEKDAY = ["一", "二", "三", "四", "五", "六", "日",]

const StaffShift: React.FC = () => {
    const cache: CacheStore = useStore().cacheStore;

    const staffInfoRef = useRef<Record<string, any>>();
    const [shiftForm, setShiftForm] = useState({
        shiftType: "班次",
        shiftName: "日班",
        shiftWholeDay: 1,
        shiftStartTime: "",
        shiftEndTime: "",
        weeklyRestType: "自定义",
        weeklyRestDay: [] as number[]
    })

    const [showTimePicker, setShowTimePicker] = useState(false);

    const [showSchedulePicker, setShowSchedulePicker] = useState(false);

    const timeRange = useMemo(() => {
        if (!shiftForm.shiftStartTime || !shiftForm.shiftEndTime) {
            return ""
        }
        return `${shiftForm.shiftStartTime} ~ ${shiftForm.shiftEndTime}`
    }, [shiftForm.shiftStartTime, shiftForm.shiftEndTime])

    const updateShiftForm = (props: Record<string, any>) => {
        setShiftForm(state => ({
            ...state,
            ...props
        }))
    }

    const onScheduleTimeChange = (start: string, end: string) => {
        updateShiftForm({
            shiftStartTime: start,
            shiftEndTime: end
        })
    }

    const onRestTypeChange = (type: string) => {
        if (type === "单休") {
            updateShiftForm({
                weeklyRestDay: [5],
                weeklyRestType: type
            })
        } else if (type === "双休") {
            updateShiftForm({
                weeklyRestDay: [5, 6],
                weeklyRestType: type
            })
        } else {
            updateShiftForm({
                weeklyRestDay: [],
                weeklyRestType: type
            })
        }
    }

    const onRestDayChange = (index: number) => {
        let restDay = [...shiftForm.weeklyRestDay];
        if ((shiftForm.weeklyRestType === "单休" && [5, 6].includes(index))) {
            updateShiftForm({
                weeklyRestDay: [index]
            })
            return
        }

        if (shiftForm.weeklyRestType === "自定义") {
            if (restDay.includes(index)) {
                restDay = restDay.filter(item => item != index)
            } else {
                restDay.push(index)
            }
            updateShiftForm({
                weeklyRestDay: restDay
            })
        }
    }

    // 提交员工数据
    const onSubmit = async () => {
        const staff = {
            ...staffInfoRef.current,
            ...shiftForm,
            weeklyRestDay: shiftForm.weeklyRestDay.join(",")
        }
        cache.setPageContext(staff)
        Taro.navigateBack()
    };

    useDidShow(() => {
        const staffInfo = cache.pageContext;
        if (!staffInfo) {
            return
        }
        staffInfoRef.current = staffInfo;

        const cacheShiftForm = {
            shiftType: staffInfo.shiftType || "班次",
            shiftName: staffInfo.shiftName || "日班",
            shiftWholeDay: staffInfo.shiftWholeDay || 1,
            shiftStartTime: staffInfo.shiftStartTime || "",
            shiftEndTime: staffInfo.shiftEndTime || "",
            weeklyRestType: staffInfo.weeklyRestType || "自定义",
            weeklyRestDay: staffInfo.weeklyRestDay.split(",").map(item => Number(item)) || []
        }
        console.log(cacheShiftForm)
        setShiftForm(cacheShiftForm)
    })

    return (
        <BasicPage
            title="员工排班"
            bottomSafe
        >
            <View className={styles.staff_shift_page}>
                <ScrollView className={styles.wrapper} scrollY>
                    <View className={styles.typeSwitcher}>
                        <Switcher value={shiftForm.shiftType} options={[{ value: "班次" }, { value: "轮休" }]} onChange={val => updateShiftForm({ shiftType: val })}></Switcher>
                    </View>
                    <View className={styles.formPanel}>
                        <View className={styles.formItem}>
                            <View className={styles.label}>班次名称</View>
                            <View className={styles.control}>
                                <Switcher size="small" value={shiftForm.shiftName} options={[{ value: "日班" }, { value: "夜班" }]} onChange={val => updateShiftForm({ shiftName: val })}></Switcher>
                            </View>
                        </View>
                        <View className={styles.formItem}>
                            <View className={styles.label}>是否全天</View>
                            <View className={styles.control}>
                                <AtSwitch className={styles.customSwitch} checked={shiftForm.shiftWholeDay === 1} color={"#FDD244"} onChange={checked => updateShiftForm({ shiftWholeDay: checked ? 1 : 0 })} />
                            </View>
                        </View>
                        <View className={styles.formItem}>
                            <View className={styles.label}>工作时间</View>
                            <View className={styles.control} onClick={() => setShowTimePicker(true)}>
                                <View className={styles.content}>
                                    {timeRange ? timeRange : <Label className={styles.placeholder}>开始时间 ～ 结束时间</Label>}
                                </View>
                                <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
                            </View>
                        </View>
                        <View className={`${styles.formItem} ${styles.schedule}`}>
                            <View className={styles.subContent} onClick={() => setShowSchedulePicker(true)}>
                                <View className={styles.label}>设置休息日</View>
                                <View className={styles.control}>
                                    <View className={styles.content}>{shiftForm.weeklyRestType}</View>
                                    <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
                                </View>
                            </View>
                            <View className={styles.scheduleBox}>
                                {
                                    WEEKDAY.map((day, index) => (
                                        <View
                                            key={day}
                                            className={`${styles.weekday} ${shiftForm.weeklyRestDay.includes(index) ? styles.active : ''}`}
                                            onClick={() => onRestDayChange(index)}
                                        >
                                            {day}
                                        </View>
                                    ))
                                }
                            </View>
                        </View>
                    </View>
                </ScrollView>
                <View className={styles.submit_box}>
                    <Button className={styles.submit} hoverClass={styles.active} onClick={onSubmit}>保存</Button>
                </View>
            </View>
            <TimeRangePicker
                show={showTimePicker}
                start={shiftForm.shiftStartTime || "9:00"}
                end={shiftForm.shiftEndTime || "17:00"}
                onConfirm={onScheduleTimeChange}
                onClose={() => setShowTimePicker(false)} />
            <Picker
                show={showSchedulePicker}
                data={shiftForm.weeklyRestType}
                range={["单休", "双休", "自定义"]}
                onConfirm={val => onRestTypeChange(val)}
                onClose={() => setShowSchedulePicker(false)}
            />
        </BasicPage>

    )
}

export default StaffShift;