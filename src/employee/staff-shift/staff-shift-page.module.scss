$at-switch-title-size: 32px;
$at-switch-title-color: red;

@import "~taro-ui/dist/style/components/switch.scss";

.staff_shift_page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #fff;
    box-sizing: border-box;

    .wrapper {
        width: 100%;
        background-color: #F6F6F6;
        flex: 1 0;
        overflow: hidden;
        padding: 16px;
        box-sizing: border-box;

        .typeSwitcher {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-bottom: 16px;
        }

        .formPanel {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 0 20px;

            .formItem {
                height: 50px;
                display: flex;
                align-items: center;
                border-bottom: 1px solid rgba(235, 237, 240, 0.46);

                .label {
                    width: 90px;
                    font-size: 15px;
                    flex-shrink: 0;
                }

                .control {
                    flex: 1 0;
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    color: #333333;
                    font-size: 14px;

                    .content {
                        flex: 1 0;

                        .placeholder {
                            color: #949494;
                        }
                    }

                    .linkIcon {
                        color: #969799;
                    }
                }

                &:last-child {
                    border-bottom: 0;
                }

                .customSwitch {
                    padding: 0;
                    $custom-switch-width: 40px;
                    $custom-switch-height: 20px;

                    &:after {
                        border: 0;
                    }



                    // 参考 https://juejin.im/post/5b051b1c6fb9a07aca7a8676
                    :global {
                        .at-switch__switch {
                            width: $custom-switch-width;
                            height: $custom-switch-height;
                        }

                        .weui-switch,
                        .wx-switch-input {
                            width: $custom-switch-width !important;
                            height: $custom-switch-height !important;

                            &::before {
                                width: $custom-switch-width - 2 !important;
                                height: $custom-switch-height - 2 !important;
                            }

                            &::after {
                                width: $custom-switch-height - 3 !important;
                                height: $custom-switch-height - 3 !important;
                            }
                        }
                    }

                }

                &.schedule {
                    flex-direction: column;
                    height: fit-content;

                    .subContent {
                        width: 100%;
                        height: 50px;
                        display: flex;
                        align-items: center;
                    }

                    .scheduleBox {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        padding-bottom: 14px;

                        .weekday {
                            width: 36px;
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            background-color: #F7F7F7;
                            color: #949494;
                            border-radius: 8px;

                            &.active {
                                background-color: #FDD244;
                                color: #fff;
                            }
                        }
                    }
                }
            }
        }
    }

    .submit_box {
        padding: 7px 16px;
        background-color: #fff;
        box-sizing: border-box;

        .submit {
            width: 100%;
            height: 36px;
            background-color: #fdd244;
            color: #FFFFFF;
            border-radius: 999px;
            border: 0;
            font-size: 15px;

            &::after {
                border: none;
            }

            &.active {
                background-color: #fdd244e0;
            }
        }
    }
}