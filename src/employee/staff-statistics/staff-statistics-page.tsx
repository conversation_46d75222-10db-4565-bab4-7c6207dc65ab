import { useState } from "react";
import { Text, View, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@tarojs/components";
import { AtIcon } from "taro-ui";
import styles from "./staff-statistics-page.module.scss";

const Index = () => {
	const [info, setState] = useState({
		month: "2025-05",
		staff: "小王",
	});
	const [list, setList] = useState<any[]>([
		1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14,
	]);

	const range = [
		{
			code: "xiaowang",
			name: "小王",
		},
		{
			code: "xiaowang2",
			name: "小王2",
		},
	];

	const updateState = (state) => {
		setState({ ...info, ...state });
	};

	const scrollStyle = {
		height: `calc(100vh - 120px)`,
	};
	const scrollTop = 0;
	const Threshold = 20;

	const config = [
		{ title: "办卡", value: 1 },
		{ title: "续卡", value: 12 },
		{ title: "划卡", value: 142 },
		{ title: "收入", value: 13 },
	];

	return (
		<View className={styles.container}>
			<View className={styles.top}>
				<View className={styles.left}>
					<Text className={styles.title}>月统计</Text>
					<View className={styles.sub}>
						<Picker
							mode="date"
							fields="month"
							value={info.month}
							onChange={(e) =>
								updateState({ month: e.detail.value })
							}
						>
							<View>{info.month}</View>
						</Picker>
					</View>
				</View>
				<View className={styles.right}>
					<Picker
						mode="selector"
						range={range}
						range-key="name"
						value={range?.findIndex(
							(item) => item.name === info.staff
						)}
						onChange={(e) =>
							updateState({
								staff: range[e.detail.value]?.name,
							})
						}
					>
						<View>
							<Text>{info.staff}</Text>
							<AtIcon size="16" value="chevron-down"></AtIcon>
						</View>
					</Picker>
					<View
						className={styles.sub}
						onClick={() => alert("暂无数据")}
					>
						<Text>统计</Text>
						<AtIcon size="16" value="chevron-right"></AtIcon>
					</View>
				</View>
			</View>
			<View className={styles.middle}>
				<Text className={styles.tip}>
					共办卡3人、续卡0人、划卡3人、收支(+3226元1-0元)
				</Text>
			</View>
			<View className={styles.bottom}>
				<ScrollView
					className="scrollview"
					scrollY
					scrollWithAnimation
					scrollTop={scrollTop}
					style={scrollStyle}
					lowerThreshold={Threshold}
					upperThreshold={Threshold}
					// onScrollToUpper={this.onScrollToUpper.bind(this)} // 使用箭头函数的时候 可以这样写 `onScrollToUpper={this.onScrollToUpper}`
					// onScroll={this.onScroll}
				>
					{list.map((item) => (
						<View className={styles["list-item"]} key={item}>
							<View className={styles["list-item-top"]}>
								2025年03月14日 星期五
							</View>
							<View className={styles["list-item-bottom"]}>
								{config.map((obj) => (
									<View
										className={styles.box}
										key={obj.title}
									>
										<View className={styles["box-item"]}>
											{obj.value}人
										</View>
										<View className={styles["box-item"]}>
											{obj.title}
										</View>
									</View>
								))}
							</View>
						</View>
					))}
				</ScrollView>
			</View>
		</View>
	);
};

export default Index;
