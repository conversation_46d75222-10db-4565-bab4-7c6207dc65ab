@use "@/theme.scss" as t;

.container {
	.top {
		padding: t.$font-size-lg;
		background-color: t.$color-brand;
		@extend %display-flex-row;

		.left {
			@extend %display-flex-column;
			font-size: 32px;
			.title {
				font-weight: bold;
				font-size: t.$font-size-base;
			}
			.sub {
			}
		}

		.right {
			padding: 12px 24px;
			font-size: t.$font-size-base;
		}
	}

	.middle {
		width: 100%;
		height: 64px;
		font-size: t.$font-size-base;
		line-height: 64px;
		text-align: center;
		background-color: bisque;
	}

	.bottom {
		background-color: t.$background-color;
		.list-item {
			position: relative;
			margin: 16px;
			padding: 24px;
			color: #999;
			font-size: t.$font-size-base;
			background-color: #fff;
			border-radius: 8px;
			@extend %display-flex-column;
			&-top {
				font-size: t.$font-size-base;
			}
			&-bottom {
				width: 100%;
				@extend %display-flex-row;
			}
		}

		.list-item-bottom {
			.box {
				align-items: center;
				width: 100%;
				height: 100%;
				margin-top: 8px;
				border-right: 1px solid t.$color-border-base;
				@extend %display-flex-column;
			}

			.box:last-child {
				border: none;
			}
		}
	}
}
