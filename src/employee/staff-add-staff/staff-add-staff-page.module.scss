@use "@/theme.scss" as t;

.wrapper {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.scrollView {
		flex: 1 0;
		padding: 16px;
		background-color: #F6F6F6;
		box-sizing: border-box;

		.cards {
			box-sizing: border-box;
			width: 100%;
			overflow-y: auto;
		}

		.card {
			@extend %card;

			.avatarSection {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin-bottom: 10px;

				.avatarWrapper {
					display: flex;
					flex-direction: column;
					align-items: center;

					.avatarImg {
						position: relative;

						.avatar {
							width: 80px;
							height: 80px;
							border-radius: 50%;
						}

						.camera {
							position: absolute;
							right: -10px;
							bottom: 0px;
							width: 40px;
							height: 40px;
						}
					}
				}

				.uploadHint {
					.uploadText {
						color: #cecece;
						font-size: 12px;
					}
				}
			}

			.formItem {
				height: 50px;
				border-bottom: 1px solid rgba(235, 237, 240, 0.46);
				display: flex;
				align-items: center;

				.label {
					width: 70px;
					color: #1D2129;
					font-size: 15px;
					flex-shrink: 0;

					&.required {
						&::after {
							content: "*";
							color: red;
						}
					}
				}

				.content {
					flex: 1 0;
					height: 100%;
					font-size: 15px;
					color: #333333;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.input {
						width: 100%;
					}

					.linkIcon {
						color: #969799;
					}

					.customSwitch {
						padding: 0;
						$custom-switch-width: 40px;
						$custom-switch-height: 20px;

						&:after {
							border: 0;
						}



						// 参考 https://juejin.im/post/5b051b1c6fb9a07aca7a8676
						:global {
							.at-switch__switch {
								width: $custom-switch-width;
								height: $custom-switch-height;
							}

							.weui-switch,
							.wx-switch-input {
								width: $custom-switch-width !important;
								height: $custom-switch-height !important;

								&::before {
									width: $custom-switch-width - 2 !important;
									height: $custom-switch-height - 2 !important;
								}

								&::after {
									width: $custom-switch-height - 3 !important;
									height: $custom-switch-height - 3 !important;
								}
							}
						}

					}

					.extraIcon {
						width: 15px;
						height: 16px;
					}
				}


				&:last-child {
					border-bottom: 0;
				}
			}
		}
	}

	.submitBox {
		width: 100%;
		height: 50px;
		background: #fff;
		display: flex;
		align-items: center;

		.submitBtn {
			width: calc(100% - 32px);
		}
	}
}


.container {
	padding: 10px;
	background: #f6f6f6;

	.cards {
		box-sizing: border-box;
		width: 100%;
		overflow-y: auto;
	}

	.card {
		@extend %card;

		.avatarSection {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-bottom: 10px;

			.avatarWrapper {
				display: flex;
				flex-direction: column;
				align-items: center;

				.avatarImg {
					position: relative;

					.avatar {
						width: 80px;
						height: 80px;
						border-radius: 50%;
					}

					.camera {
						position: absolute;
						right: -10px;
						bottom: 0px;
						width: 40px;
						height: 40px;
					}
				}
			}

			.uploadHint {
				.uploadText {
					color: #cecece;
					font-size: 12px;
				}
			}
		}
	}

	.obts {
		@extend %bottom-btn;
	}
}