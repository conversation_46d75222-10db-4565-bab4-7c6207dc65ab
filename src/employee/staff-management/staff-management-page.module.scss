$color: #1f1f1f;
$color-sub: #a5a5a5;
$border-color: rgba(238, 238, 238, 0.7);
$theme-color: #fdd244;
$margin-x: 16px;
$margin-x-l: 20px;

.container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	overflow: hidden;

	.shopInfo {
		display: flex;
		align-items: center;
		box-sizing: border-box;
		height: 42px;
		padding: 0 $margin-x;

		.shopIcon {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}

		.shopName {
			color: $color;
			font-weight: 500;
			font-size: 16px;
		}
	}

	.titleSection {
		padding: 0 $margin-x-l;

		.title {
			display: block;
			margin-top: 8px;
			margin-bottom: 8px;
			color: $color;
			font-weight: 600;
			font-size: 24px;
		}

		.subTitle {
			display: block;
			color: $color-sub;
			font-size: 14px;
		}
	}

	.staffCount {
		margin: 20px $margin-x-l 0;
		padding-bottom: 12px;
		border-bottom: 1px solid $border-color;

		.countText {
			color: $color-sub;
			font-size: 14px;
		}
	}

	.addStaffBtn {
		display: flex;
		align-items: center;
		padding: 12px 0px;
		margin: 0 $margin-x-l;
		border-bottom: 1px solid $border-color;

		.plusImg {
			width: 18px;
			height: 18px;
			margin-right: 8px;
		}

		.addStaff {
			color: $theme-color;
			font-size: 16px;
		}
	}

	.staffList {
		flex: 1 0;
		overflow: hidden;
		background-color: #fff;
		border-radius: 8px;

		.staffItem {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 8px 0px;
			border-bottom: 1px solid $border-color;
			margin: 0 $margin-x-l;
			box-sizing: border-box;

			.staffInfo {
				display: flex;
				align-items: center;

				.staffDetail {
					margin-left: 12px;

					.nameRow {
						display: flex;
						align-items: center;
						margin-bottom: 6px;

						.staffName {
							margin-right: 8px;
							color: #333;
							font-weight: 500;
							font-size: 16px;
						}

						.statusTag {
							width: 42px;
							height: 18px;
							color: #fff;
							font-size: 10px;
							line-height: 18px;
							text-align: center;
							border-radius: 100px 100px 100px 0px;
						}

						.activeStatus {
							background: #50d226;
						}

						.inactiveStatus {
							color: #999;
							background-color: #f5f5f5;
						}
					}

					.positionRow {
						display: flex;
						align-items: center;

						.positionTag {
							box-sizing: border-box;
							height: 14px;
							margin-right: 4px;
							padding: 0px 4px;
							color: #a6660f;
							font-size: 10px;
							line-height: 12px;
							border: 1px solid #a6660f;
							border-radius: 9px;
						}

						.staffId {
							color: $color-sub;
							font-size: 12px;
						}
					}
				}
			}

			.moreAction {
				width: 18px;
				height: 18px;
			}
		}
	}

	.emptyList {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 32px;
		color: #666666;
		font-weight: 400;
		font-size: 14px;

		.empty {
			width: 136px;
			height: 145px;
			margin-bottom: 16px;
		}

		.emptyText {
			color: #666666;
			font-weight: 400;
			font-size: 14px;
		}

		.emptyAddStaffBtn {
			box-sizing: border-box;
			width: 162px;
			height: 36px;
			margin-top: 16px;
			color: #fff;
			line-height: 36px;
			text-align: center;
			background: #fdd244;
			border-radius: 20px;
			cursor: pointer;
		}
	}
}
