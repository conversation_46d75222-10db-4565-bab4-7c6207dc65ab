import React, { useCallback, useRef, useState } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { Text, View, Image } from "@tarojs/components";
import { AtAvatar } from "taro-ui";
import {
	PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import ScrollList, { ScrollListRef } from "@/view/component/scroll-list/scroll-list.component";
import CustomActionSheet from "@/view/component/custom-action-sheet/custom-action-sheet.component";
import service from "@/service";
import shopImg from "@/assets/image/common/shop-info.png";
import PlusImg from "@/assets/image/common/plus-circle.png";
import MoreActionImg from "@/assets/image/common/more-action.png";
import EmptyImg from "@/assets/image/common/empty.png";
import styles from "./staff-management-page.module.scss";
import BasicPage from "@/view/component/basic-page/basic-page.components";
import useStore from "@/hook/store";
import { GlobalInfoStore } from "@/store/global-info.store";

const PAGE_SIZE = 10;

const Index: React.FC = () => {
	const globalCache: GlobalInfoStore = useStore().globalInfoStore;

	const container: PageContainerRef = useRef(null);
	const scrollListRef: ScrollListRef = useRef(null);
	const [{ dataSource, total, hasRequested }, setDataSource] = useState<any>({
		dataSource: [],
		total: 0,
		hasRequested: false,
	});
	const [listHeight, setListHeight] = useState("100vh");
	const [actionSheetVisible, setActionSheetVisible] = useState(false);
	const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(
		null
	);

	useDidShow(() => {
		getList(1);
		scrollListRef.current?.reset();
	});

	const getList = useCallback(
		(pageNum: number) => {
			service.business.businessEmployeeController
				.getEmployeeList({
					pageNum,
					pageSize: PAGE_SIZE,
				})
				.then((res) => {
					if (res?.data) {
						setDataSource({
							dataSource:
								pageNum === 1
									? res?.data
									: dataSource?.concat(res?.data) ??
									dataSource,
							total: res?.total ?? 0,
							hasRequested: true,
						});
					}
				});
		},
		[dataSource]
	);

	const onLinkTo = (type: "add" | "edit" | "statistics") => {
		const url = {
			add: "/employee/staff-add-staff/staff-add-staff-page",
			edit: `/employee/staff-add-staff/staff-add-staff-page?id=${selectedEmployeeId}`,
			statistics: `/employee/staff-statistics/staff-statistics-page?id=${selectedEmployeeId}`,
		}[type];
		Taro.navigateTo({ url });
	};

	const handleMoreAction = (employeeId) => {
		setSelectedEmployeeId(employeeId);
		setActionSheetVisible(true);
	};

	const onDelete = () => {
		Taro.showLoading({
			title: "保存中"
		})

		try {
			service.business.businessEmployeeController
				.deleteEmployeeEmployeeId({ employeeId: selectedEmployeeId })
				.then((res) => {
					console.log("删除员工成功", res);
					if (res) {
						getList(1);
					} else {
						throw new Error("保存失败")
					}
				});
		} catch (error) {
			Taro.showToast({
				title: "保存失败",
				icon: "error"
			})
		} finally {
			Taro.hideLoading()
		}

	};

	const handleActionSelect = (index: number) => {
		setActionSheetVisible(false);
		const actions: Record<number, () => void> = {
			// 0: () => onLinkTo("statistics"),
			0: () => onLinkTo("edit"),
			1: () => onDelete(),
		};
		const fn = actions[index];
		fn?.();
	};

	// 渲染员工列表项
	const renderStaffItem = (item) => (
		<View className={styles.staffItem} key={item.employeeId}>
			<View className={styles.staffInfo}>
				<AtAvatar circle image={item.avatarUrl} size="normal" />
				<View className={styles.staffDetail}>
					<View className={styles.nameRow}>
						<Text className={styles.staffName}>
							{item.employeeNickname || "-"}
						</Text>
						<View
							className={`${styles.statusTag} ${item.status
								? styles.activeStatus
								: styles.inactiveStatus
								}`}
						>
							<Text>{item.status ? "已启用" : "已禁用"}</Text>
						</View>
					</View>
					<View className={styles.positionRow}>
						<View className={styles.positionTag}>
							<Text>{item.position ?? "暂无"}</Text>
						</View>
						<Text className={styles.staffId}>
							ID：{item.employeeId ?? "-"}
						</Text>
					</View>
				</View>
			</View>
			<View onClick={() => handleMoreAction(item.employeeId)}>
				<Image className={styles.moreAction} src={MoreActionImg} />
			</View>
		</View>
	);

	return (
		<BasicPage
			title="员工管理"
			ref={container}
		>
			<view className={styles.container}>
				<View className={styles.top} id="top">
					<View className={styles.shopInfo}>
						<Image className={styles.shopIcon} src={shopImg} />
						<Text className={styles.shopName}>
							{globalCache.businessName}
						</Text>
					</View>
					<View className={styles.titleSection}>
						<Text className={styles.title}>员工管理</Text>
						<Text className={styles.subTitle}>
							添加员工，一起协助管理门店生意。
						</Text>
					</View>

					{hasRequested && dataSource.length > 0 && (
						<>
							<View className={styles.staffCount}>
								<Text className={styles.countText}>
									已添加 ({total})
								</Text>
							</View>

							<View
								className={styles.addStaffBtn}
								onClick={() => onLinkTo("add")}
							>
								<Image src={PlusImg} className={styles.plusImg} />
								<Text className={styles.addStaff}>添加店员</Text>
							</View>
						</>
					)}
				</View>
				{/* 使用封装的ScrollList组件 */}
				<ScrollList
					ref={scrollListRef}
					dataSource={dataSource}
					total={total}
					pageSize={PAGE_SIZE}
					renderItem={renderStaffItem}
					onLoadMore={getList}
					className={styles.staffList}
					keyExtractor={(item) => item?.employeeId?.toString()}
					emptyComponent={
						<View className={styles.emptyList}>
							<Image className={styles.empty} src={EmptyImg} />
							<Text className={styles.emptyText}>暂无员工数据</Text>
							<View
								className={styles.emptyAddStaffBtn}
								onClick={() => onLinkTo("add")}
							>
								添加员工
							</View>
						</View>
					}
				/>

				{/* 自定义 ActionSheet */}
				<CustomActionSheet
					visible={actionSheetVisible}
					options={["编辑", "删除"]}
					onSelect={handleActionSelect}
					onCancel={() => setActionSheetVisible(false)}
				/>
			</view>

		</BasicPage>
	);
};

export default Index;
