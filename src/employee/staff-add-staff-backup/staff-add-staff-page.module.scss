@use "@/theme.scss" as t;

.container {
	padding: 10px;
	background: #f6f6f6;

	.cards {
		box-sizing: border-box;
		width: 100%;
		overflow-y: auto;
	}

	.card {
		@extend %card;

		.avatarSection {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-bottom: 10px;

			.avatarWrapper {
				display: flex;
				flex-direction: column;
				align-items: center;

				.avatarImg {
					position: relative;
					.avatar {
						width: 80px;
						height: 80px;
						border-radius: 50%;
					}

					.camera {
						position: absolute;
						right: -10px;
						bottom: 0px;
						width: 40px;
						height: 40px;
					}
				}
			}

			.uploadHint {
				.uploadText {
					color: #cecece;
					font-size: 12px;
				}
			}
		}
	}

	.obts {
		@extend %bottom-btn;
	}
}
