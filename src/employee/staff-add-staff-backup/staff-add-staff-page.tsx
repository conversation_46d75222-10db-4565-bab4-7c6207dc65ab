import React, { useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image } from "@tarojs/components";
import { AtButton } from "taro-ui";
import PageWithNav, {
	PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import RenderTypeItem from "@/view/component/render-type/render-type-item.component";
import { fieldsConfig, validateForm } from "@/util/fieldsMap";
import AvatarImg from "@/assets/image/common/avatar.png";
import CarameImg from "@/assets/image/common/camera.png";
import styles from "./staff-add-staff-page.module.scss";

interface IProps {
	avatarUrl?: string | null; // 员工头像
	avatarUrlFile?: File | null; // 员工头像文件
	employeeNickname: string | undefined; // 用户名
	sex: number; // 性别
	employeePhone: string | undefined; // 手机号
	position: string | undefined; // 职位
	status: number; // 0-禁用 1-启用
	onDutyStatus: "ON_DUTY" | "OFF_DUTY"; // ON_DUTY-上班 OFF_DUTY-下班
	rank: "高级" | "中级" | "初级";
}

const Index: React.FC = () => {
	const container: PageContainerRef = useRef(null);
	const [isEdit, setIsEdit] = useState(false); // 当前是否处于编辑态
	const [userInfo, setUserInfo] = useState<IProps>({
		avatarUrl: null,
		avatarUrlFile: null,
		employeeNickname: undefined,
		sex: 1,
		employeePhone: undefined,
		position: undefined,
		status: 0,
		onDutyStatus: "ON_DUTY",
		rank: "中级",
	});
	const [listHeight, setListHeight] = useState("calc(100vh - 42px)");
	const { id } = Taro.getCurrentInstance().router?.params ?? {};

	/** 员工编辑信息 */
	useEffect(() => {
		if (id) {
			setIsEdit(true);
			service.business.businessEmployeeController
				.getEmployeeEmployeeId({ employeeId: parseInt(id) })
				.then((data) => {
					data && setUserInfo(data);
				});
		}
	}, [id]);

	const config = [
		{ ...fieldsConfig.employeeNickname, required: true },
		{ ...fieldsConfig.employeePhone, required: true },
		fieldsConfig.sex,
	];

	const secondConfig = [
		{ ...fieldsConfig.position, required: true },
		fieldsConfig.rank,
		fieldsConfig.status,
		fieldsConfig.onDutyStatus,
		fieldsConfig.ssmd,
	];

	const updateState = (state) => {
		setUserInfo({ ...userInfo, ...state });
	};

	const onLinkTo = () => {
		Taro.navigateTo({
			url: "/employee/staff-management/staff-management-page",
		});
	};
	const onSave = async () => {
		if (check()) {
			container.current?.openToast({
				message: "保存中",
				type: "loading",
			});
			const API = isEdit ? "putBusinessEmployee" : "postBusinessEmployee";
			const { avatarUrlFile, avatar, ...rest } = userInfo;
			console.log(avatarUrlFile, avatar);

			// 服务器出错时，会导致loading提醒无法自动关闭，添加try-catch
			try {
				const data = await service.business.businessEmployeeController[API](
					{
						avatar: avatarUrlFile ?? avatar, // 新增时优先取文件，否则取图片路径
						...rest,
					}
				);
				if (data) {
					onLinkTo();
				} else {
					throw new Error("保存员工失败")
				}
			} catch (error) {
				container.current?.openMessage({
					message: "保存失败",
					type: "error",
				});
			}
			container.current?.closeToast();
		}
	};

	/** 一些必填校验 */
	const check = (): boolean => {
		const fieldsToValidate = [
			"employeeNickname",
			"employeePhone",
			"position",
		];
		const validationResult = validateForm(userInfo, fieldsToValidate);

		if (validationResult) {
			container.current?.openMessage({
				message: validationResult.message,
				type: "warning",
			});
			return false;
		}
		return true;
	};

	const renderItem = (list) => {
		return (list || []).map((item, i) => (
			<RenderTypeItem
				{...item}
				key={item.key}
				itemKey={item.key}
				value={userInfo?.[item.key]}
				onChange={updateState}
				style={
					i === list?.length - 1
						? { paddingBottom: 0, borderBottom: "none" }
						: {}
				}
			/>
		));
	};

	const handleHeightChange = (navHeight: number) => {
		const height = navHeight + 50; // 50是底部操作栏高度
		console.log("nav", navHeight);
		setListHeight(`calc(100vh - ${height}px)`);
	};

	// 添加上传图片的方法
	const handleUploadAvatar = () => {
		Taro.chooseImage({
			count: 1,
			sizeType: ["compressed"],
			sourceType: ["album", "camera"],
			success: async (res) => {
				const tempFilePath = res.tempFilePaths[0];
				console.log("选择的图片路径：", {
					avatarUrlFile: res.tempFiles[0].originalFileObj,
					avatar: tempFilePath,
				});
				updateState({
					avatarUrlFile: res.tempFiles[0].originalFileObj,
					avatar: tempFilePath,
				});
			},
		});
	};

	return (
		<PageWithNav
			showNavBar
			title="添加员工"
			containerRef={container}
			containerClassName={styles.container}
			onHeightChange={handleHeightChange}
		>
			<View className={styles.cards} style={{ height: listHeight }}>
				<View className={styles.card}>
					<View className={styles.avatarSection}>
						<View className={styles.avatarWrapper}>
							<View
								className={styles.avatarImg}
								onClick={handleUploadAvatar}
							>
								<Image
									className={styles.avatar}
									src={userInfo.avatar || AvatarImg}
									mode="aspectFill"
								/>
								<Image
									className={styles.camera}
									src={CarameImg}
									mode="aspectFill"
								/>
							</View>

							<View className={styles.uploadHint}>
								<Text className={styles.uploadText}>
									上传真实头像更利于审核通过
								</Text>
							</View>
						</View>
					</View>
					{renderItem(config)}
				</View>

				<View className={styles.card}>{renderItem(secondConfig)}</View>
			</View>

			<View className={styles.obts}>
				<AtButton type="primary" onClick={() => onSave()} circle>
					保存
				</AtButton>
			</View>
		</PageWithNav>
	);
};

export default Index;
