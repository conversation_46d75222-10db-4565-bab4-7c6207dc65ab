import { FC, useState, useRef, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import './alphabet-index.scss';

export interface AlphabetIndexProps {
  /**
   * 字母列表，默认为 A-Z
   */
  letters?: string[];
  /**
   * 点击字母时的回调
   */
  onLetterClick?: (letter: string, index: number) => void;
  /**
   * 滑动选择字母时的回调
   */
  onLetterSelect?: (letter: string, index: number) => void;
  /**
   * 自定义样式类
   */
  className?: string;
  /**
   * 是否显示当前选中字母的气泡提示
   */
  showIndicator?: boolean;
  /**
   * 自定义气泡提示样式类
   */
  indicatorClass?: string;
}

const DEFAULT_LETTERS = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];

const AlphabetIndex: FC<AlphabetIndexProps> = ({
  letters = DEFAULT_LETTERS,
  onLetterClick,
  onLetterSelect,
  className = '',
  showIndicator = true,
  indicatorClass = '',
}) => {
  const [activeIndex, setActiveIndex] = useState<number>(-1);
  const [showToast, setShowToast] = useState<boolean>(false);
  const indexBarRef = useRef<any>(null);
  const isTouching = useRef<boolean>(false);
  const touchStartTime = useRef<number>(0);
  const touchStartY = useRef<number>(0);
  const lastTouchY = useRef<number>(0);
  const isMoving = useRef<boolean>(false);

  // 重置状态
  useEffect(() => {
    return () => {
      isTouching.current = false;
      isMoving.current = false;
    };
  }, []);

  // 处理触摸开始
  const handleTouchStart = (e) => {
    isTouching.current = true;
    isMoving.current = false;
    touchStartTime.current = Date.now();
    const touch = e.touches[0];
    touchStartY.current = touch.clientY;
    lastTouchY.current = touch.clientY;
  };

  // 处理触摸移动
  const handleTouchMove = (e) => {
    if (!isTouching.current) return;

    const touch = e.touches[0];
    const currentY = touch.clientY;
    const moveDistance = Math.abs(currentY - touchStartY.current);

    // 如果移动距离超过阈值，认为是滑动
    if (moveDistance > 5) {
      isMoving.current = true;
    }

    if (isMoving.current) {
      lastTouchY.current = currentY;
      handleLetterSelect(e);
    }
  };

  // 处理触摸结束
  const handleTouchEnd = (e) => {
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - touchStartTime.current;
    const moveDistance = Math.abs(lastTouchY.current - touchStartY.current);

    // 判断是否为点击：时间短且移动距离小
    if (touchDuration < 200 && moveDistance < 5) {
      const touch = e.changedTouches[0];
      const rect = e.currentTarget.getBoundingClientRect();
      const offsetY = touch.clientY - rect.top;
      const index = Math.floor((offsetY / rect.height) * letters.length);

      if (index >= 0 && index < letters.length) {
        handleLetterClick(letters[index], index);
      }
    }

    isTouching.current = false;
    isMoving.current = false;

    // 延迟隐藏提示
    if (showIndicator) {
      setTimeout(() => {
        setShowToast(false);
      }, 500);
    }
  };

  // 处理字母选择（滑动）
  const handleLetterSelect = (e) => {
    // 阻止默认行为
    e.stopPropagation();

    // 获取触摸点相对于索引栏的位置
    const touch = e.touches[0];
    const rect = e.currentTarget.getBoundingClientRect();
    const offsetY = touch.clientY - rect.top;
    const index = Math.floor((offsetY / rect.height) * letters.length);

    // 确保索引在有效范围内
    if (index >= 0 && index < letters.length && index !== activeIndex) {
      const letter = letters[index];
      setActiveIndex(index);

      // 显示提示
      if (showIndicator) {
        setShowToast(true);
      }

      // 触发滑动回调
      if (onLetterSelect) {
        onLetterSelect(letter, index);
      }
    }
  };

  // 处理点击字母
  const handleLetterClick = (letter: string, index: number) => {
    setActiveIndex(index);

    // 显示提示
    if (showIndicator) {
      setShowToast(true);
      setTimeout(() => {
        setShowToast(false);
      }, 500);
    }

    // 触发点击回调
    if (onLetterClick) {
      onLetterClick(letter, index);
    }
  };

  return (
    <View className={`alphabet-index-container ${className}`}>
      {/* 字母提示气泡 */}
      {showIndicator && showToast && activeIndex !== -1 && (
        <View className={`alphabet-index-indicator ${indicatorClass}`}>
          <Text className="alphabet-index-indicator-text">
            {letters[activeIndex]}
          </Text>
        </View>
      )}

      {/* 字母索引列表 */}
      <View
        ref={indexBarRef}
        className="alphabet-index-bar"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {letters.map((letter, index) => (
          <View
            key={letter}
            className={classNames('alphabet-index-item', {
              'alphabet-index-item-active': index === activeIndex,
            })}
            onClick={() => handleLetterClick(letter, index)}
          >
            <Text className="alphabet-index-item-text">{letter}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

export default AlphabetIndex;
