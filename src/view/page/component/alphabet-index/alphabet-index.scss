/* 基础样式通过标准CSS实现 */
.alphabet-index-container {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
  display: flex;
  align-items: center;
}

.alphabet-index-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0.5rem 0.25rem;
  // border-radius-left: 0.5rem;
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  // background-color: rgba(243, 244, 246, 0.8);
  // height: 70vh;
  max-height: 500px;
}

.alphabet-index-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.125rem 0;
  cursor: pointer;
  transition: all 150ms ease-in-out;
}

.alphabet-index-item-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #4B5563;
}

.alphabet-index-item-active {
  background-color: var(--primary-color, #FDD244);
  border-radius: 9999px;
  transform: scale(1.2);
}

.alphabet-index-item-active .alphabet-index-item-text {
  color: white;
  font-weight: 700;
}

.alphabet-index-indicator {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--primary-color, #FDD244);
  color: white;
  border-radius: 9999px;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  animation: fade-in 0.2s ease-out forwards;
}

.alphabet-index-indicator-text {
  font-size: 1.875rem;
  font-weight: 700;
}

/* 自定义动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translate(-10px, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(0, -50%) scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out forwards;
}
