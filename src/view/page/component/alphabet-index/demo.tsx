import { Component } from 'react'
import { View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import AlphabetIndex from './alphabet-index.component'
import './alphabet-index.scss'

export default class Index extends Component {
  state = {
    currentLetter: '',
    // 示例数据
    contacts: [
      { name: '阿里巴巴', firstLetter: 'A' },
      { name: '百度', firstLetter: 'B' },
      { name: '腾讯', firstLetter: 'T' },
      // ... 更多联系人
    ]
  }

  // 处理字母点击
  handleLetterClick = (letter: string) => {
    Taro.showToast({
      title: `点击了字母 ${letter}`,
      icon: 'none',
      duration: 1000
    })

    this.setState({ currentLetter: letter })

    // 滚动到对应的分组
    Taro.createSelectorQuery()
      .select(`#group-${letter}`)
      .boundingClientRect()
      .selectViewport()
      .scrollOffset()
      .exec(res => {
        if (res[0]) {
          Taro.pageScrollTo({
            scrollTop: res[0].top + res[1].scrollTop - 20,
            duration: 300
          })
        }
      })
  }

  // 处理字母滑动选择
  handleLetterSelect = (letter: string) => {
    this.setState({ currentLetter: letter })
  }

  render() {
    const { currentLetter } = this.state

    // 自定义字母表（可选）
    const customLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
      'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#']

    return (
      <View className='index-page'>
        <View className='page-title'>联系人列表</View>

        {/* 当前选中的字母 */}
        {currentLetter && (
          <View className='current-letter'>
            当前选中: {currentLetter}
          </View>
        )}

        {/* 联系人列表 */}
        <View className='contact-list'>
          {/* 这里渲染联系人列表 */}
        </View>

        {/* 字母索引组件 */}
        <AlphabetIndex
          letters={customLetters}
          onLetterClick={this.handleLetterClick}
          onLetterSelect={this.handleLetterSelect}
          showIndicator={true}
        />
      </View>
    )
  }
}
