.container {
	display: flex;
	flex-direction: column;
	gap: 24px;
	padding: 32px 32px calc(env(safe-area-inset-bottom) + 32px) 32px;

	> .empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;

		> .image {
			margin-top: 175px;
			margin-bottom: 8px;
			width: 400px;
			height: 400px;
		}

		> .text {
			color: #9DA3B2;
			font-size: 30px;
		}
	}
}
