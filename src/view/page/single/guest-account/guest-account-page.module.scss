@use "@/theme.scss" as t;

$label-width: 60px;
$border-base: 1px solid t.$color-grey-4;

.container {
	width: 100%;
	height: 100%;
	background: t.$color-bg;

	> .top {
		.scrollContent {
			width: calc(100% - t.$padding-16 * 2);
			padding: t.$padding-16;

			> .row {
				@extend %display-flex-row;
				margin-bottom: t.$margin-12;
				padding: t.$padding-16;
				overflow: hidden;
				font-size: t.$font-size-base;
				background: t.$color-bg;
				border-radius: t.$border-radius-lg;

				.label {
					width: $label-width;
					font-weight: bold;
				}

				.input {
					width: calc(100% - $label-width);
					padding: t.$padding-16;
					background: #f0f0f0;
					border-radius: t.$border-radius-lg;
				}
			}

			> .remark {
				margin-bottom: t.$margin-12;
				overflow: hidden;
				background: t.$color-bg;
				border-radius: t.$border-radius-lg;

				> .input {
					z-index: 0;
					height: 100px;
					padding: t.$padding-16;
					font-size: t.$font-size-base;
				}
			}

			> .operate {
				@extend %display-flex-row;
				justify-content: flex-start;
				margin-bottom: t.$margin-12;

				.activeTag {
					color: t.$color-brand;
					background-color: transparent;
				}
				.ml16 {
					margin-left: t.$margin-12;
				}
			}

			> .project {
				margin-top: t.$margin-12;
				.projectTag {
					display: inline-block;
					margin-right: t.$margin-12;

					.tag {
						@extend %display-flex-column;
					}

					.value {
						font-size: t.$font-size-sm;
					}

					:global {
						.at-tag {
							margin-bottom: t.$margin-12;
							border: $border-base;
						}
					}
				}
			}
		}
	}

	> .bottom {
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		justify-content: flex-end;
		padding: t.$padding-16;
		background: t.$color-bg;
		border-top: $border-base;
		@extend %display-flex-row;

		> .count {
			display: flex;
			flex: 1;
			flex-direction: row;
			align-items: baseline;

			> .text {
				color: #6b7080;
				font-size: t.$font-size-sm;
			}

			> .value {
				color: t.$color-error;
				font-weight: bold;
				font-size: t.$font-size-lg;
				font-family: DIN Pro-Medium, DIN Pro;
			}

			> .yuan {
				margin-left: t.$margin-6;
				color: t.$color-error;
				font-size: t.$font-size-sm;
			}

			> .detail {
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-left: t.$margin-12;
				color: t.$color-error;
				font-size: t.$font-size-sm;
			}
		}

		> .button {
			margin: 0;
			margin-left: t.$margin-12;
		}
	}
}
