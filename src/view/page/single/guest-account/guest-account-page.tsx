import React, { useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import moment from "moment";
import { Text, Textarea, View, Input, Picker } from "@tarojs/components";
import { AtTag, AtButton, AtNoticebar, AtToast, AtBadge } from "taro-ui";
import { observer } from "mobx-react";
import FrameScrollContainer from "@/view/component/frame-scroll-container/frame-scroll-container.component";
import PageContainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import GuestAccountProjectsModal from "./components/guest-account-projects/guest-account-projects";
import { CommonUtil } from "@/util/common-util";
import styles from "./guest-account-page.module.scss";

const arr = [
	{
		name: "光子嫩肤",
		code: "1",
		price: 1000,
	},
	{
		name: "超皮秒",
		code: "2",
		price: 5000,
	},
	{
		name: "水光针",
		code: "3",
		price: 2300,
	},
	{
		name: "超声炮",
		code: "4",
		price: 3300,
	},
	{
		name: "双眼皮",
		code: "5",
		price: 500,
	},
].map((item) => ({
	...item,
	label: `${item.name} （${item.price}元/次）`,
	value: item.code,
}));

const HousekeepingBillingPage: React.FC = observer(() => {
	const businessId = 1; // 商家id
	const container: PageContainerRef = useRef(null);

	const [info, setInfo] = useState({
		amount: 0, // 收入
		remark: null, // 备注
		transactionTime: null, // 关联时间
		associatedEmployeeId: null, // 关联员工id
		projectIds: [], // 所选项目Id
	});
	const [visible, setVisible] = useState(false); // 项目设置visible
	const [isOpened, setIsOpened] = useState(false); // loading
	const [staffRange, setStaffRange] = useState<
		{ name: string; code: number }[]
	>([]); // 员工列表
	const [projects, setProjects] = useState<any>([]); // 项目列表
	const [selectedData, setSelectedData] = useState<any>([]); // 已选择的项目列表

	const pickerConfig = [
		{
			title: "添加日期",
			props: {
				mode: "date",
				value: info.transactionTime,
				showValue: info?.transactionTime,
				onChange: (e) =>
					updateInfoState({ transactionTime: e.detail.value }),
			},
		},
		{
			title: "关联员工",
			className: styles.ml16,
			props: {
				mode: "selector",
				range: staffRange,
				rangeKey: "name",
				value: staffRange?.findIndex(
					(item: any) => item.code === info.associatedEmployeeId
				),
				showValue: staffRange?.find(
					(item: any) => item.code === info.associatedEmployeeId
				)?.name,
				onChange: (e) => {
					const index = e.detail.value;
					updateInfoState({
						associatedEmployeeId: staffRange[index]?.code,
					});
				},
			},
		},
	];

	useEffect(() => {
		setTimeout(() => {
			// 员工列表
			service.business.businessEmployeeController
				.getList({ businessId })
				.then((res) => {
					const list = (res?.data || []).map((item) => ({
						name: item.username,
						code: item.employeeId,
					}));
					setStaffRange(list);
				});

			// 项目列表
			setProjects(arr);
		}, 1000);
	}, []);

	const updateInfoState = (state) => {
		setInfo({ ...info, ...state });
	};

	const onCancel = () => {
		Taro.navigateBack();
	};

	/** 一些必填校验 */
	const check = (): boolean => {
		const requireds = [
			{
				title: "收入",
				key: "amount",
				verify: CommonUtil.stringIsNull(info.amount),
				message: "收入不能为空",
			},
			{
				title: "备注",
				key: "remark",
				verify: CommonUtil.stringIsNull(info.remark),
				message: "备注不能为空",
			},
			{
				title: "关联时间",
				key: "transactionTime",
				verify: CommonUtil.stringIsNull(info.transactionTime),
				message: "请选择关联时间",
			},
			{
				title: "关联员工",
				key: "associatedEmployeeId",
				verify: CommonUtil.stringIsNull(info.associatedEmployeeId),
				message: "请选择关联员工",
			},
		];

		const firstInvalid = requireds.find((item) => item?.verify);
		if (firstInvalid) {
			container.current?.openMessage({
				message: firstInvalid?.message,
				type: "warning",
			});
			return false;
		}
		return true;
	};

	const onSave = async () => {
		if (!check()) {
			return;
		}
		setIsOpened(true);
		const data = await service.business.walkInBillController.postWalkInBill(
			{
				businessId,
				...info,
				transactionTime: info.transactionTime
					? moment(info.transactionTime).format("YYYY-MM-DD HH:mm:ss")
					: null,
				projectIds: JSON.stringify(info?.projectIds),
			}
		);
		setIsOpened(false);
		if (data) {
			Taro.navigateBack();
		}
	};

	const onTagClick = (i) => {
		let tempList: string[] = [];
		let list: any = [];
		let price = 0;
		selectedData.forEach((item, index) => {
			const selectedValue =
				index === i ? item.selectedValue + 1 : item.selectedValue;
			list.push({
				...item,
				selectedValue,
			});
			if (selectedValue > 0) {
				price = price + item.price * selectedValue;
				tempList.push(`${item.label} * ${selectedValue}次`);
			}
		});

		setSelectedData(list);
		updateInfoState({ remark: tempList.join(" + "), amount: price });
	};

	const onProjectChange = (projectIds) => {
		let arrs = projects
			.filter((item) => projectIds.includes(item.value))
			.map((obj) => {
				const match = selectedData.find(
					(item) => item.value === obj.value
				);
				return match ? match : { ...obj, selectedValue: 0 };
			});

		let tempList = [];
		let price = 0;
		arrs.filter((item) => item.selectedValue > 0).forEach((obj) => {
			tempList.push(`${obj.label} * ${obj.selectedValue}次`);
			price = price + obj.price * obj.selectedValue;
		});

		updateInfoState({
			projectIds,
			remark: tempList.join(" + "),
			amount: price,
		});
		setSelectedData(arrs);
	};

	return (
		<PageContainer className={styles.container} ref={container}>
			<View className={styles.top}>
				<FrameScrollContainer innerClassName={styles.scrollContent}>
					<View className={styles.row}>
						<Text className={styles.label}>收入</Text>
						<Input
							className={styles.input}
							placeholder="输入金额"
							value={info.amount}
							onInput={(e) =>
								updateInfoState({ amount: e.detail.value })
							}
						/>
					</View>
					<View className={styles.remark}>
						<Textarea
							className={styles.input}
							placeholder="请输入备注..."
							value={info.remark}
							onInput={(e) =>
								updateInfoState({ remark: e.detail.value })
							}
						></Textarea>
					</View>
					<View className={styles.operate}>
						{pickerConfig.map(({ title, className, props }) => (
							<Picker key={title} {...props}>
								<AtTag
									type="primary"
									circle
									className={`${styles.activeTag} ${className}`}
									active
									size="small"
								>
									{props?.showValue
										? props?.showValue
										: title}
								</AtTag>
							</Picker>
						))}
					</View>
					<AtNoticebar icon="volume-plus">
						选择记账项目(多次点击会累加,点击数量可修改)
					</AtNoticebar>

					<View className={styles.project}>
						{selectedData.map((item, i) => (
							<View
								key={item.label}
								className={styles.projectTag}
							>
								<AtBadge value={item?.selectedValue}>
									<AtTag
										circle
										onClick={() => onTagClick(i)}
										size="small"
									>
										{item.label}
									</AtTag>
								</AtBadge>
							</View>
						))}
						<Text className={styles.projectTag}>
							<AtTag
								type="primary"
								circle
								active
								size="small"
								onClick={() => setVisible(true)}
							>
								+ 设置项目
							</AtTag>
						</Text>
					</View>
				</FrameScrollContainer>
			</View>

			<View className={styles.bottom}>
				<AtButton
					circle
					className={styles.button}
					onClick={() => onCancel()}
				>
					取消
				</AtButton>
				<AtButton
					type="primary"
					circle
					className={`${styles.button} ${styles.ml16}`}
					onClick={() => onSave()}
				>
					确认
				</AtButton>
			</View>

			<AtToast
				isOpened={isOpened}
				text="正在加载..."
				hasMask
				status="loading"
				duration={0}
			/>

			{visible && (
				<GuestAccountProjectsModal
					visible={visible}
					setVisible={setVisible}
					value={info.projectIds}
					checkList={projects}
					onChange={(projectIds) => onProjectChange(projectIds)}
				/>
			)}
		</PageContainer>
	);
});

export default HousekeepingBillingPage;
