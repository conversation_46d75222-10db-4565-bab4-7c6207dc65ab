import { useEffect, useState } from "react";
import { Text, View } from "@tarojs/components";
import { AtFloatLayout, AtButton, AtCheckbox } from "taro-ui";
import styles from "./index.module.scss";

const Index = ({ visible, setVisible, checkList, value, onChange }) => {
	const [checkedList, setCheckedList] = useState<any>([]);

	useEffect(() => {
		setCheckedList(value);
	}, [value]);

	const onCancel = () => {
		setVisible(false);
	};

	const onSave = () => {
		onChange(checkedList);
		setVisible(false);
	};

	return (
		<AtFloatLayout
			isOpened={visible}
			title="设置项目"
			onClose={() => setVisible(false)}
		>
			<View className={styles.container}>
				<View className={styles.list}>
					<AtCheckbox
						options={checkList}
						selectedList={checkedList}
						onChange={(e) => setCheckedList(e)}
					/>
				</View>

				<View className={styles.bottom}>
					<Text style={{ marginRight: "8px" }}>
						<AtButton
							type="secondary"
							circle
							onClick={() => onCancel()}
						>
							取消
						</AtButton>
					</Text>
					<AtButton type="primary" circle onClick={() => onSave()}>
						保存
					</AtButton>
				</View>
			</View>
		</AtFloatLayout>
	);
};

export default Index;
