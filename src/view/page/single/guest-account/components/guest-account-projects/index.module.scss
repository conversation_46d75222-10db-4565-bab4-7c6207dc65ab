@use "@/theme.scss" as t;

.container {
	.list {
		overflow-y: auto;

		.row {
			@extend %display-flex-row;
			margin-bottom: t.$margin-12;
			padding: t.$padding-12;
			font-size: t.$font-size-base;
			background-color: t.$background-color;
			border-radius: t.$border-radius-md;
			.title {
				width: 60px;
			}

			.input {
				width: calc(50% - 60px);
			}

			.unit {
				margin: 0px t.$margin-12;
			}

			.addTag {
				margin-bottom: t.$margin-12;
			}

			:global {
				.at-input {
					margin-left: t.$margin-12;
					border-radius: t.$border-radius-md;

					.weui-input {
						padding-left: t.$padding-12;
					}
				}
			}
		}
	}

	.bottom {
		@extend %display-flex-row;
		justify-self: flex-end;
	}
}
