.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #F0F0F0;

	> .top {
		flex: 1;
		overflow: hidden;
	}

	> .bottom {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 32px;
		padding: 16px 32px calc(env(safe-area-inset-bottom) + 16px) 32px;
		background: #FFFFFF;

		> .items {
			display: flex;
			flex-direction: row;
			gap: 24px;
			padding: 16px 8px 8px 8px;

			> .each {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 12px;

				> .text {
					color: #6B7080;
					font-size: 20px;
				}
			}
		}

		> .button {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88px;
			background: #FF6600;
			border-radius: 44px;
			font-size: 30px;
		}
	}
}

.scrollContent {

	> .detailImages {
		position: relative;
		width: 100%;
		height: 0;
		padding-bottom: 60%;

		> .swiper {
			position: absolute;
			width: 100%;
			height: 100%;

			.swiperImage {
				width: 100%;
				height: 100%;
			}
		}
	}

	> .info {
		display: flex;
		flex-direction: column;
		padding: 32px;
		background: #FFFFFF;

		> .header {
			display: flex;
			flex-direction: row;
			align-items: baseline;
			padding-bottom: 32px;

			> .price {
				color: #FF6600;
				font-family: DIN Pro-Medium;
				font-size: 64px;
				font-weight: bold;
			}

			> .yuan {
				padding-left: 4px;
				color: #FF6600;
				font-size: 24px;
			}

			> .vip {
				display: flex;
				flex-direction: row;
				margin-left: 20px;

				> .left {
					display: flex;
					justify-content: center;
					align-items: center;
					padding: 4px 8px;
					background: #F55858;
					color: #FFFFFF;
					font-size: 20px;
					border: 1px solid #F55858;
					border-top-left-radius: 4px;
					border-bottom-left-radius: 4px;
				}

				> .right {
					display: flex;
					justify-content: center;
					align-items: center;
					padding: 4px 8px;
					color: #F55858;
					font-size: 20px;
					border: 1px solid #F55858;
					border-top-right-radius: 4px;
					border-bottom-right-radius: 4px;
				}
			}

			> .count {
				flex: 1;
				color: #6B7080;
				font-size: 24px;
				text-align: right;
			}
		}

		> .title {
			color: #1A1A1A;
			font-size: 36px;
			font-weight: bold;
		}

		> .description {
			padding: 16px 0;
			color: #6B7080;
			font-size: 28px;
		}
	}

	> .richText {
		width: 100%;
	}
}
