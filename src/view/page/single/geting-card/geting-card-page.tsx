/* eslint-disable react-hooks/rules-of-hooks */
import {
  View,
  Input,
  Picker,
  But<PERSON>,
  Text,
  PageContainer,
} from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import styles from './geting-card-page.module.scss';

const GetingCard = () => {
  const [show, setShow] = useState(false);
  const [state, setState] = useState({
    name: '',
    phone: '',
    birthday: '',
    sex: '',
    remark: '',
  });
  const { name, phone, birthday, sex, remark } = state;

  const useStateState = (updatedValues) => {
    setState({ ...state, ...updatedValues });
  };

  // const chooseImage = () => {
  //   Taro.chooseMedia({ mediaType: ['image'], count: 1 })
  //     .then(async (res) => {
  //       // let tempFilePath = res.tempFiles[0].tempFilePath;
  //       // // 上传图片
  //       // let uploadResult = await PlatformUtil.uploadFile(
  //       //   tempFilePath,
  //       //   props.uploadType ? props.uploadType : 'common'
  //       // );
  //       // if (uploadResult && uploadResult.success) {
  //       //   let newArr = [...imageList, ...[uploadResult.data]];
  //       //   setImageList(newArr);
  //       //   props.onUploadSuccess && props.onUploadSuccess(newArr);
  //       // } else {
  //       //   // setImageList([...imageList, ...[tempFilePath]]);
  //       //   // -----
  //       //   // let newArr = [...imageList, ...[tempFilePath]];
  //       //   // setImageList(newArr);
  //       //   // props.onUploadSuccess && props.onUploadSuccess(newArr);
  //       //   // -----
  //       //   props.onUploadError && props.onUploadError();
  //       // }
  //     })
  //     .catch((e) => {
  //       console.warn(e);
  //     });
  // };

  return (
    <View className={styles.memberEdit}>
      <View>办卡</View>
      <Button type='default' onClick={() => setShow(true)}>
        <AtIcon value='add' size={20} color='#9DA3B2'></AtIcon>
        <Text>点击选择想要办理会员卡</Text>
      </Button>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>客户姓名</View>
        <Input
          className={styles.formItemWepper}
          onInput={(e) => {
            useStateState({ name: e.detail.value });
          }}
        />
      </View>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>手机号码</View>
        <Input
          className={styles.formItemWepper}
          onInput={(e) => {
            useStateState({ phone: e.detail.value });
          }}
        />
      </View>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>客户生日</View>
        <Picker
          mode='date'
          start='2015-09-01'
          end='2017-09-01'
          onChange={(e) => {
            useStateState({ birthday: e.detail.value });
          }}
        >
          <View>当前选择:{birthday}</View>
        </Picker>
      </View>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>客户性别</View>
        <Input className={styles.formItemWepper} />
      </View>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>备注内容</View>
        <Input
          className={styles.formItemWepper}
          onInput={(e) => {
            useStateState({ remark: e.detail.value });
          }}
        />
      </View>
      <Button
        type='primary'
        onClick={() => {
          console.log(state);
        }}
      >
        保存
      </Button>
      <Button>取消</Button>

      <PageContainer show={show} onClickOverlay={() => setShow(false)} position='bottom' />
    </View>
  );
};

export default GetingCard;
