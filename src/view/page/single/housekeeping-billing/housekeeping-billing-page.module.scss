.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #F0F0F0;

	> .top {
		flex: 1;
		position: relative;
		overflow: hidden;
	}

	> .bottom {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 16px 32px calc(env(safe-area-inset-bottom) + 16px) 32px;
		width: calc(100% - 64px);
		background: #FFFFFF;
		border-top: 1px solid #EAECF0;

		> .count {
			flex: 1;
			display: flex;
			flex-direction: row;
			align-items: baseline;

			> .text {
				color: #6B7080;
				font-size: 24px;
			}

			> .value {
				color: #FF6600;
				font-size: 48px;
				font-family: DIN Pro-Medium, DIN Pro;
				font-weight: bold;
			}

			> .yuan {
				margin-left: 8px;
				color: #FF6600;
				font-size: 24px;
			}

			> .detail {
				display: flex;
				flex-direction: row;
				align-items: center;
				gap: 4px;
				margin-left: 24px;
				color: #FF6600;
				font-size: 24px;
			}
		}

		> .button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 256px;
			height: 88px;
			background: #FF6600;
			border-radius: 44px;
			font-size: 30px;
		}
	}
}

.scrollContent {
	display: flex;
	flex-direction: column;
	gap: 24px;
	padding: 32px;
	width: calc(100% - 64px);

	> .list {
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;
	}

	> .service {
		display: flex;
		flex-direction: column;
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;

		> .each {
			border-bottom: 1px solid #EFF1F5;
		}

		> .each:last-child {
			border-bottom: 0;
		}
	}

	> .pet {
		display: flex;
		flex-direction: row;
		padding: 24px;
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;

		> .text {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 8px;

			> .title {
				color: #1A1A1A;
				font-size: 28px;
				font-weight: bold;
			}

			> .tip {
				color: #F55858;
				font-size: 22px;
			}
		}

		> .check {
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-right: 8px;
		}
	}

	> .remark {
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;

		> .input {
			padding: 24px;
			height: 180px;
			font-size: 28px;
			z-index: 0;
		}
	}
}
