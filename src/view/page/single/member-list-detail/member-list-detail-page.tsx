/* eslint-disable react-hooks/rules-of-hooks */
import {
  View,
  Image,
  Input,
  Picker,
  Button,
  RadioGroup,
  Radio,
} from '@tarojs/components';
import { useState } from 'react';
import img from '@/assets/image/goods-1.png';
import styles from './member-list-detail-page.module.scss';

const MemberDeatilEdit = () => {
  const [state, setState] = useState({
    name: '',
    phone: '',
    birthday: '',
    sex: '',
    remark: '',
  });
  const { name, phone, birthday, sex, remark } = state;

  const useStateState = (updatedValues) => {
    setState({ ...state, ...updatedValues });
  };

  return (
    <View className={styles.memberEdit}>
      <View>修改会员卡资料</View>
      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>会员等级</View>
        <Picker
          mode='selector'
          range={['等级1', '等级2', '等级3']}
          // onChange={this.onChange}
        >
          <View>当前选择：</View>
        </Picker>
      </View>

      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>卡面Url</View>
        <Picker
          mode='selector'
          range={['url1', 'url2', 'url3']}
          // onChange={this.onChange}
        >
          <View>当前选择：</View>
        </Picker>
      </View>

      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>会员卡对应折扣设置</View>
        <Input type='number' className={styles.formItemWepper} />
      </View>

      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>状态</View>
        <RadioGroup className={styles.formItemWepper}>
          <Radio value='1'>启用</Radio>
          <Radio value='2'>停用</Radio>
        </RadioGroup>
      </View>

      <View className={styles.formItem}>
        <View className={styles.formItemLabel}>会员人数</View>
      </View>

      <Button
        type='primary'
        onClick={() => {
          console.log(state);
        }}
      >
        保存
      </Button>
      <Button>取消</Button>
    </View>
  );
};

export default MemberDeatilEdit;
