@use "@/theme.scss" as t;

.container {
	.top {
		padding: t.$padding-16;
		color: #fff;
		background-color: t.$color-brand;
		@extend %display-flex-row;

		.title {
			@extend %display-flex-column;
			.person {
				font-weight: bold;
				font-size: t.$font-size-base;
			}
			.total {
				font-size: t.$font-size-sm;
			}
		}
	}

	.bottom {
		.list {
			background-color: t.$background-color;

			.listItem {
				position: relative;
				margin: t.$container-margin;
				padding: t.$padding-8;
				background-color: #fff;
				border-radius: t.$border-radius-lg;
				@extend %display-flex-row;

				.status {
					position: absolute;
					top: 0;
					left: 0;
					padding: 1px 3px;
					color: #fff;
					font-size: t.$font-size-xs;
					border-radius: 4px;
				}

				.openStatus {
					background-color: t.$color-success;
				}

				.closeStatus {
					background-color: t.$color-error;
				}

				.content {
					@extend %display-flex-column;

					.title {
						font-size: t.$font-size-base;
					}

					.sub {
						color: #999;
						font-size: t.$font-size-sm;
					}
				}
				.optTag {
					margin-left: t.$margin-6;
				}
			}
		}
	}
}
