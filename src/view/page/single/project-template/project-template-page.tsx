import React, { useCallback, useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { Text, View, ScrollView } from "@tarojs/components";
import { AtTag, AtNoticebar, AtLoadMore } from "taro-ui";
import PageContainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import styles from "./project-template-page.module.scss";

const btnsMap = {
	add: {
		title: "+ 添加模版",
		url: "/view/page/single/project-add-template/project-add-template-page",
	},
	edit: {
		title: "修改",
		url: "/view/page/single/project-add-template/project-add-template-page",
	},
};
const Index: React.FC = () => {
	const container: PageContainerRef = useRef(null);
	const [{ dataSource, total }, setDataSource] = useState<any>({
		dataSource: [],
		total: 0,
	});
	const pageSize = 10;
	const [pageNum, setPageNum] = useState(1);
	const [status, setStatus] = useState<"more" | "noMore" | "loading">("more"); // more-加载更多 loading-加载中 noMore-无更多数据
	const [clientHeight, setClientHeight] = useState(0); // 可视区域高度

	// 可视区域高度
	useEffect(() => {
		const query = Taro.createSelectorQuery();
		query
			.select("#scrollView")
			.boundingClientRect((rect: any) => {
				rect && setClientHeight(rect?.height);
			})
			.exec();
	}, []);

	useEffect(() => {
		getList();
	}, [pageNum]);

	const getList = useCallback(() => {
		setStatus("loading");
		service.business.appointmentTemplateController
			.getAppointmentTemplateList({
				pageNum,
				pageSize,
			})
			.then((res) => {
				if (res?.data) {
					setDataSource({
						dataSource: dataSource?.concat(res?.data) ?? dataSource,
						total: res?.total ?? 0,
					});
					setStatus(
						pageNum * pageSize < res?.total ? "more" : "noMore"
					);
				}
			});
	}, []);

	// 滚动事件
	const onScroll = (e) => {
		const { scrollTop, scrollHeight } = e.detail;

		// 判断是否滚动到底部
		if (scrollTop + clientHeight >= scrollHeight - 30) {
			if (pageNum * pageSize < total) {
				setPageNum((prev) => prev + 1);
				setStatus("more");
			} else {
				setStatus("noMore");
			}
		}
	};
	const onLinkTo = (url, id?: number) => {
		Taro.navigateTo({ url: id ? `${url}?templateId=${id}` : url });
	};

	return (
		<PageContainer className={styles.container} ref={container}>
			<View className={styles.top}>
				<View className={styles.title}>
					<Text className={styles.person}>项目模版</Text>
					<Text className={styles.total}>共有{total}份模版</Text>
				</View>

				<AtTag circle active onClick={() => onLinkTo(btnsMap.add.url)}>
					{btnsMap.add.title}
				</AtTag>
			</View>
			<View className={styles.bottom}>
				<AtNoticebar>商家可新增、编辑、删除模版</AtNoticebar>
				<View className={styles.list}>
					<ScrollView
						scrollY
						scrollTop={0}
						style={{
							height: `calc(100vh - 92px)`,
						}}
						id="scrollView"
						lowerThreshold={20}
						upperThreshold={20}
						onScroll={onScroll}
					>
						{dataSource.map((item) => (
							<View
								className={styles.listItem}
								key={item.employeeId}
							>
								<View
									className={`${styles.status} ${
										item.status
											? styles.openStatus
											: styles.closeStatus
									}`}
								>
									{item.status ? "启用" : "禁用"}
								</View>
								<View className={styles.content}>
									<Text className={styles.title}>
										模版名：{item.templateName ?? "-"}
									</Text>
									<Text className={styles.sub}>
										备注：{item.remark}
									</Text>
									<Text className={styles.sub}>
										关联员工：{item.employeeList}
									</Text>
									<Text className={styles.sub}>
										接单模式：{item.takeOrdersMode}
									</Text>
									<Text className={styles.sub}>
										预约间隔时长：{item.intervalDuration}
									</Text>
								</View>
								<View>
									{[btnsMap.edit].map((obj) => (
										<Text
											className={styles.optTag}
											key={obj.title}
										>
											<AtTag
												circle
												active
												type="primary"
												size="small"
												onClick={() =>
													onLinkTo(
														obj.url,
														item.templateId
													)
												}
											>
												{obj.title}
											</AtTag>
										</Text>
									))}
								</View>
							</View>
						))}
						<AtLoadMore status={status} />
					</ScrollView>
				</View>
			</View>
		</PageContainer>
	);
};

export default Index;
