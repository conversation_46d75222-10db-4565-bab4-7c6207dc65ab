@use "@/theme.scss" as t;

.container {
	padding: 32px 0;
	background: #ffffff;

	.avatar {
		@extend %display-flex-row;
		align-items: end;
		align-items: baseline;
		margin-left: 24px;
		padding: 36px 24px;
		padding-left: 4px;
		border-bottom: 1px solid #f4f6fa;

		.title {
			width: 150px;
			color: #1a1a1a;
			font-weight: bold;
			font-size: 32px;
		}
	}

	.obts {
		padding: t.$padding-16;
		.mtb {
			margin-top: 32px;
			margin-bottom: 32px;
		}
	}

	:global {
		.at-modal__content {
			min-height: auto;
		}
	}
}
