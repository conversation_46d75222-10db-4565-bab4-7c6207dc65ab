import React, { useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import { AtButton, AtToast, AtModal } from "taro-ui";
import useStore from "@/hook/store";
import { GlobalInfoStore } from "@/store/global-info.store";
import PageContainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import RenderTypeItem from "@/view/component/render-type/render-type-item.component";
import { CommonUtil } from "@/util/common-util";
import { fieldsConfig } from "@/util/fieldsMap";
import styles from "./project-add-template.module.scss";

interface ProjectTemplateInfo {
	templateName?: string; // 模板名称
	projectIds?: number[]; // 项目列表
	dayCanAppointmentTime?: string; // 每日可预约时间
	meanwhileReceptionNum?: number; // 同时接待人数
	appointmentNum?: number; // 预约人数
	noteNotifySwitch?: boolean; // 短信通知开关
	wxNotifySwitch?: boolean; // 微信通知开关
	notifyPhone?: boolean; // 电话通知开关
	takeOrdersMode?: string; // 接单模式
	employeeIds?: number[]; // 关联员工列表
	weekCanAppointmentDate?: string; // 每周可预约日期
	status?: string; // 状态
	userId?: string; // 用户ID
	username?: string; // 用户名
	password?: string; // 密码
}
const AddressInputPage: React.FC = () => {
	const container: PageContainerRef = useRef(null);
	const globalInfo: GlobalInfoStore = useStore().globalInfoStore;
	const [isEdit, setIsEdit] = useState(false); // 当前是否处于编辑态
	const [info, setInfo] = useState<ProjectTemplateInfo>({});
	const [isOpened, setIsOpened] = useState(false);
	const [confirmType, setConfirmType] = useState<"delete" | "reset" | null>(
		null
	); // delete-删除 reset-重置
	const [staffRange, setStaffRange] = useState<
		{ name: string; code: number }[]
	>([]); // 员工列表

	const { templateId } = Taro.getCurrentInstance().router?.params ?? {};

	/** 模版编辑信息 */
	useEffect(() => {
		if (templateId) {
			setIsEdit(true);
			service.business.appointmentTemplateController
				.getAppointmentTemplateTemplateId({ templateId })
				.then((data) => {
					data && setInfo(data);
				});
		}
	}, [templateId]);

	useEffect(() => {
		// 根据商家Id获取员工列表
		service.business.businessEmployeeController
			.getList({ businessId: globalInfo.businessId })
			.then((res) => {
				const list = (res?.data || []).map((item) => ({
					name: item.username,
					code: item.employeeId,
				}));
				setStaffRange(list);
			});

		// 项目列表
	}, []);

	const config = [
		{ ...fieldsConfig.templateName, required: true },
		{
			...fieldsConfig.projectIds,
			range: globalInfo.projectsData,
			required: true,
		},
		fieldsConfig.dayCanAppointmentTime,
		{ ...fieldsConfig.meanwhileReceptionNum, titleWidth: 60 },
		{ ...fieldsConfig.appointmentNum, titleWidth: 60 },
		fieldsConfig.noteNotifySwitch,
		fieldsConfig.wxNotifySwitch,
		{ ...fieldsConfig.notifyPhone, required: true },
		{ ...fieldsConfig.takeOrdersMode, required: true },
		{ ...fieldsConfig.employeeIds, range: staffRange, required: true },
		fieldsConfig.weekCanAppointmentDate,
		fieldsConfig.status,
	];

	const updateState = (state) => {
		setInfo({ ...info, ...state });
	};

	const onLinkTo = () => {
		Taro.navigateTo({
			url: "/view/page/single/project-template/project-template-page",
		});
	};
	const onSave = async () => {
		if (check()) {
			setIsOpened(true);
			const API = isEdit
				? "putAppointmentTemplate"
				: "postAppointmentTemplate";
			const data = await service.business.appointmentTemplateController[
				API
			]({
				...info,
				projectIds: JSON.stringify([info?.projectIds]),
				employeeIds: JSON.stringify([info?.employeeIds]),
			});
			if (data) {
				console.log("****", data);
				onLinkTo();
			} else {
				container.current?.openMessage({
					message: "保存失败",
					type: "error",
				});
			}
			setIsOpened(false);
		}
	};

	const onDelete = () => {
		setIsOpened(true);
		service.business.appointmentTemplateController
			.deleteAppointmentTemplateTemplateId({
				templateId,
			})
			.then((res) => {
				if (res) {
					onLinkTo();
				}
				setIsOpened(false);
			});
	};

	/** 一些必填校验 */
	const check = (): boolean => {
		const requireds = [
			{
				...fieldsConfig.projectIds,
				verify: CommonUtil.stringIsNull(
					info?.[fieldsConfig.projectIds.key]
				),
				message: "项目名称不能为空",
			},
			{
				...fieldsConfig.employeeIds,
				verify: CommonUtil.stringIsNull(
					info?.[fieldsConfig.employeeIds.key]
				),
				message: "关联员工不能为空",
			},
		];

		const firstInvalid = requireds.find((item) => item?.verify);
		if (firstInvalid) {
			container.current?.openMessage({
				message: firstInvalid?.message,
				type: "warning",
			});
			return false;
		}
		return true;
	};

	/** 二次确认框 */
	const confirmMap = confirmType
		? {
				delete: {
					title: "删除",
					content: `确定删除该模版？`,
					onConfirm: onDelete,
				},
		  }[confirmType]
		: {};

	return (
		<PageContainer className={styles.container} ref={container}>
			{config.map((item) => (
				<RenderTypeItem
					{...item}
					itemKey={item.key}
					value={info?.[item.key]}
					onChange={updateState}
				/>
			))}

			<View className={styles.obts}>
				<AtButton type="primary" onClick={() => onSave()}>
					保存
				</AtButton>
				{isEdit && (
					<View style={{ marginTop: 8 }}>
						<AtButton
							type="secondary"
							onClick={() => setConfirmType("delete")}
						>
							删除
						</AtButton>
					</View>
				)}
			</View>

			<AtToast
				isOpened={isOpened}
				text="正在加载..."
				hasMask
				status="loading"
				duration={0}
			></AtToast>

			<AtModal
				isOpened={Boolean(confirmType)}
				{...confirmMap}
				cancelText="取消"
				confirmText="确认"
				onClose={() => setConfirmType(null)}
				onCancel={() => setConfirmType(null)}
			/>
		</PageContainer>
	);
};

export default AddressInputPage;
