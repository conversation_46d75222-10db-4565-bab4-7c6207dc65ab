import React, { useEffect, useState, useCallback, useRef } from "react";
import { observer } from "mobx-react";
import { View, ScrollView } from "@tarojs/components";
import Taro from "@tarojs/taro"; // 导入Taro
import { postReservationTemplateProjectList } from "@/service/business/reservationTemplateController";
import { AtActivityIndicator } from "taro-ui";
import PageWithNav, {
  PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import {
  MemberHeader,
  ServiceSearch,
  ServiceCategory,
  ServiceItem,
  CartFooter,
  CartPreview,
  CategoryItem,
  Service,
  FlyingBall
} from './components';
import { debounce } from '@/helper/debounce';
import EmptyComponent from './components/EmptyComponent';
import memberAvatar from '@/assets/image/common/member-user.png';

// 定义挂单类型
interface HoldOrder {
  id: string;
  items: { [key: string]: number };
  totalAmount: number;
  totalItems: number;
  timestamp: number;
}

const MemberCardPage: React.FC = observer(() => {
  const container: React.RefObject<PageContainerRef> = useRef(null);
  const [searchValue, setSearchValue] = useState('');
  const [services, setServices] = useState<Service[]>([]);
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [activeCategory, setActiveCategory] = useState('');
  const [cart, setCart] = useState<{ [key: string]: number }>({});
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [showCartPreview, setShowCartPreview] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const cartFooterId = 'cart-footer'; // Use an ID instead of ref
  const [flyingBalls, setFlyingBalls] = useState<Array<{ id: string, startPos: { x: number, y: number }, endPos: { x: number, y: number } }>>([]);

  // 挂单功能相关状态
  const [holdOrders, setHoldOrders] = useState<HoldOrder[]>([]);
  const handleHeightChange = (navHeight: number) => {
    console.log("navHeight", navHeight);
  };
  const debouncedSearch = useCallback(debounce((value: string) => {
    if (value) {
      filterServices(value, services);
    } else if (activeCategory) {
      filterServicesByCategory(activeCategory, services);
    } else {
      setFilteredServices(services);
    }
  }, 300), [services, activeCategory]);

  useEffect(() => {
    // 获取项目列表
    setIsLoading(true);
    setError('');

    postReservationTemplateProjectList({} as any)
      .then((res: any) => {
        let projectData;

        // 处理不同的API返回格式
        if (res?.data) {
          projectData = res.data;
        } else if (Array.isArray(res)) {
          projectData = res;
        } else {
          projectData = [];
        }

        if (!projectData || projectData.length === 0) {
          projectData = [];
        }

        const mappedItems = projectData.map((item: any) => {
          const employeeInfo = Array.isArray(item.employees) ? item.employees.map((emp: any) => ({
            templateEmployeeId: emp.templateEmployeeId,
            employeeNickname: emp.employeeNickname,
            avatarUrl: emp.avatarUrl || 'https://img.yzcdn.cn/vant/cat.jpeg',
            employeeTags: emp.employeeTags || [],
            seq: emp.seq || 0
          })) : [];

          return {
            id: item.templateProjectId?.toString() || '',
            name: item.projectName || '',
            price: item.projectDiscPrice || 0,
            originalPrice: item.projectOrigPrice || 0,
            image: item.image || 'https://img.yzcdn.cn/vant/cat.jpeg',
            employees: employeeInfo,
            count: 0,
            category: item.projectName
          };
        });

        const categoryList: CategoryItem[] = Array.from(new Set(mappedItems.map(item => item.category))).map(category => ({
          id: category as string,
          name: category as string
        }));

        setServices(mappedItems);
        setCategories(categoryList);
        setFilteredServices(mappedItems);

        setActiveCategory('all');
      })
      .catch((err) => {
        setError('获取项目列表失败，请稍后重试');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    // 计算购物车总金额和总数量
    let amount = 0;
    let items = 0;

    Object.keys(cart).forEach(serviceId => {
      const service = services.find(s => s.id === serviceId);
      if (service) {
        amount += service.price * cart[serviceId];
        items += cart[serviceId];
      }
    });

    setTotalAmount(amount);
    setTotalItems(items);

    // 更新服务项目列表中的数量
    const updatedServices = services.map(service => ({
      ...service,
      count: cart[service.id] || 0
    }));

    // 根据条件过滤服务项目
    if (searchValue) {
      filterServices(searchValue, updatedServices);
    } else if (activeCategory) {
      filterServicesByCategory(activeCategory, updatedServices);
    } else {
      setFilteredServices(updatedServices);
    }
  }, [cart, services, activeCategory]);

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  const handleSearch = (value: string) => {
    if (value) {
      filterServices(value, services);
    }
  };

  const filterServices = (value: string, serviceList = services) => {
    if (!value.trim()) {
      if (activeCategory) {
        filterServicesByCategory(activeCategory, serviceList);
      } else {
        setFilteredServices(serviceList);
      }
      return;
    }

    const filtered = serviceList.filter(service =>
      service.name.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredServices(filtered);
  };

  const filterServicesByCategory = (categoryId: string, serviceList = services) => {
    if (categoryId === 'all') {
      setFilteredServices(serviceList);
      return;
    }

    const filtered = serviceList.filter(service =>
      service.name.startsWith(categoryId)
    );
    setFilteredServices(filtered);
  };

  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    setSearchValue(''); // 清空搜索内容
    filterServicesByCategory(categoryId, services);
  };

  const handleAddToCart = (serviceId: string, startPos?: { x: number, y: number }) => {
    setCart(prevCart => {
      const newCart = { ...prevCart };
      newCart[serviceId] = (newCart[serviceId] || 0) + 1;
      return newCart;
    });

    // If startPos is provided, animate a flying ball using Taro's createSelectorQuery
    if (startPos) {
      const query = Taro.createSelectorQuery();
      query
        .select(`#${cartFooterId}`)
        .boundingClientRect((cartRect: any) => {
          if (cartRect) {
            // Use a more reliable method to estimate the end position
            const endPos = {
              x: cartRect.left + 55, // Estimate the cart icon's position
              y: cartRect.top + cartRect.height / 2
            };

            const newBall = {
              id: `ball-${Date.now()}`,
              startPos,
              endPos
            };

            setFlyingBalls(prev => [...prev, newBall]);
          }
        })
        .exec();
    }
  };

  const handleRemoveFromCart = (serviceId: string) => {
    setCart(prevCart => {
      const newCart = { ...prevCart };
      if (newCart[serviceId] > 0) {
        newCart[serviceId] -= 1;
        if (newCart[serviceId] === 0) {
          delete newCart[serviceId];
        }
      }
      return newCart;
    });
  };

  const handleClearCart = () => {
    setCart({});
    setShowCartPreview(false);
  };

  const handleCheckout = () => {
    if (totalItems > 0) {
      // 添加跳转到开单结账页面的逻辑
      Taro.navigateTo({
        url: '/member/order-management/order-management-page',
        success: function () {
          console.log('跳转到开单结账页面成功');
        },
        fail: function (err) {
          console.error('跳转失败:', err);
          Taro.showToast({
            title: '跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  };

  // 购物车图标点击事件 - 显示购物车预览
  const handleCartClick = () => {
    if (totalItems > 0) {
      setShowCartPreview(true);
    }
  };

  const getCartItems = () => {
    return Object.keys(cart).map(serviceId => {
      const service = services.find(s => s.id === serviceId);
      return {
        service: service!,
        count: cart[serviceId]
      };
    }).filter(item => item.service);
  };

  const handleBallAnimationEnd = (ballId: string) => {
    setFlyingBalls(prev => prev.filter(ball => ball.id !== ballId));
  };

  // 处理挂单功能
  const handleHoldOrder = () => {
    if (totalItems > 0) {
      // 创建新的挂单
      const newHoldOrder: HoldOrder = {
        id: `order-${Date.now()}`,
        items: { ...cart },
        totalAmount,
        totalItems,
        timestamp: Date.now()
      };

      // 保存挂单到本地存储
      const existingHoldOrders = Taro.getStorageSync('holdOrders') || [];
      const updatedHoldOrders = [...existingHoldOrders, newHoldOrder];
      Taro.setStorageSync('holdOrders', updatedHoldOrders);

      // 更新状态
      setHoldOrders(updatedHoldOrders);

      // 清空当前购物车
      setCart({});

      // 显示提示
      Taro.showToast({
        title: '挂单成功',
        icon: 'success',
        duration: 2000
      });
    }
  };

  // 加载挂单数据
  useEffect(() => {
    const storedHoldOrders = Taro.getStorageSync('holdOrders') || [];
    setHoldOrders(storedHoldOrders);
  }, []);

  const renderContent = () => {
    if (isLoading) {
      return (
        <View className="flex flex-col justify-center items-center h-[calc(100vh-240px)] bg-white rounded-[16px] m-2 p-5 shadow-[0_4px_16px_rgba(0,0,0,0.06)]">
          <AtActivityIndicator mode="center" content="加载中..." color="#FDD244" />
        </View>
      );
    }

    if (error || filteredServices.length === 0) {
      return <EmptyComponent message={error || '暂无数据'} />;
    }

    return (
      <View className="flex bg-white rounded-[16px] m-2 h-[calc(100vh-240px)] overflow-hidden shadow-[0_4px_16px_rgba(0,0,0,0.06)]">
        <ServiceCategory
          categories={[
            { id: 'all', name: '全部' },
            ...categories
          ]}
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
        />
        <ScrollView
          scrollY
          className="flex-1 h-full overflow-y-auto"
          scrollWithAnimation
        >
          {filteredServices.map(service => (
            <ServiceItem
              key={service.id}
              service={service}
              onAddToCart={handleAddToCart}
              onRemoveFromCart={handleRemoveFromCart}
            />
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <PageWithNav
      showNavBar
      title="开单"
      containerRef={container}
      onHeightChange={handleHeightChange}
    >
      <View className="p-0 bg-[#f8f9fa] min-h-screen box-border">
        <View className="p-4 pt-6 pb-2 bg-gradient-to-b  to-[#f8f9fa] rounded-b-[24px] shadow-sm mb-2">
          <MemberHeader
            avatarUrl={memberAvatar}
            memberName="顾客姓名"
            lastVisit="2023-08-15"
            isVip={true}
            onCardClick={() => console.log('办卡')}
            onChangeCustomerClick={() => console.log('更换顾客')}
          />
        </View>

        <ServiceSearch
          value={searchValue}
          onChange={handleSearchChange}
          onSearch={handleSearch}
        />

        {renderContent()}

        <CartFooter
          totalItems={totalItems}
          totalAmount={totalAmount}
          onCheckout={handleCheckout}
          onCartClick={handleCartClick}
          onHoldOrder={handleHoldOrder}
          id={cartFooterId} // Add an ID prop instead of ref
        />

        <CartPreview
          visible={showCartPreview}
          cartItems={getCartItems()}
          onClose={() => setShowCartPreview(false)}
          onRemoveItem={handleRemoveFromCart}
          onAddItem={handleAddToCart}
          onClearCart={handleClearCart}
          onConfirm={() => {
            setShowCartPreview(false);
            handleCheckout();
          }}
          totalAmount={totalAmount}
        />

        {/* Flying Balls Animation */}
        {flyingBalls.map(ball => (
          <FlyingBall
            key={ball.id}
            startPos={ball.startPos}
            endPos={ball.endPos}
            onAnimationEnd={() => handleBallAnimationEnd(ball.id)}
          />
        ))}
      </View>
    </PageWithNav>
  );
});

export default MemberCardPage;
