import React, { useState } from 'react';
import { View, Input } from '@tarojs/components';
import { AtIcon } from 'taro-ui';

interface ServiceSearchProps {
  value: string;
  placeholder?: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
}

const ServiceSearch: React.FC<ServiceSearchProps> = ({
  value,
  placeholder = '搜索项目名称、价格',
  onChange,
  onSearch
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e) => {
    const newValue = e.detail.value;
    onChange(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleConfirm = (e) => {
    onSearch(e.detail.value);
  };

  return (
    <View className='mx-2 mb-4 mt-3'>
      <View
        className={`flex items-center ${isFocused
          ? 'bg-white shadow-[0_0_0_2px_rgba(253,210,68,0.4)]'
          : 'bg-white shadow-[0_2px_8px_rgba(0,0,0,0.08)]'
          } rounded-[18px] px-3.5 py-1.5 h-[36px] relative border ${isFocused ? 'border-[#FDD244]' : 'border-[#eeeeee]'
          } transition-all duration-200 ease-in-out`}
      >
        <View className='mr-2 flex items-center'>
          <AtIcon value='search' size='16' color={isFocused ? '#FDD244' : '#999'} />
        </View>
        <Input
          className='flex-1 border-none bg-transparent text-sm text-[#333] h-full p-0 outline-none flex items-center placeholder:text-[#999]'
          value={value}
          placeholder={placeholder}
          onInput={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onConfirm={handleConfirm}
          confirmType='search'
        />
        {value && (
          <View
            className='ml-2 flex items-center p-1 hover:bg-[#f5f5f5] rounded-full'
            onClick={() => onChange('')}
          >
            <AtIcon value='close-circle' size='16' color='#999' />
          </View>
        )}
      </View>
    </View>
  );
};

export default ServiceSearch; 
