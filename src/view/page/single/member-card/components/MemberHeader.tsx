import React from 'react';
import { View, Text, Image } from '@tarojs/components';

interface MemberHeaderProps {
  avatarUrl: string;
  memberName: string;
  lastVisit: string;
  isVip: boolean;
  onCardClick?: () => void;
  onChangeCustomerClick?: () => void;
}

const MemberHeader: React.FC<MemberHeaderProps> = ({
  avatarUrl,
  memberName,
  lastVisit,
  isVip,
  onCardClick,
  onChangeCustomerClick
}) => {
  return (
    <View className='bg-[#333] p-4 rounded-lg flex flex-col text-white'>
      <View className='flex items-center mb-4'>
        <Image className='w-[60px] h-[60px] rounded-full mr-3' src={avatarUrl} mode='aspectFill' />
        <View className='flex-1'>
          <View className='flex items-center mb-1'>
            <Text className='text-lg font-medium mr-2'>{memberName}</Text>
            {isVip && <View className='bg-gradient-to-r from-[#e0b973] to-[#ad8832] rounded-[10px] px-2 py-0.5 text-xs font-bold text-white'>VIP</View>}
          </View>
          <Text className='text-xs font-normal text-white leading-[17px] text-left'>上次到店 {lastVisit}</Text>
        </View>
      </View>
      <View className='flex justify-end gap-3'>
        <View
          className='bg-gradient-to-br from-[#FFF0DF] to-[#FDD1A0] text-[#333] py-1.5 px-3 rounded-[13px] text-sm text-center'
          onClick={onCardClick}
        >
          办卡
        </View>
        <View
          className='bg-gradient-to-br from-[#FFF0DF] to-[#FDD1A0] text-[#333] py-1.5 px-3 rounded-[13px] text-sm text-center'
          onClick={onChangeCustomerClick}
        >
          更换顾客
        </View>
      </View>
    </View>
  );
};

export default MemberHeader; 
