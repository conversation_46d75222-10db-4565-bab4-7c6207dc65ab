import React from 'react';
import { View, Text } from '@tarojs/components';

export interface CategoryItem {
  id: string;
  name: string;
}

interface ServiceCategoryProps {
  categories: CategoryItem[];
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

const ServiceCategory: React.FC<ServiceCategoryProps> = ({
  categories,
  activeCategory,
  onCategoryChange
}) => {
  return (
    <View className='w-[90px] min-w-[90px] bg-[#f9f9f9] h-full overflow-y-auto'>
      {categories.map(category => (
        <View
          key={category.id}
          className={`p-4 px-2 text-center relative ${activeCategory === category.id
            ? 'bg-white font-medium before:content-[""] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-[3px] before:h-4 before:bg-[#ffcc00] before:rounded-r-[3px]'
            : ''
            }`}
          onClick={() => onCategoryChange(category.id)}
        >
          <Text className='text-sm text-[#333] leading-[1.4] overflow-hidden text-ellipsis line-clamp-2'>{category.name}</Text>
        </View>
      ))}
    </View>
  );
};

export default ServiceCategory; 
