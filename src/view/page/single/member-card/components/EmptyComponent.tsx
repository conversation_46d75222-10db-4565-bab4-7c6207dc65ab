import React from 'react';
import { View, Text } from '@tarojs/components';

interface EmptyComponentProps {
  message: string;
}

const EmptyComponent: React.FC<EmptyComponentProps> = ({ message }) => {
  return (
    <View className="flex justify-center items-center h-full text-[#999]">
      <Text className="text-lg text-center">{message}</Text>
    </View>
  );
};

export default EmptyComponent; 
