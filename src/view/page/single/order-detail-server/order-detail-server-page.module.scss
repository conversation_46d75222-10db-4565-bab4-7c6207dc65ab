.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #F0F0F0;

	> .top {
		flex: 1;
		position: relative;
		overflow: hidden;
	}

	> .bottom {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 16px 32px calc(env(safe-area-inset-bottom) + 16px) 32px;
		width: calc(100% - 64px);
		background: #FFFFFF;
		border-top: 1px solid #EAECF0;

		> .button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 88px;
			background: #FF6600;
			font-size: 30px;
			border: 1px solid #FF6600;
			border-radius: 8px;
		}
	}
}

.scrollContent {
	display: flex;
	flex-direction: column;
	gap: 32px;
	padding: 32px;
	width: calc(100% - 64px);
	min-height: 100%;
	background: #F0F0F0;

	> .customer {
		background: #FFFFFF;
		border-radius: 24px;

		> .header {

			> .image {
				width: 144px;
				height: 44px;
			}
		}

		> .content {
			display: flex;
			flex-direction: row;
			gap: 16px;
			padding: 24px;

			> .image {
				width: 88px;
				height: 88px;
				border-radius: 50%;
			}

			> .info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				gap: 4px;

				> .name {
					color: #1A1A1A;
					font-size: 30px;
					font-weight: bold;
				}

				> .phone {
					color: #9DA3B2;
					font-size: 24px;
				}
			}

			> .tel {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 4px;
				padding: 0 12px;
				margin-right: 24px;

				> .text {
					color: #1A1A1A;
					font-size: 24px;
				}
			}
		}
	}

	> .detail {
		display: flex;
		flex-direction: column;
		gap: 16px;
		padding: 32px 24px;
		background: #FFFFFF;
		border-radius: 24px;

		> .header {
			color: #9DA3B2;
			font-size: 28px;
		}

		.text {
			padding: 12px 0;
			color: #1A1A1A;
			font-size: 28px;
		}

		.title {
			color: #9DA3B2;
			white-space: nowrap;
		}

		> .location {
			display: flex;
			flex-direction: row;
			gap: 8px;
			position: relative;
			padding: 24px;
			height: calc(128px - 48px);

			> .background {
				position: absolute;
				width: 100%;
				height: 100%;
				left: 0;
				top: 0;
				z-index: 0;
			}

			> .icon {
				margin: 6px;
				width: 28px;
				height: 28px;
				z-index: 1;
			}

			> .info {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 12px;
				z-index: 1;

				> .address {
					color: #1A1A1A;
					font-size: 28px;
					font-weight: bold;
				}

				> .distance {
					color: #6B7080;
					font-size: 24px;
				}
			}

			> .nav {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 4px;
				padding: 0 12px;
				z-index: 1;

				> .icon {
					width: 40px;
					height: 40px;
				}

				> .text1 {
					color: #1A1A1A;
					font-size: 24px;
				}
			}
		}
	}

	> .list {
		background: #FFFFFF;
		border-radius: 24px;

		> .each {
			border-bottom: 1px solid #F4F6FA;
		}

		> .each:last-child {
			border-bottom: 0;
		}
	}

	> .situation {
		display: flex;
		flex-direction: column;
		gap: 16px;
		padding: 32px 24px;
		background: #FFFFFF;
		border-radius: 24px;

		> .title {
			color: #1A1A1A;
			font-size: 28px;
			font-weight: bold;
		}

		> .highlight {
			color: #F55858;
		}

		> .images {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			gap: 16px;

			> .each {
				width: 144px;
				height: 144px;
				border-radius: 8px;
			}
		}
	}

	> .comment {
		display: flex;
		flex-direction: column;
		gap: 24px;
		padding: 32px 24px;
		background: #FFFFFF;
		border-radius: 24px;

		> .header {
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: 16px;

			> .logo {
				width: 68px;
				height: 68px;
				border-radius: 50%;
			}

			> .center {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 4px;

				> .name {
					color: #131D34;
					font-size: 28px;
					font-weight: bold;
				}

				> .rate {
					color: #6B7080;
					font-size: 18px;
				}
			}

			> .date {
				color: #9DA3B2;
				font-size: 24px;
			}
		}

		> .content {
			color: #6B7080;
			font-size: 28px;
		}

		> .images {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			gap: 16px;

			> .each {
				width: 200px;
				height: 200px;
				border-radius: 8px;
			}
		}
	}
}
