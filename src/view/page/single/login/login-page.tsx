import { View, Input, Button, Label, Image, RichText, ITouchEvent } from "@tarojs/components";
import Taro from "@tarojs/taro";
import styles from "./login-page.module.scss"
import useWindowArea from "@/hook/windowArea";
import React, { useState } from "react";
import { isDev } from "@/constant/env";

interface IUserAccount {
  username: string,
  password: string,
  agreeProtocol: boolean
}

const LoginPage: React.FC = () => {
  const { bottomArea } = useWindowArea()

  const [userAccount, setUserAccount] = useState<IUserAccount>({
    username: isDev ? "admin" : "",
    password: isDev ? "admin123" : "",
    agreeProtocol: isDev ? true : false
  });

  const [agreeProtocol, setAgreeProtocol] = useState(isDev ? true : false);

  useMount(() => {
    const existInfo = Taro.getStorageSync("loginInfo");
    if (existInfo) {
      // 若之前有登录过，则默认勾选同意用户协议
      setAgreeProtocol(true);
      Taro.removeStorageSync("loginInfo");
    }
  });

  // 更新账号state信息
  const updateAccount = (params: Record<string, string>) => {
    setUserAccount(state => ({
      ...state,
      ...params
    }))
  }

  // 重设密码
  const toResetPwd = () => {
    Taro.navigateTo({ url: "/view/page/single/password-recovery/password-recovery-page" });
  }

  // 查看用户协议
  const toViewProtocol = (e: ITouchEvent) => {
    e.stopPropagation();
    //todo 打开查看用户协议
  }

  // 查看隐私政策
  const toViewPolicy = (e: ITouchEvent) => {
    e.stopPropagation();
    //todo 打开查看用户协议
  }

  /**
   * 账号密码登录
   * @returns 
   */
  const login = async () => {
    if (!userAccount.username.trim()) {
      Taro.showToast({
        title: '请输入用户名/手机号',
        icon: 'none'
      })
      return
    }

    if (!userAccount.password.trim()) {
      Taro.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }

    if (!agreeProtocol) {
      Taro.showToast({
        title: '请认真阅读并勾选同意《用户协议》和《隐私政策》',
        icon: 'none'
      })
      return
    }

    Taro.showLoading({
      title: "登录中"
    });
    try {
      const res = await service.auth.tokenController
        .login(userAccount);
      setLoginInfo(res?.access_token);
    } catch (error) {

    } finally {
      Taro.hideLoading();
    }
  }

  /**
   * 通过手机号登录
   * @param e 微信手机号登录回调事件
   */
  const phoneLogin = (e) => {
    if (e && e.detail && e.detail.errMsg == "getPhoneNumber:ok") {
      if (!agreeProtocol) {
        Taro.showToast({
          title: '请认真阅读并勾选同意《用户协议》和《隐私政策》',
          icon: 'none'
        })
        return
      }
      businessWxLogin(e.detail);
    } else {
      Taro.showToast({
        title: '用户拒绝授权',
        icon: 'none',
      });
    }

  }

  /**
   * 用户授权后走微信登录流程
   * @param weiXinLoginParam 微信账号参数
   */
  const businessWxLogin = (weiXinLoginParam) => {
    Taro.login({
      success: async (res) => {
        Taro.showLoading({
          title: "登录中"
        });
        try {
          weiXinLoginParam.code = res.code;
          const loginRes = await service.auth.tokenController.businessLoginWx(weiXinLoginParam);
          // 登录成功
          setLoginInfo(loginRes?.access_token);
        } catch (error) {

        }
        Taro.hideLoading()
      },
      fail: (err) => {
        console.log(err);
      },
    })
  }

  // 设置用户登录态缓存
  const setLoginInfo = async (token) => {
    let loginInfo: any = {};
    loginInfo.token = token;
    Taro.setStorageSync("loginInfo", loginInfo);
    try {
      const res = await service.business.businessController.getIdsUserId()
      if (res && res.length) {
        loginInfo.businessId = res[0];
        Taro.setStorageSync("loginInfo", loginInfo);

        const pages = Taro.getCurrentPages();
        if (pages.length === 1) {
          Taro.reLaunch({ url: "/view/page/tab/home/<USER>" })
        } else {
          Taro.navigateBack()
        }
      } else {
        // 没有店铺信息，转向到店铺注册页面
        Taro.reLaunch({
          url: "/shop/register-business/register-business-page"
        })
      }
      // .then((res) => {
      // 	if (res && res.length > 0) {
      // 		// TODO 先默认取第一个
      // 		loginInfo.businessId = res[0];
      // 		Taro.setStorageSync("loginInfo", loginInfo);
      // 	}
      // })
    } catch (error) {

    }

  }

  return (
    <View className={styles.loginPage} style={{ paddingBottom: bottomArea }}>
      <View className={styles.topLayer}>
        <View className={styles.welcome}>
          <RichText nodes="您好，<br/>欢迎登录"></RichText>
        </View>
        <Image
          className={styles.main_img}
          src={require("@/assets/image/login/main.png")}
          mode="widthFix"
        />
      </View>
      <View className={styles.form}>
        <View className={styles.formItem}>
          <View className={styles.label}>
            用户名/手机号
          </View>
          <View className={styles.control}>
            <Input value={userAccount.username} placeholder='请输入您的手机号' cursorSpacing={20} onInput={e => updateAccount({ username: e.detail.value })} />
          </View>
        </View>
        <View className={styles.formItem}>
          <View className={styles.label}>
            密码
          </View>
          <View className={styles.control}>
            <Input password value={userAccount.password} placeholder='请输入您的密码' cursorSpacing={20} onInput={e => updateAccount({ password: e.detail.value })} />
          </View>
        </View>
      </View>
      <View className={styles.extra}>
        <View className={styles.operation}>
          {/* <Image className={styles.icon} src={require("@/assets/image/login/shiyongjilu.png")} mode="aspectFit" />
					申请试用 */}
        </View>
        <View className={styles.operation} onClick={toResetPwd}>
          <Image className={styles.icon} src={require("@/assets/image/login/mima.png")} mode="aspectFit" />
          验证码登录
        </View>
      </View>
      <View className={styles.loginBtns}>
        <Button className={styles.login} hoverClass={styles.active} onClick={login}>登录</Button>
        <View>OR</View>
        <Button className={styles.phoneLogin} hoverClass={styles.active} openType="getPhoneNumber"
          onGetPhoneNumber={phoneLogin}>手机号一键登录</Button>
      </View>
      <View className={styles.holder}></View>
      <View className={styles.protocol} onClick={() => setAgreeProtocol(state => !state)}>
        {/* <AtCheckbox
					className={styles.protocolCheckbox}
					options={[{
						value: 'agreed',
						label: ''
					}]}
					selectedList={[]}
					onChange={() => {}}
				/> */}

        <View className={styles.checkbox}>
          {agreeProtocol && <Image className={styles.agree_icon} src={require("@/assets/image/login/check-fill.svg")} mode="widthFix"></Image>}
        </View>

        <Label>
          阅读并同意<Label className={styles.highlight} onClick={toViewProtocol}>用户协议</Label>和<Label className={styles.highlight} onClick={toViewPolicy}>隐私政策</Label>并理解相关条款
        </Label>
      </View>
    </View>
  );
};

export default LoginPage;
