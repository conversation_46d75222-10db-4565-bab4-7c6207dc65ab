$primary-color: #FDD244;
$padding: 20px;
$gap: 12px;
$font-size: 16px;

.loginPage {
    width: 100%;
    height: 100%;
    background-image: url("../../../../assets/image/login/bg.png");
    background-size: 100% 100%;
    // background: linear-gradient(180deg, rgba(253, 210, 68, 0.26) 0%, rgba(255, 248, 225, 0) 100%);
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: $gap;

    .topLayer {
        height: 265px;
        position: relative;

        .welcome {
            position: absolute;
            bottom: 45px;
            left: 24px;
            font-size: 28px;
            font-weight: bold;
        }

        .main_img {
            position: absolute;
            bottom: 11px;
            right: 18px;
            width: 167px;
        }
    }

    .form {
        padding: 0 20px;

        .formItem {
            padding-top: $padding;
            border-bottom: 1px solid #EBEDF0;
            font-size: $font-size;

            .label {
                line-height: $font-size + 4;
                color: #1f1f1f;
                font-weight: bold;
            }

            .control {
                padding: $padding / 2 0;
            }
        }
    }

    .extra {
        padding: $padding / 2 $padding;
        display: flex;
        justify-content: space-between;
        color: $primary-color;
        font-size: $font-size - 2;

        .operation {
            display: flex;
            align-items: center;

            .icon {
                width: 18px;
                height: 18px;
                margin-right: 5px;
            }
        }
    }

    .loginBtns {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        color: #999999;
        margin-top: $font-size;

        .login {
            width: 80%;
            background-color: $primary-color;
            color: #FFFFFF;
            border-radius: 999px;
            border: 0;
            font-size: $font-size;

            &::after {
                border: none;
            }

            &.active {
                background-color: #fdd244e0;
            }
        }

        .phoneLogin {
            width: 80%;
            background-color: rgba(253, 210, 68, 0.1);
            color: $primary-color;
            border-radius: 999px;
            border: 0;
            font-size: $font-size;

            &::after {
                border: none;
            }

            &.active {
                background-color: rgba(253, 210, 68, 0.05);
            }
        }
    }

    .holder {
        flex: 1 0;
    }

    .protocol {
        font-size: $font-size - 2;
        color: #999999;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2px;

        .checkbox {
            width: 18px;
            height: 18px;
            border-radius: 999px;
            border: 1px solid #FDD244;
            margin-right: 5px;
            display: flex;
            justify-content: center;
            align-items: center;

            .agree_icon {
                width: 100%;
                height: 100%;
            }
        }

        .highlight {
            color: #333333;
            font-weight: bold;
        }
    }


}