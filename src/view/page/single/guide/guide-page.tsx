import { View } from "@tarojs/components"
import Taro from "@tarojs/taro";
import { useEffect } from "react";

const Index: React.FC = () => {

    useEffect(() => {
        // 判断是否有登录状态
        const loginInfo = Taro.getStorageSync("loginInfo");
        if (!loginInfo) {
            Taro.reLaunch({ url: "/view/page/single/login/login-page" })
        } else {
            Taro.reLaunch({ url: "/view/page/tab/home/<USER>" })
        }
    }, [])

    return (
        // todo: 增加logo或广告组件
        <View></View>
    )
}

export default Index;