$primary-color: #FDD244;
$padding: 20px;
$gap: 12px;
$font-size: 16px;

.loginPage {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(253, 210, 68, 0.26) 0%, rgba(255, 248, 225, 0) 100%);
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: $gap;

    .navBar {
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .backButton {
            position: absolute;
            left: 12px;
            font-size: 16px;
        }

        .title {
            color: #1f1f1f;
            font-weight: 400;
            font-size: 16px;
            text-align: center;
        }
    }

    .topLayer {
        height: 120px;
        position: relative;

        .page_title {
            position: absolute;
            bottom: 20px;
            left: 24px;
            font-size: 28px;
            font-weight: bold;
        }
    }

    .form {
        padding: 0 20px;

        .formItem {
            padding-top: $padding;
            border-bottom: 1px solid #EBEDF0;
            font-size: $font-size;

            .label {
                line-height: $font-size + 4;
                color: #1f1f1f;
                font-weight: bold;
            }

            .control {
                padding: $padding / 2 0;

                &.verify {
                    display: flex;
                    align-items: center;

                    .input {
                        flex: 1 0;
                    }

                    .sendCode {
                        padding: 4px 12px;
                        border-radius: 999px;
                        background-color: transparent;
                        font-size: 14px;
                        line-height: 1;

                        &::after {
                            border: none;
                        }

                        &.active {
                            background-color: rgba(253, 210, 68, 0.297);
                        }
                    }

                    .resendTip {
                        padding: 4px 12px;
                        font-size: 14px;
                        color: $primary-color;
                        line-height: 1;
                    }
                }
            }
        }
    }

    .btns {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        color: #999999;
        margin-top: 100px;

        .next {
            width: 80%;
            background-color: $primary-color;
            color: #FFFFFF;
            border-radius: 999px;
            border: 0;
            font-size: $font-size;

            &::after {
                border: none;
            }

            &.active {
                background-color: #fdd244e0;
            }

            &.disabled {
                background-color: #E0E0E0;
                color: #ffffff;
            }
        }
    }
}