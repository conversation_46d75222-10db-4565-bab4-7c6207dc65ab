import { View, Input, Button, Label, RichText } from "@tarojs/components";
import Taro from "@tarojs/taro";
import styles from "./password-recovery-page.module.scss"
import useWindowArea from "@/hook/windowArea";
import React, { useMemo, useRef, useState } from "react";
import { validatePhone } from "@/util/validate-util";
import { AtIcon } from "taro-ui";

const COUNTDOWN = 60;

const PasswordRecoveryPage: React.FC = () => {
	const { bottomArea, topArea, navArea } = useWindowArea()

	const [recoveryForm, setRecoveryForm] = useState({
		username: "",
		verifyCode: ""
	});

	const [showSend, setShowSend] = useState(true);
	const [countdown, setCountdown] = useState(COUNTDOWN);

	const countdownRef = useRef(COUNTDOWN)
	const timer = useRef<NodeJS.Timeout>();

	// 更新找回表单state信息
	const updateForm = (params: Record<string, string>) => {
		setRecoveryForm(state => ({
			...state,
			...params
		}))
	}

	// const phoneNumber = useMemo(() => {
	// 	console.log(recoveryForm.username.replace(/\s/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3'))
	// 	return recoveryForm.username.replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3')
	// }, [recoveryForm.username])

	// const onPhoneNumberChange = (val: string) => {
	// 	updateForm({ username: val.replace(/[^\d]/g, "") })
	// }

	const sendVerifyCode = async () => {
		if (!recoveryForm.username.trim()) {
			Taro.showToast({
				title: '请输入用户名/手机号',
				icon: 'none'
			})
			return
		}

		if (!validatePhone(recoveryForm.username)) {
			Taro.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			});
			return;
		}

		try {
			service.auth.tokenController.postAuthSendSmsCode({
				phonenumber: recoveryForm.username
			}).then(res => {
				setShowSend(false);
				Taro.showToast({
					title: '获取成功，请查收短信验证码',
					icon: 'none'
				})
				timer.current = setInterval(() => {
					setCountdown(--countdownRef.current);
					if (countdownRef.current === 0) {
						setShowSend(true)
						clearInterval(timer.current);
						countdownRef.current = COUNTDOWN;
					}
				}, 1000)
			})
		} catch (error) {
			Taro.showToast({
				title: '获取验证码失败',
				icon: 'none'
			})
		}
	}

	/**
	 * 账号密码登录
	 * @returns 
	 */
	const nextStep = async () => {
		if (!recoveryForm.username.trim()) {
			Taro.showToast({
				title: '请输入用户名/手机号',
				icon: 'none'
			})
			return
		}

		if (!validatePhone(recoveryForm.username)) {
			Taro.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			});
			return;
		}

		if (!recoveryForm.verifyCode.trim()) {
			Taro.showToast({
				title: '请输入验证码',
				icon: 'none'
			})
			return
		}

		try {
			// todo 调用验证接口

		} catch (error) {

		}
	}

	/**
	 * 验证码登录
	 */
	const loginWithVerityCode = async () => {
		if (!recoveryForm.username.trim()) {
			Taro.showToast({
				title: '请输入用户名/手机号',
				icon: 'none'
			})
			return
		}

		if (!validatePhone(recoveryForm.username)) {
			Taro.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			});
			return;
		}

		if (!recoveryForm.verifyCode.trim()) {
			Taro.showToast({
				title: '请输入验证码',
				icon: 'none'
			})
			return
		}

		try {
			const res = await service.auth.tokenController.postLoginSmsBusiness({
				phonenumber: recoveryForm.username,
				smscode: recoveryForm.verifyCode
			})
			setLoginInfo(res?.access_token);

			Taro.navigateTo({ url: "/view/page/tab/home/<USER>" });

		} catch (error) {
			Taro.showToast({
				title: '登录失败',
				icon: 'error'
			})
		}
	}

	const setLoginInfo = (token) => {
		let loginInfo: any = {};
		loginInfo.token = token;
		Taro.setStorageSync("loginInfo", loginInfo);
		service.business.businessController.getIdsUserId().then((res) => {
			if (res && res.length > 0) {
				// TODO 先默认取第一个
				loginInfo.businessId = res[0];
				Taro.setStorageSync("loginInfo", loginInfo);
			}
		})
	}

	return (
		<View className={styles.loginPage} style={{ paddingBottom: bottomArea }}>
			<View className={styles.navBar} style={{ height: navArea + topArea, paddingTop: topArea }}>
				<View className={styles.backButton} onClick={() => Taro.navigateBack()}>
					<AtIcon value="chevron-left" size="13" color="#000000" ></AtIcon>
				</View>
			</View>
			<View className={styles.topLayer}>
				<View className={styles.page_title}>
					<RichText nodes="验证码登录"></RichText>
				</View>
			</View>
			<View className={styles.form}>
				<View className={styles.formItem}>
					<View className={styles.label}>
						用户名/手机号
					</View>
					<View className={styles.control}>
						<Input type='number' value={recoveryForm.username} placeholder='请输入您的手机号' cursorSpacing={20} onInput={e => updateForm({ username: e.detail.value })} />
					</View>
				</View>
				<View className={styles.formItem}>
					<View className={styles.label}>
						验证码
					</View>
					<View className={`${styles.control} ${styles.verify}`}>
						<Input className={styles.input} type='number' value={recoveryForm.verifyCode} placeholder='请输入验证码' cursorSpacing={20} onInput={e => updateForm({ verifyCode: e.detail.value })} />
						{
							showSend ? <Button className={styles.sendCode} hoverClass={styles.active} onClick={sendVerifyCode}>获取验证码</Button>
								:
								<Label className={styles.resendTip}>{countdown}秒后重新发送</Label>
						}
					</View>
				</View>
			</View>
			<View className={styles.btns}>
				<Button className={`${styles.next} ${recoveryForm.verifyCode ? '' : styles.disabled}`} hoverClass={styles.active} onClick={loginWithVerityCode}>登录</Button>
			</View>
		</View>
	);
};

export default PasswordRecoveryPage;