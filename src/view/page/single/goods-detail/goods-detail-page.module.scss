.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #F0F0F0;

	> .top {
		flex: 1;
		overflow: hidden;
	}

	> .bottom {
		gap: 32px;
		padding: 16px 32px calc(env(safe-area-inset-bottom) + 16px) 32px;
		background: #FFFFFF;

		> .button {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88px;
			background: #FF6600;
			border-radius: 44px;
			font-size: 30px;
		}
	}
}

.scrollContent {

	> .detailImages {
		position: relative;
		width: 100%;
		height: 0;
		padding-bottom: 60%;

		> .swiper {
			position: absolute;
			width: 100%;
			height: 100%;

			.swiperImage {
				width: 100%;
				height: 100%;
			}
		}
	}

	> .info {
		display: flex;
		flex-direction: column;
		gap: 16px;
		padding: 48px 32px;
		background: #FFFFFF;

		> .title {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;

			> .name {
				color: #1A1A1A;
				font-size: 36px;
				font-weight: bold;
			}

			> .points {
				color: #FF6600;
				font-size: 36px;
			}
		}

		> .content {
			color: #6B7080;
			font-size: 28px;
		}
	}

	> .richText {
		width: 100%;
	}
}
