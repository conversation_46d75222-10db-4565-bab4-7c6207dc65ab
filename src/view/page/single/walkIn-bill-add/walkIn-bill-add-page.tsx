import BasicPage from "@/view/component/basic-page/basic-page.components"
import styles from "./walkIn-bill-add-page.module.scss"
import { Input, Label, Picker, ScrollView, Textarea, View, Image, ITouchEvent } from "@tarojs/components";
import { AtButton, AtFloatLayout, AtIcon } from "taro-ui";
import { useEffect, useMemo, useRef, useState } from "react";
import { getEmployeeList } from "@/service/business/businessEmployeeController";
import { postShopProjectAll } from "@/service/business/shangjiaxiangmuguanli";
import dayjs from "dayjs";
import Taro from "@tarojs/taro";
import { postMemberRecordWalkAddNew } from "@/service/business/huiyuanxiangqingxiaofeimingxi";
import { getMemberDetaillist } from "@/service/business/huiyuanguanli";
import checkedImg from '@/assets/image/checked.png';
import DatetimePicker from "@/view/component/common/DatetimePicker";


interface Project {
  projectId: number,
  projectName: string
}

interface Member {
  memberId: number,
  nickName: string
}

interface Employee {
  userId: number,
  employeeNickname: string
}

const Amount_Options = [
  {
    value: 50
  }, {
    value: 100
  }, {
    value: 200
  }, {
    value: 500
  }
]

const Index: React.FC = () => {
  // 商家项目列表
  const [projectList, setProjectList] = useState<Project[]>([]);
  // 商家项目列表
  const [memberList, setMemberList] = useState<Member[]>([]);
  // 商家员工列表
  const [employeeList, setEmployeeList] = useState<Employee[]>([]);

  const [billForm, setBillForm] = useState({
    projectIdx: 0,
    projectId: undefined,
    projectName: "",
    employeeIdx: 0,
    employeeId: undefined,
    employeeNickname: "",
    memberIdx: 0,
    memberId: undefined,
    memberName: "",
    amount: 0,
    datetime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    projectList: [] as number[],
    memo: ""
  })

  // 项目弹框
  const [showProjectPopup, setShowProjectPopup] = useState(false);
  const [tempProjectList, setTempProjectList] = useState<number[]>([])

  const [selectAmount, setSelectAmount] = useState<string | number>()
  // 自定义金额
  const [customAmount, setCustomAmount] = useState<number>();
  // 自定义金额编辑弹框
  const [showCustomAmountPopup, setShowCustomAmountPopup] = useState(false);
  const [tempCustomAmount, setTempCustomAmount] = useState<string>();

  const selectedProject = useMemo(() => {
    return projectList.filter(item => billForm.projectList.includes(item.projectId));
  }, [billForm.projectList, projectList])


  /**
   * 更新账单状态
   * @param param 
   */
  const onBillFormChange = (param: Record<string, any>) => {
    setBillForm(state => ({
      ...state,
      ...param
    }))
  }

  /**
   * 打开项目弹框回调
   */
  const onShowProjectPopup = () => {
    setTempProjectList([...billForm.projectList]);
    setShowProjectPopup(true)
  }

  /**
   * 消费项目改变回调
   * @param e 
   */
  const onProjectChange = (id: number) => {
    let exist = [...tempProjectList];
    if (exist.includes(id)) {
      exist = exist.filter(item => item != id)
    } else {
      exist.push(id)
    }
    setTempProjectList(exist)
  }

  const onConfirmProject = (e: ITouchEvent) => {
    e.stopPropagation();
    onBillFormChange({
      projectList: tempProjectList
    })
    setShowProjectPopup(false)
  }

  /**
   * 客户改变回调
   * @param e 
   */
  const onMemberChange = (e) => {
    const idx = e.detail.value;
    const member = memberList[idx];
    if (member) {
      onBillFormChange({
        memberIdx: idx,
        memberId: member.memberId,
        memberName: member.nickName
      })
    }
  }

  /**
   * 员工改变回调
   * @param e 
   */
  const onEmployeeChange = (e) => {
    const idx = e.detail.value;
    const employee = employeeList[idx];
    if (employee) {
      onBillFormChange({
        employeeIdx: idx,
        employeeId: employee.userId,
        employeeNickname: employee.employeeNickname
      })
    }
  }

  /**
   * 消费金额改变回调
   * @param index 
   */
  const onAmountChange = (index: number | string) => {
    if (index == "custom") {

    } else {
      const amount = Amount_Options[index].value;
      onBillFormChange({
        amount
      });
    }

    setSelectAmount(index)
  }

  /**
   * 自定义金额改变回调
   * @returns 
   */
  const onCustomAmountChange = (e: ITouchEvent) => {
    e.stopPropagation();

    if (!tempCustomAmount?.trim()) {
      setShowCustomAmountPopup(false)
      return
    }
    if (isNaN(Number(tempCustomAmount.trim()))) {
      Taro.showToast({
        title: "金额非法",
        icon: "none"
      })
      setShowCustomAmountPopup(false)
      return
    }
    setCustomAmount(Number(tempCustomAmount));
    onBillFormChange({
      amount: Number(tempCustomAmount)
    })
    setSelectAmount("custom")
    setShowCustomAmountPopup(false)
  }

  /**
   * 提交账单
   * @returns 
   */
  const submit = async () => {
    if (billForm.projectList.length === 0) {
      Taro.showToast({
        title: "请选择项目服务",
        icon: "none"
      })
      return
    }
    if (!billForm.employeeId) {
      Taro.showToast({
        title: "请选择员工",
        icon: "none"
      })
      return
    }
    if (!billForm.amount) {
      Taro.showToast({
        title: "请选择消费金额",
        icon: "none"
      })
      return
    }
    if (!billForm.datetime) {
      Taro.showToast({
        title: "请选择消费时间",
        icon: "none"
      })
      return
    }
    Taro.showLoading();
    try {
      const params = {
        memberId: billForm.memberId || undefined,
        employeeId: billForm.employeeId!,
        receivedAmount: billForm.amount,
        operRecordBPLists: selectedProject.map(item => ({
          projectId: item.projectId,
          categoryId: item.categoryId,
          count: 1
        })),
        remark: billForm.memo
      }

      await postMemberRecordWalkAddNew(params);
      Taro.hideLoading();
      Taro.navigateBack();
      Taro.showToast({
        title: "开单成功",
        icon: "success"
      })
    } catch (error) {
      Taro.hideLoading();
    }

  }

  useEffect(() => {
    // 初始化获取商家项目列表和商家员工列表
    try {
      getEmployeeList({
        pageQueryModel: 0
      } as any).then(res => {
        if (res) {
          setEmployeeList(res.data)
        }
      })
      const loginInfo = Taro.getStorageSync('loginInfo');
      postShopProjectAll({
        businessId: loginInfo.businessId,
        status: 1
      }).then(res => {
        if (res) {
          setProjectList(res as Project[])
        }
      })

      getMemberDetaillist({
        detailVo: {
          // memberType: "0", // 0对应的是散客 1对应的是会员 不传就是所有
          businessId: loginInfo.businessId
        } as any,
        pageQueryModel: 0
      }).then(res => {
        if (res) {
          setMemberList(res.data)
        }
      })
    } catch (error) {

    }
  }, [])

  return (
    <BasicPage
      title="散客消费"
      bottomSafe
    >
      <View className={styles.pageWrapper} catchMove>
        <ScrollView className={styles.formWrapper} scrollY>
          <View className={styles.formBox}>
            {/* <Picker value={billForm.projectIdx} range={projectList} rangeKey="projectName" onChange={onProjectChange}> */}
            <View className={styles.formItem} onClick={onShowProjectPopup}>
              <View className={styles.label}>项目服务</View>
              <View className={styles.control}>
                <View className={styles.content}>
                  {
                    billForm.projectList.length != 0 && (
                      <ScrollView className={styles.xScroll} scrollX>
                        <View className={styles.selectedProjectList}>
                          {
                            selectedProject.map(item => (
                              <View className={styles.projectName}>{item.projectName}</View>
                            ))
                          }
                        </View>
                      </ScrollView>
                    )
                  }
                  {
                    billForm.projectList.length == 0 && (
                      <Label className={styles.placeholder}>选择项目</Label>
                    )
                  }
                </View>
              </View>
              <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
            </View>
            {/* </Picker> */}
            <Picker value={billForm.memberIdx} range={memberList} rangeKey="nickName" onChange={onMemberChange}>
              <View className={styles.formItem}>
                <View className={styles.label}>客户</View>
                <View className={styles.control}>
                  <View className={styles.content}>
                    {
                      billForm.memberName ? <>{billForm.memberName}</> : <Label className={styles.placeholder}>选择客户，非必选</Label>
                    }
                  </View>
                  <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
                </View>
              </View>
            </Picker>
          </View>
          <View className={styles.formBox}>
            <View className={styles.boxTitle}>消费金额</View>
            <view className={styles.amountGrid}>
              {
                Amount_Options.map((item, index) => (
                  <View className={`${styles.amountOption} ${index === selectAmount ? styles.active : ''}`} onClick={() => onAmountChange(index)}>
                    <View className={styles.amount}>
                      {item.value}
                      <Label className={styles.unit}>元</Label>
                    </View>
                  </View>
                ))
              }
              <View className={`${styles.amountOption} ${"custom" === selectAmount ? styles.active : ''}`} onClick={() => setShowCustomAmountPopup(true)}>
                <View className={styles.amount}>
                  {customAmount ? <>{customAmount}<Label className={styles.unit}>元</Label></> : <>自定义</>}
                </View>
              </View>
            </view>
          </View>
          <View className={styles.formBox}>
            <Picker value={billForm.employeeIdx} range={employeeList} rangeKey="employeeNickname" onChange={onEmployeeChange}>
              <View className={styles.formItem}>
                <View className={styles.label}>员工</View>
                <View className={styles.control}>
                  <View className={styles.content}>
                    {
                      billForm.employeeNickname ? <>{billForm.employeeNickname}</> : <Label className={styles.placeholder}>选择员工</Label>
                    }
                  </View>
                  <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
                </View>
              </View>
            </Picker>
            <DatetimePicker data={billForm.datetime} onChange={val => onBillFormChange({ datetime: val })}>
              <View className={styles.formItem}>
                <View className={styles.label}>消费时间</View>
                <View className={styles.control}>
                  <View className={styles.content}>
                    {
                      billForm.datetime ? <>{billForm.datetime}</> : <Label className={styles.placeholder}>选择消费时间</Label>
                    }
                  </View>
                  <AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
                </View>
              </View>
            </DatetimePicker>
            <View className={`${styles.formItem} ${styles.textarea}`}>
              <View className={styles.label}>备注</View>
              <View className={styles.control}>
                <Textarea value={billForm.memo} maxlength={200} placeholder="请输入备注，类似客户信息" autoHeight style={{ width: "100%", minHeight: 130 }} onInput={e => onBillFormChange({ memo: e.detail.value })}></Textarea>
              </View>
              <View className={styles.maxLength}>
                {billForm.memo.length || 0}/200
              </View>
            </View>
          </View>
        </ScrollView>
        <View className={styles.submitBox}>
          <AtButton className={styles.submitBtn} type="primary" circle onClick={submit}>
            保存
          </AtButton>
        </View>
        <AtFloatLayout isOpened={showProjectPopup} onClose={() => setShowProjectPopup(false)}>
          <View className={`${styles.popup} ${styles.projectPopup}`}>
            <View className={styles.header}>
              <View className={styles.cancel} onClick={() => setShowProjectPopup(false)}>取消</View>
              <View className={styles.title}>选择项目</View>
              <View className={styles.confirm} onClick={e => onConfirmProject(e)}>确定</View>
            </View>
            <View className={styles.wrapper}>
              {
                projectList.length ? (
                  <ScrollView className={styles.projectList} scrollY>
                    {
                      projectList.map(project => (
                        <View
                          className={`${styles.project} ${tempProjectList.includes(project.projectId) ? styles.selected : ''}`}
                          key={project.projectId}
                          onClick={() => onProjectChange(project.projectId)}
                        >
                          <Label className={styles.name}>{project.projectName}</Label>
                          <Image className={`w-5 h-5 ${styles.checked}`} src={checkedImg} mode="aspectFit" />
                        </View>
                      ))
                    }
                  </ScrollView>
                ) : (
                  <View className={styles.project_empty}>无服务项目</View>
                )
              }

            </View>
          </View>
        </AtFloatLayout>
        <AtFloatLayout isOpened={showCustomAmountPopup} onClose={() => setShowCustomAmountPopup(false)}>
          <View className={`${styles.popup} ${styles.customAmountPopup}`}>
            <View className={styles.header}>
              <View className={styles.cancel} onClick={() => setShowCustomAmountPopup(false)}>取消</View>
              <View className={styles.title}>自定义金额</View>
              <View className={styles.confirm} onClick={e => onCustomAmountChange(e)}>确定</View>
            </View>
            <View className={styles.wrapper}>
              <View className={styles.customAmountInputRow}>
                ¥
                <Input className={styles.input} value={tempCustomAmount} onInput={e => setTempCustomAmount(e.detail.value)}></Input>
                <AtIcon className={styles.linkIcon} size='10' value='close-circle' onClick={() => setTempCustomAmount("")}></AtIcon>
              </View>
            </View>
          </View>
        </AtFloatLayout>
      </View>
    </BasicPage>
  )
}

export default Index;
