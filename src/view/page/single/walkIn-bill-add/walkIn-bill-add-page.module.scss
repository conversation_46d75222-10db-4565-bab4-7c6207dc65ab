.pageWrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .formWrapper {
        flex: 1 0;
        background-color: #F6F6F6;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 10px;
        overflow: hidden;

        .formBox {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 0 10px;

            .boxTitle {
                padding: 12px 0;
                font-size: 14px;
                font-weight: 500;
            }

            .formItem {
                height: 50px;
                width: 100%;
                overflow: hidden;
                display: flex;
                align-items: center;
                border-bottom: 1px solid rgba(235, 237, 240, 0.46);

                &.textarea {
                    height: unset;
                    flex-direction: column;
                    align-items: normal;

                    .label {
                        padding: 12px 0;
                    }

                    .control {
                        flex: none;
                        height: unset;
                        width: 100%;
                    }

                    .maxLength {
                        font-size: 12px;
                        color: #999999;
                        text-align: right;
                        padding: 8px 0;
                    }
                }

                .label {
                    width: 90px;
                    font-size: 15px;
                    flex-shrink: 0;
                }

                .control {
                    flex: 1 0;
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    color: #333333;
                    font-size: 14px;
                    overflow: hidden;

                    .content {
                        width: 100%;
                        white-space: nowrap;

                        .placeholder {
                            color: #949494;
                        }
                    }
                }

                .linkIcon {
                    flex-shrink: 0;
                    color: #969799;
                }

                .xScroll {
                    width: 100%;
                    overflow: hidden;

                    .selectedProjectList {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }

                    .projectName {
                        padding: 2px 6px;
                        border-radius: 3px;
                        color: #FDD244;
                        background-color: rgba(253, 210, 68, 0.1);
                        font-size: 12px;
                    }
                }

                &:last-child {
                    border-bottom: 0;
                }
            }

            .amountGrid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 10px;
                padding-bottom: 20px;

                .amountOption {
                    height: 66px;
                    background-color: #f7f7f7;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                    font-size: 20px;
                    border: 1px solid transparent;

                    &.active {
                        border-color: #FDD244;
                        background-color: rgba(253, 210, 68, 0.1);
                        color: #FDD244;
                    }

                    .unit {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .submitBox {
        width: 100%;
        height: 50px;
        background: #fff;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .submitBtn {
            width: calc(100% - 32px);
        }
    }


}

.popup {
    .header {
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        font-size: 15px;
        color: #333333;

        .confirm {
            color: #FDD244;
        }
    }
}

.projectPopup {
    .projectList {
        height: 260px;
        padding: 0 0 30px;
        box-sizing: border-box;

        .project {
            height: 46px;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .checked {
                opacity: 0;
            }

            &.selected {
                color: #FDD244;

                .checked {
                    opacity: 1;
                }
            }
        }
    }

    .project_empty {
        height: 260px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #666666;
    }
}

.customAmountPopup {
    .wrapper {
        padding: 50px 10px 150px;

        .customAmountInputRow {
            height: 50px;
            font-size: 30px;
            font-weight: 500;
            display: flex;
            align-items: center;
            margin: 0 20px;
            border-top: 1px solid rgba(235, 237, 240, 0.46);
            border-bottom: 1px solid rgba(235, 237, 240, 0.46);

            .input {
                height: 100%;
                flex: 1 0;
                text-align: center;
            }
        }
    }
}

:global {
    .at-float-layout__container {
        min-height: unset;
        margin-right: unset;

        .layout-body {
            min-height: unset;
            margin-right: unset;
            padding: 0;
        }

        .layout-body__content {
            min-height: unset;
            margin-right: unset;
        }
    }
}