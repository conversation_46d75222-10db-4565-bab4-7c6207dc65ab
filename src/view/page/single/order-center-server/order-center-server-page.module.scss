.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #F0F0F0;

	> .header {
		background: #FFFFFF;
	}

	> .tab {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 96px;
		background: #FFFFFF;

		> .tabBar {
			flex: 1;
		}

		> .avatar {
			width: 64px;
			height: 64px;
			margin: 0 32px;
			border-radius: 32px;
		}
	}

	> .content {
		flex: 1;
		overflow: hidden;

		> .scroll {
			height: 100%;
		}
	}
}
