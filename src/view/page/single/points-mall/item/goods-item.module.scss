.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	border: 1px solid #E0E2E5;
	border-radius: 16px;
	overflow: hidden;

	> .imageBox {
		position: relative;
		width: 100%;
		height: 0;
		padding-bottom: 100%;

		> .image {
			position: absolute;
			width: 100%;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			margin: auto;
		}
	}

	> .info {
		display: flex;
		flex-direction: column;
		gap: 16px;
		padding: 16px 24px 24px 24px;

		> .title {
			color: #1A1A1A;
			font-size: 28px;
			font-weight: bold;
		}

		> .bottom {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;

			> .points {
				flex: 1;
				color: #F46B4F;
				font-size: 32px;
			}

			> .button {
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 0;
				padding: 0;
				width: 112px;
				height: 56px;
				background: #FF6600;
				border-radius: 28px;
				font-size: 24px;
			}
		}
	}
}
