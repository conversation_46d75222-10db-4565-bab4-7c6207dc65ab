.container {
	display: flex;
	flex-direction: column;
	padding-bottom: env(safe-area-inset-bottom);
	background: #FFFFFF;

	> .points {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 8px;
		width: 100%;
		height: 240px;
		background: #FBD18E;

		> .value {
			color: #833900;
			font-size: 80px;
			font-family: DIN Pro-Medium, DIN Pro;
			font-weight: bold;
		}

		> .buttons {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			gap: 16px;

			> .each {
				color: #914802;
				font-size: 24px;
			}

			> .divider {
				width: 2px;
				height: 24px;
				background: #914802;
			}
		}
	}

	> .goods {
		display: grid;
		grid-template-columns: repeat(2, calc(50% - 12px));
		grid-gap: 24px;
		padding: 32px;
	}
}
