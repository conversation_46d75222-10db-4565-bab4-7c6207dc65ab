.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	background: #FFFFFF;
	border-radius: 24px;

	> .each {
		display: flex;
		flex-direction: row;
		padding: 32px 32px 32px 18px;
		border-bottom: 1px solid #EFF1F5;

		> .check {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 32px;

			> .image {
				width: 32px;
				height: 32px;
			}

			> .circle {
				width: 30px;
				height: 30px;
				border-radius: 50%;
				border: 2px solid #DCDFE5;
				background: #FFFFFF;
			}
		}

		> .image {
			padding: 0 16px;
			width: 175px;
			height: 175px;
			border-radius: 16px;
			overflow: hidden;
		}

		> .content {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 8px;
			padding: 4px 0;
			overflow: hidden;

			> .title {
				color: #1A1A1A;
				font-size: 30px;
				font-weight: bold;
			}

			> .introduction {
				flex: 1;
				color: #6B7080;
				font-size: 24px;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}

			> .bottom {
				display: flex;
				flex-direction: row;
				align-items: baseline;

				> .price {
					flex: 1;
					display: flex;
					flex-direction: row;
					align-items: baseline;

					> .price1 {
						color: #FF6600;
						display: flex;
						flex-direction: row;
						align-items: baseline;

						> .value {
							font-size: 48px;
							font-family: DIN Pro-Medium, DIN Pro;
							font-weight: bold;
						}

						> .yuan {
							margin-left: 8px;
							font-size: 24px;
						}
					}

					> .price2 {
						margin-left: 16px;
						color: #FFAE04;
						font-size: 24px;
					}
				}

				> .delete {
					color: #9DA3B2;
					font-size: 24px;
				}
			}
		}
	}

	> .each:last-child {
		border-bottom: 0;
	}
}
