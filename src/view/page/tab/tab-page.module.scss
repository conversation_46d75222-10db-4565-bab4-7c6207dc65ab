.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    .wrapper {
        flex: 1 0;
        overflow: hidden;

        .headerText {
            font-size: 34px;
            font-weight: bold;
        }

        >.each {
            height: 100%;
            overflow: hidden;
        }

        >.tabBar {
            padding-bottom: env(safe-area-inset-bottom);
        }
    }
}