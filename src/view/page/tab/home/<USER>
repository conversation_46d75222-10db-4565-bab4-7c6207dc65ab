import Taro from '@tarojs/taro';
import React, { useState, useEffect } from "react";
import { View, Text, Image, Picker } from "@tarojs/components";
import { postReservationRecordAccept, postReservationRecordEditReservationDate } from '@/service/business/reservationRecordController';
import { postCommonMsgSend } from '@/service/business/gonggongguanli'
import dayjs from 'dayjs';
import styles from "./reservation-item.module.scss";
import { GlobalInfoStore } from "@/store/global-info.store";
import useStore from "@/hook/store";

// 日期时间选项配置
const dateTimeRange = [
  // 未来30天的日期选项
  Array.from({ length: 30 }, (_, i) => {
    const date = dayjs().add(i, 'day');
    const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()];
    return i === 0 ? `今天 ${weekDay}` : `${date.format('MM月DD日')} ${weekDay}`;
  }),
  // 24小时选项
  Array.from({ length: 24 }, (_, i) =>
    i < 10 ? `0${i}:00` : `${i}:00`
  ),
  // 每10分钟一个选项
  Array.from({ length: 6 }, (_, i) =>
    i === 0 ? '00' : `${i}0`
  )
];

interface ReservationItemProps {
  item: BUSINESS.ReservationRecordListVo;
  onRefresh?: () => void;
}

const statusMap = {
  0: { text: "待接单", color: "#FFFFFF", bg: "#F43535" },
  1: { text: "已约", color: "#FFFFFF", bg: "#FDD244" },
  2: { text: "已取消", color: "#999", bg: "#F0F0F0" },
  3: { text: "已到店", color: "#FFB300", bg: "#FFE9B0" },
  4: { text: "未到店", color: "#999", bg: "#F0F0F0" },
  5: { text: "已完成", color: "#FFB300", bg: "#FFE9B0" },
  6: { text: "已逾期", color: "#999", bg: "#F0F0F0" },
};

const ReservationItem: React.FC<ReservationItemProps> = ({ item, onRefresh }) => {
  // 修改时间状态
  const [dateTime, setDateTime] = useState<string>('');

  // 在组件挂载时初始化 dateTime
  useEffect(() => {
    if (item) {
      const serviceTime = item.serviceTime || item.reservationDate || '';
      if (serviceTime) {
        setDateTime(serviceTime);
      } else {
        // 如果没有预约时间，使用当前时间
        const now = dayjs();
        setDateTime(now.format('YYYY-MM-DDTHH:mm:ss'));
      }
    }
  }, [item]);

  if (!item) return null;

  const status = statusMap[item.status ?? 0];
  const serviceName = (() => {
    if (item.serviceName) return item.serviceName;
    if (item.projects && item.projects.length > 0) {
      const names = item.projects.map(p => p.projectName);
      if (names.length > 2) {
        return `${names.slice(0, 2).join('、')}等`;
      }
      return names.join('、');
    }
    return "未知服务";
  })();
  const servicePrice = item.servicePrice ?? (item.projects && item.projects[0]?.projectDiscPrice) ?? 0;
  const remainTime = item.remainingTime || "";
  const serviceTime = item.serviceTime || item.reservationDate || "";
  const employees = item.employees || [];
  const mainEmployee = employees[0];

  // 计算 Picker 的初始值
  const getInitialPickerValue = () => {
    const currentTime = dateTime ? dayjs(dateTime) : dayjs();
    // 计算与今天的日期差
    const today = dayjs().startOf('day');
    const targetDate = currentTime.startOf('day');
    const dateIndex = targetDate.diff(today, 'day');

    // 如果日期差小于0（过去的日期）或大于30（超过30天），则使用今天
    const validDateIndex = dateIndex >= 0 && dateIndex < 30 ? dateIndex : 0;
    const hourIndex = currentTime.hour();
    const minuteIndex = Math.floor(currentTime.minute() / 10);

    // console.log('当前时间:', currentTime.format('YYYY-MM-DD HH:mm:ss'));
    // console.log('日期差:', dateIndex);
    // console.log('有效日期索引:', validDateIndex);
    // console.log('小时:', hourIndex);
    // console.log('分钟:', minuteIndex);

    return [validDateIndex, hourIndex, minuteIndex];
  };

  const cache: GlobalInfoStore = useStore().globalInfoStore;
  const shopInfo = cache.getShopInfo();
  console.log(shopInfo.businessName, 'shopInfo')

  const sendMessage = async (item: BUSINESS.ReservationRecordListVo) => {
    const result = await postCommonMsgSend({
      "msgType": 3, // 消息类型
      "customerAppointmentDate": item.reservationDate, // 客户会员预约时间
      "businessName": shopInfo.businessName, // 商家名称
      "businessPhone": shopInfo.contactPhone, // 商家电话
      "customerNickName": item.nickName, // 客户会员名称
      "serviceName": item.serviceName // 服务名称
    })
    console.log(result, 'result')
  }
  // 确认接单
  const handleAccept = async () => {
    Taro.requestSubscribeMessage({
      tmplIds: [
        'xQEqSHKy10wEgYrGj1mMR_zNr3xTDaym7HONpIpxKgY', // 开卡成功通知：卡号、姓名、实收金额、会员有效期、赠送金额
        // 'EmH5R8DYrRPh_deEkSrM79gbfyH5dBanWw3nDw-UNso', // 优惠买单通知：店铺名称、消费金额、实付金额、支付时间、支付方式
        '5n6PlynhXynzxcvIh2EgasAa0Xlo2hQhrk1mXbdu6Vs', // 服务预约成功提醒：预约时间、门店、电话、预约会员、服务名称
        'CVPQxV69Assu-Zj5Wojv59rMfhMZnhY_JbTFFxg2tk0'  // 会员加入提醒：会员名称、手机号、注册门店、注册时间、会员卡号
      ],
      success(res) {
        console.log('用户订阅成功', res);
      },
      fail(err) {
        console.error('用户订阅失败', err);
      }
    });
    Taro.showModal({
      title: '确认接单',
      content: '确定要接单吗？',
      confirmColor: '#FFB300',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await postReservationRecordAccept({ reservationRecordId: item.id ?? 0 });
            if (result) {
              Taro.showToast({ title: '接单成功', icon: 'success' });
              if (onRefresh) {
                onRefresh();
                // 消息推送
                await sendMessage(item)
              }
            } else {
              Taro.showToast({ title: result?.msg || '接单失败', icon: 'none' });
            }
          } catch (error) {
            Taro.showToast({ title: '网络异常', icon: 'none' });
          }
        }
      }
    });
  };

  // 处理时间选择
  const handleTimeSelect = async (e: any) => {
    const [dateIndex, hourIndex, minuteIndex] = e.detail.value;
    const selectedDate = dayjs().add(dateIndex, 'day');
    const dateTimeStr = selectedDate
      .hour(hourIndex)
      .minute(minuteIndex * 10)
      .format('YYYY-MM-DDTHH:mm:ss');
    console.log(dateTimeStr, '---dateTimeStr')
    setDateTime(dateTimeStr);

    // 二次确认
    Taro.showModal({
      title: '确认修改预约时间',
      content: `确定将预约时间修改为 ${selectedDate.format('MM月DD日')} ${hourIndex < 10 ? '0' : ''}${hourIndex}:${minuteIndex === 0 ? '00' : minuteIndex + '0'} 吗？`,
      confirmColor: '#FFB300',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await postReservationRecordEditReservationDate({
              id: item.id ?? 0,
              reservationDate: dateTimeStr,
            });
            Taro.showToast({ title: "修改成功", icon: "success" });
            onRefresh && onRefresh();
          } catch (error) {
            Taro.showToast({ title: result?.msg || "修改失败", icon: "none" });
          }
        }
      }
    });
  };

  return (
    <View
      className={styles.reservationCard}
      style={{
        background: "#fff",
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        boxShadow: "0 2px 8px rgba(0,0,0,0.03)",
      }}
    >
      {/* 顶部状态和价格 */}
      <View className="flex items-center mb-2 w-full">
        <View
          style={{
            background: status.bg,
            color: status.color,
            borderRadius: "10px 10px 0 10px",
            padding: "2 10",
            fontWeight: 500,
            marginRight: 8,
            width: 34,
            lineHeight: '20px',
            textAlign: "center",
            fontSize: '10px',
          }}
          className="text-xs flex-shrink-0"
        >
          {status.text}
        </View>
        <Text style={{
          color: '#161616',
          fontWeight: '600'
        }} className="font-semibold text-sm text-[#222] mr-1 flex-1 min-w-0 truncate">{serviceName}</Text>
        <Text style={{
          color: '#161616',
          fontWeight: '600',
          fontSize: '16px',
        }} className="font-semibold text-sm text-[#222] flex-shrink-0">￥{servicePrice}</Text>
        <Text className="text-xs text-[#999] ml-2 flex-shrink-0">元</Text>
        {item.status === 1 && remainTime && (
          <View className="flex items-center flex-shrink-0 ml-2">
            <Text className="text-xs text-[#999]">剩余时间</Text>
            <Text className="text-xs text-[#FFB300] ml-1">{remainTime}</Text>
          </View>
        )}
      </View>
      {/* 服务名称 */}
      <View style={{ display: "flex", alignItems: "center", marginBottom: 4 }}>
        <Text className="text-sm text-[#999] mr-1">服务名称：</Text>
        <Text
          className="text-sm text-[#222] flex-1 truncate"
          style={{
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {serviceName}
        </Text>
      </View>
      {/* 服务时间和修改时间 */}
      <View style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
        <Text className="text-sm text-[#999] mr-1">服务时间：</Text>
        <Text className="text-sm text-[#222] mr-2">{serviceTime ? dayjs(serviceTime).format('YYYY-MM-DD HH:mm') : "未设置"}</Text>
        {[0, 1]?.includes(item.status as number) && (
          <Picker
            mode="multiSelector"
            range={dateTimeRange}
            value={getInitialPickerValue()}
            onChange={handleTimeSelect}
          >
            <Text style={{
              fontSize: '13px',
            }}
              className="text-xs text-[#FFB300] ml-2 cursor-pointer"
            >
              修改时间
            </Text>
          </Picker>
        )}
      </View>
      {/* 员工与操作按钮 */}
      <View style={{ display: "flex", alignItems: "center", marginTop: 16 }}>
        <View style={{ display: "flex", alignItems: "center", flex: 1 }}>
          <Image
            src={mainEmployee?.avatarUrl || ""}
            style={{
              width: 28,
              height: 28,
              borderRadius: "50%",
              marginRight: 8,
              background: "#F0F0F0",
              objectFit: "cover",
            }}
          />
          <Text className="text-sm text-[#222]">
            {mainEmployee?.employeeNickname || item.nickName || "未知员工"}
          </Text>
        </View>
        <View style={{ display: "flex", gap: 12 }}>
          {item.status === 0 && (
            <View
              style={{
                minWidth: 80,
                height: 28,
                borderRadius: 18,
                fontSize: '14px',
                background: "#fff",
                border: "1px solid #E5E5E5",
                color: "#222",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: 500,
                marginRight: 0,
                cursor: "pointer",
                boxSizing: "border-box",
              }}
              className="text-base"
              onClick={handleAccept}
            >
              确认
            </View>
          )}
          <View
            style={{
              minWidth: 100,
              height: 28,
              borderRadius: 18,
              fontSize: '14px',
              background: "#FFCC33",
              color: "#fff",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontWeight: 500,
              cursor: "pointer",
              boxSizing: "border-box",
            }}
            className="text-base"
            onClick={() => {
              const phone = item.phonenumberMasked;
              if (phone) {
                Taro.makePhoneCall({ phoneNumber: phone });
              } else {
                Taro.showToast({ title: '无联系电话', icon: 'none' });
              }
            }}
          >
            联系客户
          </View>
        </View>
      </View>
    </View>
  );
};

export default ReservationItem; 
