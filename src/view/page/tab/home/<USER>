@import "~taro-ui/dist/style/components/grid.scss";
@import "~taro-ui/dist/style/components/icon.scss";

.container {
	// padding-bottom: env(safe-area-inset-bottom);
	height: 100%;
	background-color: #f5f5f5;

	.scrollView {
		width: 100%;
		height: 100%;
		position: relative;

		// 若想让背景保持不动，往上移一层
		.bg {
			position: absolute;
			width: 100%;
			height: 184px;
			top: 0;
			left: 0;
			background-image: url("../../../../assets/image/home/<USER>");
			background-size: 100% 100%;
		}
	}

	.homeWrapper {
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 1;
		background-color: transparent;

		.homeInfoSection {
			background-size: 100% 100%;

			.homeInfoHeader {
				display: flex;
				align-items: center;
				justify-content: center;

				.homeHeaderIconWrapper {
					display: flex;
					align-items: center;

					.homeHeaderIcon {
						position: absolute;
						left: 17px;
						width: 21px;
						height: 20px;
					}

					.homeHeaderText {
						color: #1f1f1f;
						font-weight: 400;
						font-size: 18px;
						text-align: center;
					}
				}
			}
		}

		.shopHeader {
			display: flex;
			align-items: center;
			margin-top: 10px;
			margin-bottom: 22px;

			.shopHeaderIcon {
				width: 21px;
				height: 20px;
				margin-right: 8px;
				margin-left: 17px;
			}

			.downIcon {
				width: 8px;
				height: 6px;
				margin-left: 5px;
			}

			.searchIcon {
				width: 24px;
				height: 24px;
				position: absolute;
				right: 12px;
			}

			.shopName {
				font-size: 15px;
				font-weight: 500;
				color: #1F1F1F;
				max-width: 80%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.shopCenter {
			margin-left: 10px;
			margin-right: 10px;
			margin-bottom: 10px;
		}

		.todayStats {
			border-radius: 16px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
			background-color: #fff;
			margin-top: 10px;

			.statsTitle {
				display: flex;
				justify-content: space-between;
				align-items: center;
				line-height: 30px;
				padding: 6px 8px 6px 13px;

				Text {
					&:first-child {
						font-size: 14px;
						font-weight: 600;
						color: #000000;
					}
				}

				.statsTime {
					font-size: 12px;
					color: #999999;
				}
			}

			.mainStats {
				display: flex;
				justify-content: space-between;
				margin-bottom: 20px;
				padding: 0 12px;
				gap: 8px;

				.blue {
					background: #DEEFFF;
				}

				.pink {
					background: #FAF2FF;
				}

				.statsItem {
					border-radius: 8px;
					display: flex;
					width: 100%;
					align-items: center;

					.statsIcon {
						width: 28px;
						height: 28px;
						margin-right: 16px;
						padding: 16px 0 14px 14px;
					}

					> View {
						display: flex;
						flex-direction: column;
						justify-content: center;
					}

					.statsValue {
						font-size: 18px;
						font-weight: 600;
						color: #000000;
						margin-bottom: 4px;
						display: flex;
						align-items: center;
					}

					.statsLabel {
						font-size: 12px;
						color: #666666;
						margin-top: 4px;
					}

					.statsValueUnit {
						color: #999999;
						font-size: 12px;
						margin-left: 4px;
					}
				}
			}

			.subStats {
				display: flex;
				justify-content: space-between;
				margin-bottom: 4px;
				padding: 0 12px;
				gap: 29px;

				.subStatsItem {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					position: relative;

					.subLabel {
						font-size: 12px;
						color: #999;
						margin-bottom: 4px;
					}

					.subValue {
						font-size: 18px;
						color: #333333;
						font-weight: 500;
						line-height: 30px;
						display: flex;
						align-items: center;
					}

					.subValueUnit {
						color: #999999;
						font-size: 12px;
						margin-left: 4px;
					}
				}
			}

			.statMore {
				text-align: center;
				padding-bottom: 14px;

				.statDownIcon {
					width: 28px;
					height: 14px;
				}
			}
		}

		.scanVerify {
			border-radius: 8px;
			height: 42px;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 8px;

			.scanVerifyImg {
				width: 15px;
				height: 14px;
				margin-right: 8px;
			}
		}

		.reservationList {
			padding: 10px;
			width: calc(100% - 20px);
			background-color: #f5f5f5;
		}
	}

	:global {
		.at-grid {
			border-radius: 16px;
			background-color: #fff;
			margin-top: 10px;
			padding: 10px 0;

			.at-grid__flex {
				.content-inner__img {
					width: 50px;
					height: 50px;
				}

				.content-inner__text {
					font-size: 13px;
					color: #1F1F1F;
					font-weight: 500;
					margin-top: 6px;
				}
			}
		}

		.at-tabs {
			// margin-top: 10px;
			// position: relative;
			// top: -112rpx;
			// margin-bottom: -46px;

			// .at-tabs__item {
			// 	font-size: 15px;
			// 	color: #999999;
			// }

			// .at-tabs__item--active {
			// 	color: #333333;
			// 	font-size: 15px;
			// 	font-weight: 600;
			// }

			// .at-tabs__item-underline {
			// 	left: 38%;
			// 	width: 30px;
			// 	height: 3PX;
			// }

		}

		.at-card {
			border-radius: 8px;
			border: 0.5px solid #F2F2F2;
		}
	}

	.businessRegisterDiv {
		width: 100%;
		margin-top: 10px;
		margin-bottom: 10px;
	}

	.businessRegisterImg {
		border-radius: 16px;
		height: 106px;
		width: 100%;
	}

}

.logoutActionSheet {
	width: 100%;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.5);
	display: none;
	z-index: 9999;
	box-sizing: border-box;

	.actionPanel {
		box-sizing: border-box;
		width: 100%;
		position: absolute;
		bottom: 0;
		transform: translateY(200%);
		transition: .5s;
		transition-delay: .5s;

		.logoutBtn {
			box-sizing: border-box;
			margin: 0 10px;
			border-radius: 8px;
			height: 50px;
			background-color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			color: red;
			cursor: pointer;
		}
	}



	&.show {
		display: block;

		.actionPanel {
			transform: translateY(0);
		}
	}
}