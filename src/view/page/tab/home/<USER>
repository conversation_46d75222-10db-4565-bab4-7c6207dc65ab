.reservationStatus {
  width: 34px;
  height: 18px;
  border-radius: 10px 10px 0px 10px;
  margin-right: 6px;
  display: inline-grid;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.reservationItem {
  border-radius: 8px;
  overflow: hidden;
  margin: 15px 10px 10px 10px;
  background: #ffffff;
  padding: 12px;

  .reservationItemHeader {
    display: flex;
    font-size: 14px;
    justify-content: space-between;
    display: flex;



    .reservationStatusText {
      font-size: 10px;
      color: #FFFFFF;
    }
  }

  .appiont {
    background: #FDD244;
  }

  .unAppiont {
    background: #F43535;
  }

  .remainTime {
    display: flex;
    align-items: center;
    justify-content: center;

    .remainTimeText {
      font-size: 12px;
      color: #666666;
      margin-right: 6px;
    }

    .remainTimeValue {
      font-size: 15px;
      color: #F1C125;
      font-weight: 600;
    }

    .unAppiontValue {
      color: #F43535;
    }
  }

  .reservationItemName {
    .reservationItemTitle {
      font-size: 14px;
      font-weight: 600;
      color: #161616;
      margin-right: 6px;
    }

    .reservationItemPrice {
      font-size: 16px;
      color: #161616;
      font-weight: 600;
    }

    .reservationItemUnit {
      font-weight: 600;
      font-size: 14px;
      color: #161616;
    }
  }
}

.reservationContent {
  .reservationInfo {
    .serviceName {
      display: flex;
      align-items: center;
      margin-top: 14px;
      margin-bottom: 7px;
      font-size: 14px;
      color: #666;

      .serviceNameText {
        color: #999999;
      }

      .serviceNameValue {
        color: #191919;
      }
    }

    .serviceTime {
      display: flex;
      align-items: center;
      margin-top: 14px;
      margin-bottom: 17px;
      font-size: 14px;
      color: #666;
      justify-content: space-between;

      .timeLabel {
        color: #999999;
      }

      .timeValue {
        color: #191919;
      }

      .modifyTime {
        color: #F1C125;
        font-size: 13px;
      }
    }

    .staffInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 10px;
      border-top: 1px solid #f0f0f0;

      .staffNameInfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .staffName {
        font-size: 14px;
        color: #333;
        margin-right: 24px;
      }

      .staffIcon {
        width: 26px;
        height: 26px;
        margin-right: 8px;
      }

      .actionButtons {
        display: flex;

        :global {
          .at-button {
            height: 28px;
            font-size: 14px;
            border-radius: 14px;
            align-items: center;
          }
        }

        .confirmBtn {
          color: #333333;
          border: 1px solid rgba(221, 221, 221, 0.82);
        }

        .contactBtn {
          margin-left: 10px;
          color: #FFFFFF;
        }
      }
    }
  }
}