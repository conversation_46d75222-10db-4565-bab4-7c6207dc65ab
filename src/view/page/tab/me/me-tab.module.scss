.container {
	display: flex;
	flex-direction: column;
	gap: 32px;
	padding: 32px;
	background: #F0F0F0;

	> .user {
		display: flex;
		flex-direction: row;
		gap: 24px;
		width: 100%;
		height: 128px;
		padding-top: 48px;

		> .logo {
			width: 128px;
			height: 128px;
			border-radius: 50%;
		}

		> .info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			gap: 12px;
			padding: 16px 0;

			> .name {
				color: #030504;
				font-size: 36px;
				font-weight: bold;
			}

			> .member {
				width: 148px;
				height: 36px;
			}
		}

		> .control {
			display: flex;
			justify-content: center;
			align-items: center;

			> .button {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 8px;
				background: transparent;
				border: none;
				color: #BCC2CC;
				font-size: 28px;
			}
		}
	}

	> .order {
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;

		> .item {
			border-bottom: 1px solid #F4F6FA;
		}

		> .panel {
			display: flex;
			flex-direction: row;
			align-items: center;
			height: 150px;

			> .each {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 12px;

				> .icon {
					width: 50px;
					height: 50px;
				}

				> .text {
					color: #6B7080;
					font-size: 24px;
				}
			}
		}
	}

	> .list {
		background: #FFFFFF;
		border-radius: 32px;
		overflow: hidden;
	}

	>.shareButton{
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 88px;
		background: #FF6600;
		color: #FFFFFF;
		font-size: 30px;
	}
}
