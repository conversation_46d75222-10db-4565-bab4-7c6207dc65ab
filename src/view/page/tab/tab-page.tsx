import { View } from "@tarojs/components";

import CustomTabBar from "@/view/component/custom-tab-bar";
import { observer } from "mobx-react";
import React, { useContext, useEffect, useRef, useState } from "react";
import styles from "./tab-page.module.scss";
import Taro from "@tarojs/taro";
import HomeTab from "./home/<USER>";

// import HomeTab from "./home/<USER>";

const TabPage: React.FC = observer(() => {
	const [current, setCurrent] = useState(0);

	const switchTab = (e: number) => {
		setCurrent(e);
		console.log(e);
	};

	//todo 计算下方的安全距离

	return (
		<View className={styles.container}>
			<View
				className={styles.wrapper}
			>
				<View
					className={styles.each}
					// style={{ display: current == 0 ? "block" : "none" }}
				>
					<HomeTab />
				</View>
				{/* <View
					className={styles.each}
					style={{ display: current == 2 ? "block" : "none" }}
				>
				</View> */}
			</View>

			{/* <CustomTabBar onClick={(path, i) => switchTab(i)} /> */}
		</View>
	);
});

export default TabPage;
