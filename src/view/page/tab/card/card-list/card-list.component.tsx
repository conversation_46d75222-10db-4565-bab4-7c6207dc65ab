import { View, Text } from "@tarojs/components";
import { observer } from "mobx-react";
import { pinyin } from "pinyin-pro";
import styles from "./card-list.module.scss";
import cls from "classnames";
import { useState } from "react";
import MemberCard from "@/member/member-card";

const CardList: React.FC = observer(() => {
	const data = [
		{
			name: "深圳逸美美发",
			cno: "32300545",
			level: "白金卡",
			bg_color: "#f03f30",
			title_color: "#FFFFFF",
			content_color: "#FFFFFF",
			cno_color: "#FFFFFF",
			level_color: "#FFFFFF",
		},
		{
			name: "杭州轩意美容医院",
			cno: "56565667",
			level: "普卡",
			bg_color: "#3F58E3",
			title_color: "#FFFFFF",
			content_color: "#FFFFFF",
			cno_color: "#FFFFFF",
			level_color: "#FFFFFF",
		},
		{
			name: "杭州易美整形",
			cno: "88888888",
			level: "黑金卡",
			bg_color: "#000000",
			title_color: "rgb(255,215,0)",
			content_color: "rgb(255,215,0)",
			cno_color: "rgb(255,215,0)",
			level_color: "rgb(255,215,0)",
		},
		{
			name: "杭州追忆美甲中心",
			cno: "32777778",
			level: "银卡",
			bg_color: "#8063E1",
			title_color: "#FFFFFF",
			content_color: "#FFFFFF",
			cno_color: "#FFFFFF",
			level_color: "#FFFFFF",
		},
	];
	const [activeIdx, setActiveIdx] = useState<number | null>(null);

	return (
		<View className="pt-[240px]">
			<View className={styles.tariffCards}>
				{data.map((item, idx) => (
					<MemberCard
						key={idx}
						name={item.name}
						no={item.cno}
						level={item.level}
						name_color={item.title_color}
						pinyin_color={item.content_color}
						no_color={item.cno_color}
						level_color={item.level_color}
						bg_color={item.bg_color}
						onClick={() => {
							if (activeIdx === idx) {
								setActiveIdx(null);
							} else {
								setActiveIdx(idx);
							}
						}}
						className={cls({
							[styles.active]: activeIdx === idx,
						})}
						style={{
							zIndex: idx,
							transform: `translateY(${
								activeIdx !== null && activeIdx < idx ? 102 : 0
							}px)`,
							marginTop: idx > 0 ? -100 : 0,
						}}
					/>
				))}
			</View>
		</View>
	);
});

export default CardList;
