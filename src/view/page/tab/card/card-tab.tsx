import React, { useRef } from "react";
import { observer } from "mobx-react";
import Page<PERSON>ontainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import FrameScrollContainer from "@/view/component/frame-scroll-container/frame-scroll-container.component";
import styles from "./card-tab.module.scss";
import CardList from "./card-list/card-list.component";
import CardDetail from "./card-detail/card-detail.component";

const CardTab: React.FC = observer(() => {
	const container: PageContainerRef = useRef(null);

	return (
		<CardDetail />
		// <CardList />
		// <PageContainer ref={container}>
		// 	<FrameScrollContainer
		// 		innerClassName={styles.container}
		// 		showScrollbar={false}
		// 	>
		// 		<CardList />
		// 	</FrameScrollContainer>
		// </PageContainer>
	);
});

export default CardTab;
