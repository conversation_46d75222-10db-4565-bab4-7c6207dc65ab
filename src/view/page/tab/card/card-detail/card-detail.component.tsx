import { View, Text, Button } from "@tarojs/components";
import { observer } from "mobx-react";
import { pinyin } from "pinyin-pro";
import styles from "./card-list.module.scss";
import cls from "classnames";
import { useState } from "react";
import MemberCard from "@/member/member-card";
import Confetti from "@/view/component/confetti";

const demo = {
	name: "杭州易美整形",
	no: "88888888",
	level: "黑金卡",
	bg_color: "#000000",
	name_color: "rgb(255,215,0)",
	pinyin_color: "rgb(255,215,0)",
	no_color: "rgb(255,215,0)",
	level_color: "rgb(255,215,0)",
};

const CardDetail: React.FC = observer(() => {
	const onClick = () => {};

	return (
		<View className="pt-8">
			<View className="relative flex mx-auto w-[95%] flex-col rounded-xl bg-white bg-clip-border text-gray-700 shadow-md">
				<MemberCard
					{...demo}
					className="relative mx-auto -mt-6 overflow-hidden rounded-xl bg-blue-gray-500 bg-clip-border text-white shadow-lg shadow-blue-gray-500/40"
				/>
				<Confetti />
				{/* <View className="relative mx-4 -mt-6 h-40 overflow-hidden rounded-xl bg-blue-gray-500 bg-clip-border text-white shadow-lg shadow-blue-gray-500/40 bg-gradient-to-r from-blue-500 to-blue-600"></View> */}
				<View className="p-6">
					<View className="mb-2 block font-sans text-xl font-semibold leading-snug tracking-normal text-blue-gray-900 antialiased">
						会员卡 - 龚如意
					</View>
					<View className="block font-sans text-base font-light leading-relaxed text-inherit antialiased">
						欢迎龚如意女士加入易美整形会员卡，👏👏👏
					</View>
				</View>
				<View className="p-6 pt-0">
					<Button
						data-ripple-light="true"
						className="select-none rounded-lg bg-blue-500 py-3 px-6 text-center align-middle font-sans text-xs font-bold uppercase text-white shadow-md shadow-blue-500/20 transition-all hover:shadow-lg hover:shadow-blue-500/40 focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
					>
						分享会员卡
					</Button>
				</View>
			</View>
		</View>
	);
});

export default CardDetail;
