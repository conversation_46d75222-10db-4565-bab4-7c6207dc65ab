.container {
	display: flex;
	flex-direction: row;
	width: 100%;
	height: 100%;
	background: #FFFFFF;

	> .left {
		height: 100%;

		> .scroll {
			height: 100%;

			.each {
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
				width: 192px;
				height: 112px;
				background: #F5F6FA;
				color: #1A1A1A;
				font-size: 26px;
				line-height: 0;
				transition: 0.25s;

				.activeBlock {
					position: absolute;
					width: 10px;
					height: 32px;
					left: 0;
					top: 50%;
					margin-top: -16px;
					background: #FF6600;
					border-top-right-radius: 4px;
					border-bottom-right-radius: 4px;
					transition: 0.25s;
				}
			}

			.active {
				background: #FFFFFF !important;
				font-size: 30px;
				font-weight: bold;
			}
		}
	}

	> .right {
		flex: 1;
		height: 100%;
		overflow: hidden;

		> .scroll {
			height: 100%;
		}
	}
}