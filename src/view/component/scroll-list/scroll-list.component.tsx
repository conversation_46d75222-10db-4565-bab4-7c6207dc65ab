import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { AtLoadMore } from "taro-ui";
import styles from "./scroll-list.module.scss";

export type ScrollListRef = React.Ref<{
	reset: () => void
}>;

interface ScrollListProps<T = any> {
	dataSource: T[];
	total: number;
	pageSize?: number;
	renderItem: (item: T, index: number) => React.ReactNode;
	onLoadMore: (pageNum: number) => void;
	className?: string;
	wrapperClassName?: string;
	style?: React.CSSProperties;
	emptyComponent?: React.ReactNode;
	noMoreText?: string;
	keyExtractor?: (item: T, index: number) => string;
}

const ScrollList = forwardRef((props: ScrollListProps, ref: ScrollListRef) => {
	const { dataSource = [], total = 0, pageSize = 10, renderItem, onLoadMore,
		className, wrapperClassName, style, emptyComponent, noMoreText } = props;

	const [pageNum, setPageNum] = useState(1);
	const [status, setStatus] = useState<"more" | "noMore" | "loading">("more");

	const [clientHeight, setClientHeight] = useState(0);

	// 可视区域高度
	useEffect(() => {
		const query = Taro.createSelectorQuery();
		query
			.select("#scrollView")
			.boundingClientRect((rect: any) => {
				rect && setClientHeight(rect?.height);
			})
			.exec();
	}, []);

	/** 根据数据源和总数判断加载状态 */
	useEffect(() => {
		if (dataSource.length > 0 || dataSource.length === total) {
			setStatus("noMore");
		} else {
			if (status === "noMore") {
				setPageNum(1)
			}
			setStatus("more");
		}
	}, [dataSource, total]);

	// 滚动事件
	const onScroll = useCallback(
		(e) => {
			const { scrollTop, scrollHeight } = e.detail;

			// 判断是否滚动到底部
			if (scrollTop + clientHeight >= scrollHeight - 30) {
				if (pageNum * pageSize < total) {
					setPageNum((prev) => {
						const newPageNum = prev + 1;
						setStatus("loading");
						onLoadMore(newPageNum);
						return newPageNum;
					});
				} else {
					setStatus("noMore");
				}
			}
		},
		[clientHeight, pageNum, pageSize, total, onLoadMore]
	);

	// 使用ScrollView组件原生的触底方法
	const onReachBottom = useCallback(() => {
		if (pageNum * pageSize < total) {
			setPageNum((prev) => prev + 1);
			setStatus("loading");
			onLoadMore(pageNum + 1);
		} else {
			setStatus("noMore");
		}
	}, [pageNum, pageSize, total, onLoadMore])

	useImperativeHandle(ref, () => ({
		reset: () => {
			setPageNum(1);
		}
	}))

	return (
		dataSource.length > 0 ? (
			<ScrollView
				scrollY
				scrollTop={0}
				style={{ overflowY: "auto", ...style }}
				className={`${styles.scrollView} ${className}`}
				id="scrollView"
				upperThreshold={20}
				// onScroll={onScroll}
				onScrollToLower={onReachBottom}
			>
				<View className={`${styles.listContainer} ${wrapperClassName}`}>
					{dataSource.map((item, index) => (
						<>
							{renderItem(item, index)}
						</>
					))}
				</View>
				<AtLoadMore className={styles.loadMore} status={status} noMoreText={noMoreText || "已经到底啦～"} noMoreTextStyle={{ fontSize: 13 }} moreBtnStyle={{ fontSize: 13 }} />
			</ScrollView>
		) : (
			emptyComponent || <View className={styles.emptyList}>暂无数据</View>
		)
	)
})


// function ScrollList<T>({
// 	dataSource = [],
// 	total = 0,
// 	pageSize = 10,
// 	renderItem,
// 	onLoadMore,
// 	className = "",
// 	style = {},
// 	emptyComponent,
// 	keyExtractor = (_, index) => index.toString(),
// }: ScrollListProps<T>) {
// 	const [pageNum, setPageNum] = useState(1);
// 	const [status, setStatus] = useState<"more" | "noMore" | "loading">("more");
// 	const [clientHeight, setClientHeight] = useState(0);

// 	// 可视区域高度
// 	useEffect(() => {
// 		const query = Taro.createSelectorQuery();
// 		query
// 			.select("#scrollView")
// 			.boundingClientRect((rect: any) => {
// 				rect && setClientHeight(rect?.height);
// 			})
// 			.exec();
// 	}, []);

// 	/** 根据数据源和总数判断加载状态 */
// 	useEffect(() => {
// 		if (dataSource.length > 0 || dataSource.length === total) {
// 			setStatus("noMore");
// 		} else {
// 			setStatus("noMore");
// 		}
// 	}, [dataSource, total]);

// 	// 滚动事件
// 	const onScroll = useCallback(
// 		(e) => {
// 			const { scrollTop, scrollHeight } = e.detail;

// 			// 判断是否滚动到底部
// 			if (scrollTop + clientHeight >= scrollHeight - 30) {
// 				if (pageNum * pageSize < total) {
// 					setPageNum((prev) => {
// 						const newPageNum = prev + 1;
// 						setStatus("loading");
// 						onLoadMore(newPageNum);
// 						return newPageNum;
// 					});
// 				} else {
// 					setStatus("noMore");
// 				}
// 			}
// 		},
// 		[clientHeight, pageNum, pageSize, total, onLoadMore]
// 	);

// 	// 使用ScrollView组件原生的触底方法
// 	const onReachBottom = useCallback(() => {
// 		if (pageNum * pageSize < total) {
// 			setPageNum((prev) => {
// 				const newPageNum = prev + 1;
// 				setStatus("loading");
// 				onLoadMore(newPageNum);
// 				return newPageNum;
// 			});
// 		} else {
// 			setStatus("noMore");
// 		}
// 	}, [pageNum, pageSize, total, onLoadMore])

// 	return dataSource.length > 0 ? (
// 		<ScrollView
// 			scrollY
// 			scrollTop={0}
// 			style={{ overflowY: "auto", ...style }}
// 			className={`${styles.scrollView} ${className}`}
// 			id="scrollView"
// 			// lowerThreshold={20}
// 			upperThreshold={20}
// 			// onScroll={onScroll}
// 			onScrollToLower={onReachBottom}
// 		>
// 			<View className={styles.listContainer}>
// 				{dataSource.map((item, index) => (
// 					<View
// 						key={keyExtractor(item, index)}
// 						className={styles.listItem}
// 					>
// 						{renderItem(item, index)}
// 					</View>
// 				))}
// 			</View>
// 			<AtLoadMore status={status} />
// 		</ScrollView>
// 	) : (
// 		emptyComponent || <View className={styles.emptyList}>暂无数据</View>
// 	);
// }

export default ScrollList;
