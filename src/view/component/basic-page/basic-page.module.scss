.basicPage {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .navBar {
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .backButton {
            position: absolute;
            left: 12px;
            padding: 2px 8px;
        }

        .title {
            color: #1f1f1f;
            font-weight: 400;
            font-size: 16px;
            text-align: center;
        }
    }

    .container {
        flex: 1 0;
        overflow-y: auto;
        position: relative;
    }
}