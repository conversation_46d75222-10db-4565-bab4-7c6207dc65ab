import { View, Text } from "@tarojs/components";
import styles from "./basic-page.module.scss"
import { forwardRef, ReactNode, useImperativeHandle, useRef, useState } from "react";
import { navigateBack } from "@tarojs/taro";
import useWindowArea from "@/hook/windowArea";
import Message, {
    MessageRef,
    OpenMessageParam,
} from "@/view/component/message/message.component";
import Taro from "@tarojs/taro";
import { PageContainerRef } from "../page-container/page-container.component";

interface IProps {
    title: string,
    showBack?: boolean,
    bottomSafe?: boolean,
    children?: ReactNode,
    onBack?: () => void
}

const BasicPage = forwardRef((props: IProps, ref: PageContainerRef) => {
    const { title, showBack = true, bottomSafe = false, children, onBack } = props;

    const { bottomArea, topArea, navArea } = useWindowArea();

    const handleBack = () => {
        onBack?.() ?? navigateBack();
    };

    const messageRef: MessageRef = useRef(null);

    const openMessage = (data: OpenMessageParam) => {
        messageRef.current?.openMessage(data);
    };

    useImperativeHandle(ref, () => {
        return {
            openMessage: (data) => openMessage(data),
        };
    });

    return (
        <View className={styles.basicPage} style={{ paddingBottom: bottomSafe ? bottomArea : 0 }}>
            <View className={styles.navBar} style={{ height: navArea + topArea, paddingTop: topArea }}>
                {
                    showBack && <View className={styles.backButton} onClick={handleBack}>
                        <View className="at-icon at-icon-chevron-left"></View>
                    </View>
                }

                <Text className={styles.title}>{title}</Text>
            </View>
            <View className={styles.container}>
                <Message ref={messageRef}></Message>
                {children}
            </View>
        </View>
    )
})

export default BasicPage;