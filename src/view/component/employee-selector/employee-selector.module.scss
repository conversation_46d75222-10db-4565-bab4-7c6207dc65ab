.mask {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.content {
  width: 100vw;
  max-width: 500px;
  height: 60vh;
  background: #fff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .allocationBtn {
    font-size: 14px;
    color: #FFCC33;
    padding: 4px 12px;
    border-radius: 16px;
    background: rgba(255, 204, 51, 0.1);
  }
}

.close {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.list {
  flex: 1;
  overflow-y: auto;
  background: #fff;
}

.item {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 20px;
  border-bottom: 1px solid #F0F0F0;
  cursor: pointer;
  font-size: 16px;
  color: #222;
  background: #fff;
  transition: background 0.2s;
  &:active {
    background: #f5f5f5;
  }
}

.name {
  flex: 1;
  color: #222;
  font-size: 16px;
  font-weight: 400;
}
.selectedName {
  flex: 1;
  color: #FFB300;
  font-size: 16px;
  font-weight: 600;
}

.checkBox {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.checkedIcon {
  width: 22px;
  height: 22px;
}

.uncheckedBox {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  border: 1px dashed #D8D8D8;
  background: #fff;
}

.footer {
  padding: 16px;
  border-top: 1px solid #F5F5F5;
}

.footerContent {
  display: flex;
  gap: 12px;
}

.cancelBtn {
  flex: 1;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #FFCC33;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFCC33;
  font-size: 16px;
}

.confirmBtn {
  flex: 1;
  height: 44px;
  background: #FFCC33;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 16px;
} 