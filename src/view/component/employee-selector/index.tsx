import { View, Text, Image } from "@tarojs/components";
import { useEffect, useState, FC } from "react";
import CheckedYellowImage from '@/assets/image/checked.png';
import styles from './employee-selector.module.scss';

export interface EmployeeItem {
  employeeId: number;
  employeeNickname: string;
  avatarUrl?: string;
  position?: string;
}

interface EmployeeSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (employees: EmployeeItem[], isAllocation: boolean) => void;
  selectedEmployees?: EmployeeItem[];
  employees: EmployeeItem[];
}

const EmployeeSelector: FC<EmployeeSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  selectedEmployees = [],
  employees
}) => {
  const [selected, setSelected] = useState<EmployeeItem[]>(selectedEmployees);

  const handleSelect = (employee: EmployeeItem) => {
    const isSelected = selected.some(item => item.employeeId === employee.employeeId);
    if (isSelected) {
      setSelected(selected.filter(item => item.employeeId !== employee.employeeId));
    } else {
      setSelected([...selected, employee]);
    }
  };

  const handleConfirm = () => {
    onSelect(selected, false);
    onClose();
  };

  const handleCanal = () => {
    setSelected([]);
    onSelect([], true);
    onClose();
  }

  useEffect(() => {
    setSelected(selectedEmployees);
  }, [selectedEmployees]);

  if (!visible) return null;

  return (
    <View className={styles.mask} onClick={onClose}>
      <View className={styles.content} onClick={e => e.stopPropagation()}>
        {/* 头部 */}
        <View className={styles.header}>
          <Text className={styles.title}>选择员工</Text>
          <View className={styles.allocationBtn} onClick={handleCanal}>
            到店分配
          </View>
        </View>

        {/* 员工列表 */}
        <View className={styles.list}>
          {employees.map(employee => {
            const isSelected = selected.some(item => item.employeeId === employee.employeeId);
            return (
              <View
                key={employee.employeeId}
                className={styles.item}
                onClick={() => handleSelect(employee)}
              >
                <Text className={isSelected ? styles.selectedName : styles.name}>
                  {employee.employeeNickname}
                </Text>
                <View className={styles.checkBox}>
                  {isSelected ? (
                    <Image
                      src={CheckedYellowImage}
                      className={styles.checkedIcon}
                      mode="aspectFit"
                    />
                  ) : (
                    <View className={styles.uncheckedBox} />
                  )}
                </View>
              </View>
            );
          })}
        </View>

        {/* 底部按钮 */}
        <View className={styles.footer}>
          <View className={styles.footerContent}>
            <View className={styles.confirmBtn} onClick={handleConfirm}>
              确定
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default EmployeeSelector; 