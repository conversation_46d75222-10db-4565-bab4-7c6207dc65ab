import { PageContainer, PageContainerProps } from "@tarojs/components";
import FloatLayout, { FloatLayoutProps } from "../float-layout";
import { useRef } from "react";

interface PopupProps {
	open?: boolean;
	onClose?: () => void;
	children?: React.ReactNode;
	miniLayoutProps?: Omit<
		PageContainerProps,
		| "show"
		| "overlay"
		| "zIndex"
		| "duration"
		| "onBeforeLeave"
		| "onClickOverlay"
	>;
	layoutProps?: Omit<FloatLayoutProps, "isOpened" | "onClose">;
}

export const Popup: React.FC<PopupProps> = ({
	open,
	onClose,
	children,
	layoutProps = {},
	miniLayoutProps = {},
}) => {
	const floatLayoutRef = useRef<FloatLayout>(null);

	return isWeb ? (
		<FloatLayout isOpened={open} onClose={onClose} {...layoutProps}>
			{children}
		</FloatLayout>
	) : (
		<PageContainer
			show={open}
			overlay={false}
			zIndex={800}
			duration={150}
			onBeforeLeave={floatLayoutRef.current?.close}
			{...miniLayoutProps}
		>
			<FloatLayout
				ref={floatLayoutRef}
				isOpened={open}
				onClose={onClose}
				{...layoutProps}
			>
				{children}
			</FloatLayout>
		</PageContainer>
	);
};

export default Popup;
