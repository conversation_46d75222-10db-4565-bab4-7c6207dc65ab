import React, { useState } from "react";
import { View } from "@tarojs/components";
import NavBar from "@/view/component/nav-bar/nav-bar.component";
import PageContainer, {
	PageContainerRef,
} from "@/view/component/page-container/page-container.component";
import styles from "./page-with-nav.module.scss";

interface PageWithNavProps {
	title?: string;
	showNavBar?: boolean;
	onBack?: () => void;
	className?: string;
	containerClassName?: string;
	children: React.ReactNode;
	containerRef?: React.RefObject<PageContainerRef>;
	onHeightChange?: (height: number) => void;
}

export type { PageContainerRef };
const PageWithNav: React.FC<PageWithNavProps> = ({
	title = "",
	showNavBar = true,
	onBack,
	className = "",
	containerClassName = "",
	children,
	containerRef,
	onHeightChange,
}) => {
	const [navBarHeight, setNavBarHeight] = useState(42);
	const containerStyle = showNavBar
		? {
				height: `calc(100vh - ${navBarHeight}px)`,
				marginTop: `${navBarHeight}px`,
		  }
		: {};

	const handleNavBarHeightChange = (height: number) => {
		setNavBarHeight(height);
		onHeightChange?.(height);
	};

	return (
		<View className={`${styles.pageWithNav} ${className}`}>
			{showNavBar && (
				<NavBar
					text={title}
					onBack={onBack}
					navBarHeight={navBarHeight}
					onHeightChange={handleNavBarHeightChange}
				/>
			)}
			<PageContainer
				className={`${styles.container} ${containerClassName}`}
				ref={containerRef}
				style={containerStyle}
			>
				{children}
			</PageContainer>
		</View>
	);
};

export default PageWithNav;
