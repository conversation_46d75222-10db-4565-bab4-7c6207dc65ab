import React, { useState } from "react";
import { View, Text } from "@tarojs/components";
import styles from "./custom-picker.module.scss";

interface CustomPickerProps {
	visible: boolean;
	title?: string;
	options: { code: string | number; name: string }[];
	value?: number;
	onCancel: () => void;
	onConfirm: (index: number) => void;
}

const CustomPicker: React.FC<CustomPickerProps> = ({
	visible,
	title = "请选择",
	options,
	value = 0,
	onCancel,
	onConfirm,
}) => {
	const [selectedValue, setSelectedValue] = useState(value);

	if (!visible) return null;

	return (
		<View className={styles.pickerOverlay}>
			<View className={styles.pickerContainer}>
				<View className={styles.pickerHeader}>
					<Text className={styles.cancelBtn} onClick={onCancel}>
						取消
					</Text>
					<Text className={styles.pickerTitle}>{title}</Text>
					<Text
						className={styles.confirmBtn}
						onClick={() => onConfirm(selectedValue)}
					>
						确定
					</Text>
				</View>
				<View className={styles.pickerContent}>
					{options.map((item, index) => (
						<View
							key={index}
							className={`${styles.pickerItem} ${
								selectedValue === item.code
									? styles.pickerItemSelected
									: ""
							}`}
							onClick={() => setSelectedValue(item.code)}
						>
							<Text>{item.name}</Text>
						</View>
					))}
				</View>
			</View>
		</View>
	);
};

export default CustomPicker;
