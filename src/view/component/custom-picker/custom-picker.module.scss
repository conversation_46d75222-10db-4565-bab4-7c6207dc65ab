@use "@/theme.scss" as t;

.pickerOverlay {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1000;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	background-color: rgba(0, 0, 0, 0.5);
}

.pickerContainer {
	overflow: hidden;
	background-color: #fff;
	border-radius: 16px 16px 0 0;
}

.pickerHeader {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 16px;
}

.pickerTitle {
	color: #333;
	font-weight: 500;
	font-size: 16px;
}

.cancelBtn {
	color: #333;
	font-size: 16px;
}

.confirmBtn {
	color: #ffcd33;
	font-weight: 500;
	font-size: 16px;
}

.pickerContent {
	padding: 20px 0;
}

.pickerItem {
	padding: 12px 0px;
	color: #646566;
	font-size: 15px;
	text-align: center;
	border-bottom: 1px solid t.$border-color;

	&:last-child {
		border-bottom: none;
	}
}

.pickerItemSelected {
	color: #323233;
	font-weight: 500;
	font-size: 18px;
}
