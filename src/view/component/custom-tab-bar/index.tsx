import { Text, View } from "@tarojs/components";
import { useState } from "react";
import styles from "./index.module.scss";
import { Image } from "@tarojs/components";
import homePng from "@/assets/image/common/home.png";
import xiaoxiPng from "@/assets/image/common/xiaoxi.png";
import mendian from "@/assets/image/common/mendian.png";
import homeSelectedPng from "@/assets/image/common/home-selected.png";
import xiaoxiSelectedPng from "@/assets/image/common/xiaoxi-selected.png";
import mendianSelectedPng from "@/assets/image/common/mendian-selected.png";
import selectorPng from "@/assets/image/common/selector.png";

interface Props {
	onClick?: (path: string, i: number) => void;
}

const CustomTabBar = (props: Props) => {
	const tabbar = {
		color: "#B9BBBC", // 未选中字体颜色
		selectedColor: "#FDD244", // 选择字体颜色
		iconColor: "#ccc", // 图标默认颜色
		selectedIconColor: "#fff", // 选中图标颜色
		backgroundColor: "#FFFFFF", // 背景颜色
		selectedBackground: "#FDD244",
		list: [
			{
				text: "首页", // 底部文字
				iconPath: homePng, // 未选择icon图标
				selectedIconPath: homeSelectedPng, // 选中icon图标
				pagePath: "home", 
			},
			{
				text: "消息",
				iconPath: xiaoxiPng,
				selectedIconPath: xiaoxiSelectedPng,
				pagePath: "package",
			},
			{
				text: "店铺",
				iconPath: mendian,
				selectedIconPath: mendianSelectedPng,
				pagePath: "me",
			},
		],
	};

	const [current, setCurrent] = useState(0);

	const onTabbarChange = (path: string, i: number) => {
		console.log(i);
		
		if (current === i) return;
		setCurrent(i);
		props.onClick?.(path, i);
	};

	return (
		<View className={styles.container}>
			{tabbar.list.map((item, index) => (
				<View
					key={index}
					className={styles.item}
					onClick={() => onTabbarChange(item.pagePath, index)}
				>
					<Image
						src={
							index === current
								? item.selectedIconPath
								: item.iconPath
						}
						className={styles.icon}
					/>
					<Text className={styles.text}>{item.text}</Text>
				</View>
			))}
			<Image
				src={selectorPng}
				style={{
					left: `calc(${(100 / 6) * 2 * current + 100 / 6}%)`,
					transform: "translateX(-50%)",
				}}
				className={styles.indicator}
			></Image>
		</View>
	);
};

export default CustomTabBar;
