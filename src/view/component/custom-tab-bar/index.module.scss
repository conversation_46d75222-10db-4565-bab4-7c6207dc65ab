.container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  height: 75px;
  background: #ffffff;
  border-radius: 18px 18px 0 0;
  box-shadow: 0px -1px 7px 0px rgba(0, 0, 0, 0.09);
  position: relative;
  z-index: 9999999;
}

.item {
  display: flex;
  // width: 187.5px;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 24px;
  height: 24px;
}

.text {
  color: #b9bbbc;
  font-weight: bold;
  font-size: 10px;
}

.indicator {
  position: absolute;
  top: 0;
  width: 41px;
  height: 15px;
  transition: left 0.5s;
}