.overlay {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.5);
}

.actionSheet {
	position: absolute;
	right: 0px;
	bottom: 0px;
	left: 0px;
	width: 100%;
	overflow: hidden;

	.options {
		margin-right: 8px;
		margin-left: 8px;
		text-align: center;
		background: #fff;
		border-radius: 8px;
		overflow: hidden;

		.option {
			padding: 12px 12px;
			color: #333333;
			font-size: 17px;
			line-height: 24px;
			border-bottom: 1px solid #f2f2f2;
			cursor: pointer;

			&:last-child {
				border-bottom: none;
			}

			// &:active {
			// 	background-color: #0000000a;
			// }
		}
	}

	.cancel {
		margin: 12px 8px 6px 8px;
		padding: 12px;
		color: #999999;
		font-size: 17px;
		text-align: center;
		background-color: #fff;
		border-radius: 8px;
		cursor: pointer;
	}
}