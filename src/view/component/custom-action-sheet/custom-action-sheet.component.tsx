import React from "react";
import { View, Text } from "@tarojs/components";
import styles from "./custom-action-sheet.module.scss";
import useWindowArea from "@/hook/windowArea";

interface CustomActionSheetProps {
	visible: boolean;
	options: string[];
	onSelect: (index: number) => void;
	onCancel: () => void;
}

const CustomActionSheet: React.FC<CustomActionSheetProps> = ({
	visible,
	options,
	onSelect,
	onCancel,
}) => {
	if (!visible) return null;

	const { bottomArea } = useWindowArea();

	return (
		<View className={styles.overlay}>
			<View className={styles.actionSheet} style={{ bottom: bottomArea }}>
				<View className={styles.options}>
					{options.map((option, index) => (
						<View
							key={index}
							className={styles.option}
							onClick={() => onSelect(index)}
						>
							<Text>{option}</Text>
						</View>
					))}
				</View>

				<View className={styles.cancel} onClick={onCancel}>
					<Text>取消</Text>
				</View>
			</View>
		</View>
	);
};

export default CustomActionSheet;
