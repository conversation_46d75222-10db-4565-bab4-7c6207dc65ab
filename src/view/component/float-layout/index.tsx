import classNames from "classnames";
import PropTypes, { InferProps } from "prop-types";
import React from "react";
import { ScrollView, Text, View } from "@tarojs/components";
import { CommonEvent } from "@tarojs/components/types/common";
import Taro from "@tarojs/taro";
import { CommonEventFunction } from "@tarojs/components/types/common";
import { CSSProperties, ReactNode } from "react";
import Portal from "../portal";

interface Component {
	className?: string;
	style?: CSSProperties;
	customStyle?: string | CSSProperties;
	children?: ReactNode;
}

export interface FloatLayoutProps extends Component {
	/**
	 * 控制是否出现在页面上
	 * @default false
	 */
	isOpened: boolean;
	/**
	 * 容器样式
	 */
	containerClassName?: string;
	/**
	 * 元素的标题
	 */
	title?: string;
	/**
	 * 是否垂直滚动
	 * @default true
	 */
	scrollY?: boolean;
	/**
	 * 是否水平滚动
	 * @default false
	 */
	scrollX?: boolean;
	/**
	 * 设置竖向滚动条位置
	 */
	scrollTop?: number;
	/**
	 * 设置横向滚动条位置
	 */
	scrollLeft?: number;
	/**
	 * 距顶部/左边多远时，触发 scrolltolower 事件
	 */
	upperThreshold?: number;
	/**
	 * 距底部/右边多远时，触发 scrolltolower 事件
	 */
	lowerThreshold?: number;
	/**
	 * 在设置滚动条位置时使用动画过渡
	 * @default false
	 */
	scrollWithAnimation?: boolean;
	/**
	 * 元素被关闭时候触发的事件
	 */
	onClose?: CommonEventFunction;
	/**
	 * 滚动时触发的事件
	 */
	onScroll?: CommonEventFunction;
	/**
	 * 滚动到顶部/左边，会触发 onScrollToUpper 事件
	 */
	onScrollToUpper?: CommonEventFunction;
	/**
	 * 滚动到底部/右边，会触发 onScrollToLower 事件
	 */
	onScrollToLower?: CommonEventFunction;
}

interface FloatLayoutState {
	_isOpened: boolean;
	_childIsOpened: boolean;
	translateY?: any;
}

let scrollTop = 0;

const ENV = Taro.getEnv();

function handleTouchScroll(flag: any): void {
	if (ENV !== Taro.ENV_TYPE.WEB) {
		return;
	}
	if (flag) {
		scrollTop = document.documentElement.scrollTop;

		// 使body脱离文档流
		document.body.classList.add("at-frozen");

		// 把脱离文档流的body拉上去！否则页面会回到顶部！
		document.body.style.top = `${-scrollTop}px`;
	} else {
		document.body.style.top = "";
		document.body.classList.remove("at-frozen");

		document.documentElement.scrollTop = scrollTop;
	}
}

export default class FloatLayout extends React.Component<
	FloatLayoutProps,
	FloatLayoutState
> {
	public static defaultProps: FloatLayoutProps;
	public static propTypes: InferProps<FloatLayoutProps>;

	public constructor(props: FloatLayoutProps) {
		super(props);

		const { isOpened } = props;
		this.state = {
			_isOpened: isOpened,
			_childIsOpened: isOpened,
		};
	}

	public UNSAFE_componentWillReceiveProps(nextProps: FloatLayoutProps): void {
		const { isOpened } = nextProps;

		if (this.props.isOpened !== isOpened) {
			handleTouchScroll(isOpened);
		}

		if (isOpened !== this.state._isOpened) {
			this.setState({
				_isOpened: isOpened,
				_childIsOpened: isOpened,
			});
		}
	}

	private handleClose = (e): void => {
		if (typeof this.props.onClose === "function") {
			this.props.onClose(e);
		}
	};

	public close = (e): void => {
		this.setState(
			{
				_isOpened: false,
			},
			() => this.handleClose(e)
		);
		setTimeout(() => {
			this.setState({
				_childIsOpened: false,
			});
		}, 150);
	};

	private handleTouchMove = (e: CommonEvent): void => {
		e.stopPropagation();
	};

	public render(): JSX.Element {
		const { _isOpened, _childIsOpened } = this.state;
		const {
			title,

			scrollY,
			scrollX,
			scrollTop,
			scrollLeft,
			upperThreshold,
			lowerThreshold,
			scrollWithAnimation,
		} = this.props;

		const rootClass = classNames(
			"z-[99999999]",
			"at-float-layout",
			{
				"at-float-layout--active": _isOpened,
			},
			this.props.className
		);

		const containerClass = classNames(
			"at-float-layout__container layout",
			this.props.containerClassName
		);

		return (
			<Portal>
				<View className={rootClass} onTouchMove={this.handleTouchMove}>
					<View
						onClick={this.close}
						className="at-float-layout__overlay"
					/>
					<View className={containerClass}>
						{title ? (
							<View className="layout-header">
								<Text className="layout-header__title">
									{title}
								</Text>
								<View
									className="layout-header__btn-close"
									onClick={this.close}
								/>
							</View>
						) : null}
						<View className="layout-body">
							<ScrollView
								scrollY={scrollY}
								scrollX={scrollX}
								scrollTop={scrollTop}
								scrollLeft={scrollLeft}
								upperThreshold={upperThreshold}
								lowerThreshold={lowerThreshold}
								scrollWithAnimation={scrollWithAnimation}
								onScroll={this.props.onScroll}
								onScrollToLower={this.props.onScrollToLower}
								onScrollToUpper={this.props.onScrollToUpper}
								className="layout-body__content"
							>
								{_childIsOpened && this.props.children}
							</ScrollView>
						</View>
					</View>
				</View>
			</Portal>
		);
	}
}

FloatLayout.defaultProps = {
	title: "",
	isOpened: false,

	scrollY: true,
	scrollX: false,
	scrollWithAnimation: false,
};

FloatLayout.propTypes = {
	title: PropTypes.string,
	isOpened: PropTypes.bool,
	scrollY: PropTypes.bool,
	scrollX: PropTypes.bool,
	scrollTop: PropTypes.number,
	scrollLeft: PropTypes.number,
	upperThreshold: PropTypes.number,
	lowerThreshold: PropTypes.number,
	scrollWithAnimation: PropTypes.bool,
	onClose: PropTypes.func,
	onScroll: PropTypes.func,
	onScrollToLower: PropTypes.func,
	onScrollToUpper: PropTypes.func,
};
