.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.content {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  height: 75vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 17px;
  font-weight: 500;
  color: #333;
}

.close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list {
  flex: 1;
  overflow-y: auto;
  padding: 12px 16px;
}

.item {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.imageWrapper {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.image {
  width: 100%;
  height: 100%;
}

.placeholder {
  width: 100%;
  height: 100%;
  background-color: #eee;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 60px;
}

.name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
}

.duration {
  font-size: 12px;
  color: #999;
}

.price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.originalPrice {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.discountPrice {
  font-size: 14px;
  color: #ff9500;
  font-weight: 500;
}

.selected {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ff9500;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-left: 8px;
  
  .iconfont {
    color: #fff;
    font-size: 12px;
  }
}

.footer {
  padding: 16px;
  border-top: 1px solid #eee;
}

.confirmBtn {
  background-color: #ff9500;
  color: #fff;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
} 