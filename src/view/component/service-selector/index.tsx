import { View, Text, Image } from "@tarojs/components";
import { FC } from "react";
import styles from './service-selector.module.scss';

// 项目类型定义
export interface ServiceItem {
  id: string; // templateProjectId
  projectName?: string;
  projectDuration?: number;
  projectOrigPrice?: number;
  projectDiscPrice?: number;
  image?: string;
}

// Mock数据 - 暂时保留，但实际应使用API数据
// export const mockServices: ServiceItem[] = [ ... ];

// 分类数据 - 暂时保留，但实际应使用API数据
// export const serviceCategories = ['美甲服务', '皮肤护理'];

interface ServiceSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (service: ServiceItem) => void;
  selectedServices: ServiceItem[];
  services: ServiceItem[]; // 从外部传入实际的项目列表
  onConfirm?: () => void;
  // categories: string[]; // 从外部传入实际的分类列表
}

const ServiceSelector: FC<ServiceSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  selectedServices,
  services, // 使用传入的 services
  onConfirm,
  // categories // 使用传入的 categories
}) => {
  // const [activeCategory, setActiveCategory] = useState(categories?.[0] || '');

  if (!visible) return null;

  // const filteredServices = services.filter(service => service.category === activeCategory);

  return (
    <View className={styles.mask}>
      <View className={styles.content} onClick={e => e.stopPropagation()}>
        {/* 头部 */}
        <View className={styles.header}>
          <Text className={styles.title}>选择项目</Text>
          <View className={styles.close} onClick={onClose}>
            <Text className="iconfont icon-close" />
          </View>
        </View>

        {/* 项目列表 */}
        <View className={styles.list}>
          {services.map((service) => {
            const isSelected = selectedServices.some(item => item.id === service.id);
            const showDiscount = service.projectDiscPrice !== undefined && 
                               service.projectOrigPrice !== undefined && 
                               service.projectDiscPrice < service.projectOrigPrice;
            
            return (
              <View
                key={service.id}
                className={styles.item}
                onClick={() => onSelect(service)}
              >
                {/* 图片区域 */}
                <View className={styles.imageWrapper}>
                  {service.image ? (
                    <Image src={service.image} className={styles.image} mode="aspectFill" />
                  ) : (
                    <View className={styles.placeholder} />
                  )}
                </View>

                {/* 信息区域 */}
                <View className={styles.info}>
                  <Text className={styles.name}>{service.projectName}</Text>
                  <View className={styles.bottom}>
                    {service.projectDuration && (
                      <Text className={styles.duration}>{service.projectDuration}分钟</Text>
                    )}
                    <View className={styles.price}>
                      {showDiscount ? (
                        <>
                          <Text className={styles.originalPrice}>¥{service.projectOrigPrice}</Text>
                          <Text className={styles.discountPrice}>¥{service.projectDiscPrice}</Text>
                        </>
                      ) : (
                        service.projectOrigPrice !== undefined && (
                          <Text className={styles.discountPrice}>¥{service.projectOrigPrice}</Text>
                        )
                      )}
                    </View>
                  </View>
                </View>

                {/* 选中状态 */}
                {isSelected && (
                  <View className={styles.selected}>
                    <Text className="iconfont icon-check" />
                  </View>
                )}
              </View>
            );
          })}
        </View>

        <View className={styles.footer}>
          <View className={styles.confirmBtn} onClick={onConfirm || onClose}>
            确认
          </View>
        </View>
      </View>
    </View>
  );
};

export default ServiceSelector; 
