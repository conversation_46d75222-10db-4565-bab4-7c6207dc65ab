.container {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	position: absolute;
	z-index: 9999;
	width: 100%;
	height: 48px;
	top: 0;
	left: 0;
	color: #FFFFFF;
	font-size: 20px;
	transform: translate(0, -62px);
	transition: 0.5s;
}

.open {
	transform: translate(0, 0);
}

.info {
	background: rgb(120, 164, 250);
}

.success {
	background: rgb(19, 206, 102);
}

.warning {
	background: rgb(243, 202, 68);
}

.error {
	background: rgb(255, 73, 73);
}
