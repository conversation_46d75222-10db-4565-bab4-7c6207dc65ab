import React, { useState, useEffect, useMemo } from "react";
import { View, Text, ScrollView, Input } from "@tarojs/components";
import { AtIcon } from "taro-ui";
import styles from "./custom-cascade-select.module.scss";

interface CascadeOption {
	name: string;
	code: string;
	children?: CascadeOption[];
}

interface CascadeSelectProps {
	options: CascadeOption[];
	value?: string[];
	onChange?: (value: string[], labels: string[]) => void;
	title?: string;
	visible: boolean;
	onClose: () => void;
}

const CascadeSelect: React.FC<CascadeSelectProps> = ({
	options,
	value = [],
	onChange,
	title = "请选择",
	visible,
	onClose,
}) => {
	// 选中的值和标签
	const [selectedValues, setSelectedValues] = useState<string[]>(value);
	const [selectedLabels, setSelectedLabels] = useState<string[]>([]);

	// 临时存储选择的值和标签
	const [tempValues, setTempValues] = useState<string[]>(value);
	const [tempLabels, setTempLabels] = useState<string[]>([]);

	// 搜索相关
	const [searchText, setSearchText] = useState<string>("");
	const [searchResults, setSearchResults] = useState<CascadeOption[]>([]);
	const [isSearching, setIsSearching] = useState<boolean>(false);

	// 计算最大层级深度
	const maxDepth = useMemo(() => {
		const getDepth = (opts: CascadeOption[], currentDepth = 1): number => {
			let maxChildDepth = currentDepth;
			for (const opt of opts) {
				if (opt.children && opt.children.length > 0) {
					const childDepth = getDepth(opt.children, currentDepth + 1);
					maxChildDepth = Math.max(maxChildDepth, childDepth);
				}
			}
			return maxChildDepth;
		};
		return getDepth(options);
	}, [options]);

	// 动态生成各级选项
	const levelOptions = useMemo(() => {
		const result: CascadeOption[][] = Array(maxDepth)
			.fill([])
			.map(() => []);
		result[0] = options;

		// 如果有已选值，根据已选值填充各级选项
		if (selectedValues.length > 0) {
			for (let i = 0; i < selectedValues.length; i++) {
				if (i >= maxDepth - 1) break;

				const parentOptions = i === 0 ? options : result[i];
				const selectedOption = parentOptions.find(
					(opt) => opt.code === selectedValues[i]
				);

				if (selectedOption && selectedOption.children) {
					result[i + 1] = selectedOption.children;
				}
			}
		}
		// 如果没有已选值，默认展示第一条数据的所有列
		else if (options.length > 0) {
			let currentOption = options[0];
			let level = 1;

			while (
				currentOption.children &&
				currentOption.children.length > 0 &&
				level < maxDepth
			) {
				result[level] = currentOption.children;
				currentOption = currentOption.children[0];
				level++;
			}
		}

		return result;
	}, [options, selectedValues, maxDepth]);

	// 初始化或更新选中值
	useEffect(() => {
		if (value && value.length > 0) {
			setSelectedValues(value);
			setTempValues(value);
			updateSelectedLabels(value);
		}
		// 如果没有初始值且有选项数据，默认选中第一条数据
		else if (options.length > 0 && visible) {
			const defaultValues: string[] = [];
			const defaultLabels: string[] = [];

			let currentOption = options[0];
			defaultValues.push(currentOption.code);
			defaultLabels.push(currentOption.name);

			while (
				currentOption.children &&
				currentOption.children.length > 0
			) {
				currentOption = currentOption.children[0];
				defaultValues.push(currentOption.code);
				defaultLabels.push(currentOption.name);
			}

			setSelectedValues(defaultValues);
			setTempValues(defaultValues);
			setSelectedLabels(defaultLabels);
			setTempLabels(defaultLabels);
		}
	}, [value, options, visible]);

	// 根据选中的值更新标签
	const updateSelectedLabels = (values: string[]) => {
		const labels: string[] = [];
		let currentOpts = options;

		for (let i = 0; i < values.length; i++) {
			const option = currentOpts.find((opt) => opt.code === values[i]);
			if (option) {
				labels.push(option.name);
				if (option.children) {
					currentOpts = option.children;
				} else {
					break;
				}
			}
		}

		setSelectedLabels(labels);
	};

	// 处理搜索
	const handleSearch = (text: string) => {
		setSearchText(text);
		if (!text) {
			setIsSearching(false);
			setSearchResults([]);
			return;
		}

		setIsSearching(true);

		// 深度搜索所有级别的选项
		const results: CascadeOption[] = [];

		const searchInOptions = (
			opts: CascadeOption[],
			path: CascadeOption[] = []
		) => {
			for (const opt of opts) {
				if (opt.name.toLowerCase().includes(text.toLowerCase())) {
					// 将完整路径添加到结果中
					results.push({
						...opt,
						name: path
							.map((p) => p.name)
							.concat(opt.name)
							.join(" > "),
						path: path.map((p) => p.code).concat(opt.code),
					} as any);
				}

				if (opt.children && opt.children.length > 0) {
					searchInOptions(opt.children, [...path, opt]);
				}
			}
		};

		searchInOptions(options);
		setSearchResults(results);
	};

	// 处理选项点击
	const handleOptionClick = (option: CascadeOption, level: number) => {
		// 更新临时选中值和标签
		const newValues = [...tempValues.slice(0, level), option.code];
		const newLabels = [...tempLabels.slice(0, level), option.name];

		setTempValues(newValues);
		setTempLabels(newLabels);

		// 如果没有子选项或已经是最后一级，不再自动提交
		// 只在点击确定按钮时才提交
	};

	// 处理搜索结果点击
	const handleSearchResultClick = (option: any) => {
		if (option.path && option.path.length > 0) {
			const values = option.path;

			// 更新标签
			const labels: string[] = [];
			let currentOpts = options;

			for (let i = 0; i < values.length; i++) {
				const opt = currentOpts.find((o) => o.code === values[i]);
				if (opt) {
					labels.push(opt.name);
					if (opt.children) {
						currentOpts = opt.children;
					}
				}
			}

			setTempValues(values);
			setTempLabels(labels);

			// 完成选择并提交
			if (onChange) {
				onChange(values, labels);
			}

			// 清除搜索状态
			setSearchText("");
			setIsSearching(false);

			// 关闭选择器
			onClose();
		}
	};

	// 处理确认按钮点击
	const handleConfirm = () => {
		if (onChange && tempValues.length > 0) {
			// 将临时选择的值提交给父组件
			onChange(tempValues, tempLabels);
			// 同步更新组件内部的选中状态
			setSelectedValues(tempValues);
			setSelectedLabels(tempLabels);
		}
		onClose();
	};

	// 处理取消按钮点击
	const handleCancel = () => {
		// 取消时恢复到原来的选中状态
		setTempValues(selectedValues);
		setTempLabels(selectedLabels);
		onClose();
	};

	// 每次visible变化时重置搜索状态和临时选择状态
	useEffect(() => {
		if (visible) {
			setSearchText("");
			setIsSearching(false);
			// 打开时，将临时选择状态设置为当前选中状态
			setTempValues(selectedValues);
			setTempLabels(selectedLabels);
		}
	}, [visible, selectedValues, selectedLabels]);

	// 每次visible变化时重置搜索状态
	useEffect(() => {
		if (visible) {
			setSearchText("");
			setIsSearching(false);
		}
	}, [visible]);

	if (!visible) return null;

	// 计算每列宽度
	const columnWidth = `${100 / Math.min(maxDepth, 3)}%`;

	return (
		<View className={styles.cascadeContainer}>
			<View className={styles.mask} />
			<View className={styles.content}>
				<View className={styles.header}>
					<Text className={styles.cancelBtn} onClick={handleCancel}>
						取消
					</Text>
					<Text className={styles.title}>{title}</Text>
					<Text className={styles.confirmBtn} onClick={handleConfirm}>
						确定
					</Text>
				</View>

				<View className={styles.searchContainer}>
					<View className={styles.searchIcon}>
						<AtIcon value="search" size="14" color="#999" />
					</View>
					<Input
						className={styles.searchInput}
						value={searchText}
						placeholder="搜索分类"
						onInput={(e) => handleSearch(e.detail.value)}
					/>
					{searchText && (
						<View
							className={styles.clearIcon}
							onClick={() => handleSearch("")}
						>
							<AtIcon
								value="close-circle"
								size="14"
								color="#999"
							/>
						</View>
					)}
				</View>

				{isSearching ? (
					// 搜索结果列表
					<ScrollView
						className={styles.searchResultsContainer}
						scrollY
					>
						{searchResults.length > 0 ? (
							searchResults.map((option, index) => (
								<View
									key={index}
									className={styles.searchResultItem}
									onClick={() =>
										handleSearchResultClick(option)
									}
								>
									<Text>{option.name}</Text>
								</View>
							))
						) : (
							<View className={styles.emptyTip}>
								<Text>没有找到相关选项</Text>
							</View>
						)}
					</ScrollView>
				) : (
					// 动态级联选择器
					<View className={styles.columnsContainer}>
						{levelOptions
							.slice(0, Math.min(maxDepth, 3))
							.map((options, level) => (
								<ScrollView
									key={level}
									className={styles.column}
									scrollY
									style={{ width: columnWidth }}
								>
									{options.map((option, index) => (
										<View
											key={index}
											className={`${styles.option} ${
												tempValues[level] ===
												option.code
													? styles.selectedOption
													: ""
											}`}
											onClick={() =>
												handleOptionClick(option, level)
											}
										>
											<Text>{option.name}</Text>
											{tempValues[level] ===
												option.code && (
												<AtIcon
													value="check"
													size="16"
													color="#FFCD33"
												/>
											)}
										</View>
									))}
								</ScrollView>
							))}
					</View>
				)}
			</View>
		</View>
	);
};

export default CascadeSelect;
