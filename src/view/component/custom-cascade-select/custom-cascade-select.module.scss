.cascadeContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.cancelBtn, .confirmBtn {
  font-size: 14px;
  padding: 4px 8px;
}

.cancelBtn {
  color: #999;
}

.confirmBtn {
  color: #FFCD33;
}

.searchContainer {
  display: flex;
  align-items: center;
  margin: 12px 16px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 16px;
}

.searchIcon {
  margin-right: 8px;
}

.searchInput {
  flex: 1;
  height: 24px;
  font-size: 14px;
  background-color: transparent;
  border: none;
}

.clearIcon {
  margin-left: 8px;
}

/* 列容器 */
.columnsContainer {
  display: flex;
  flex: 1;
  height: 50vh;
}

/* 单列样式 */
.column {
  height: 100%;
  border-right: 1px solid #f0f0f0;
  box-sizing: border-box;
}

.column:last-child {
  border-right: none;
}

/* 选项样式 */
.option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

.selectedOption {
  color: #FFCD33;
  background-color: rgba(255, 205, 51, 0.1);
}

/* 搜索结果容器 */
.searchResultsContainer {
  flex: 1;
  max-height: 50vh;
}

/* 搜索结果项 */
.searchResultItem {
  padding: 12px 16px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

.emptyTip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  color: #999;
  font-size: 14px;
}