import { View, Text, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState, useEffect } from 'react';
import checkedImg from '@/assets/image/checked.png';

interface Project {
  projectId: number;
  projectName: string;
}

interface Props {
  visible: boolean;
  projectList: Project[];
  selectedIds: number[];
  onClose: () => void;
  onConfirm: (selectedIds: number[]) => void;
}

const ProjectMultiSelectModal = ({
  visible,
  projectList,
  selectedIds,
  onClose,
  onConfirm,
}: Props) => {
  const [localSelected, setLocalSelected] = useState<number[]>(selectedIds);

  useEffect(() => {
    setLocalSelected(selectedIds);
  }, [selectedIds, visible]);

  const toggle = (id: number) => {
    setLocalSelected(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  if (!visible) return null;

  return (
    <View
      className="fixed left-0 bottom-0 w-full z-50 flex flex-col items-center justify-end bg-black bg-opacity-40"
      style={{ height: '100vh' }}
    >
      <View
        className="w-full bg-white rounded-t-2xl shadow-lg flex flex-col"
        style={{
          maxHeight: '70vh',
          minHeight: '40vh',
          boxShadow: '0 -8px 32px rgba(0,0,0,0.10)',
        }}
      >
        {/* 标题 */}
        <View className="px-6 pt-5 pb-3 border-b border-[#F5F5F5]">
          <Text className="block text-lg font-bold text-[#222]">选择赠送项目</Text>
        </View>
        {/* 内容区（滚动） */}
        <View
          className="flex-1 overflow-y-auto px-2"
          style={{ minHeight: 0 }}
        >
          {projectList.length === 0 ? (
            <View className="flex items-center justify-center py-10 text-[#999] text-base">
              暂无可选项目
            </View>
          ) : (
            <View>
              {projectList.map(item => (
                <View
                  key={item.projectId}
                  className="flex items-center px-4 py-3 cursor-pointer border-b border-[#F5F5F5]"
                  style={{ minHeight: 48 }}
                  onClick={() => toggle(item.projectId!)}
                >
                  <Text
                    className={`flex-1 text-base transition-colors duration-150 ${
                      localSelected.includes(item.projectId)
                        ? 'text-[#FFB300] font-bold'
                        : 'text-[#222]'
                    }`}
                  >
                    {item.projectName}
                  </Text>
                  <View className="flex items-center justify-center" style={{ width: 28, height: 28 }}>
                    {localSelected.includes(item.projectId) && (
                      <Image src={checkedImg} className="w-5 h-5" mode="aspectFit" />
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
        {/* 操作栏（固定底部） */}
        <View className="flex gap-4 px-6 py-4 bg-white border-t border-[#F5F5F5]">
          <View
            className="flex-1 h-11 flex items-center justify-center rounded-full bg-[#F0F0F0] text-[#999] text-base active:opacity-70"
            onClick={onClose}
          >
            取消
          </View>
          <View
            className="flex-1 h-11 flex items-center justify-center rounded-full bg-[#FFB300] text-white text-base font-bold active:opacity-80"
            onClick={() => onConfirm(localSelected)}
          >
            确定
          </View>
        </View>
      </View>
    </View>
  );
};

export default ProjectMultiSelectModal; 