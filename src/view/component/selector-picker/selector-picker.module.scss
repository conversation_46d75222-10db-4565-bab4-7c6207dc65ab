.container {
	width: 100%;
	transition: 0.1s;

	> .content {
		display: flex;
		flex-direction: column;
		gap: 8px;
		justify-content: center;
		width: calc(100% - 24px - 4px - 24px);
		margin-left: 24px;
		padding-right: 24px;
		padding-left: 4px;
		border-bottom: 1px solid #f4f6fa;

		> .mainLine {
			display: flex;
			flex: 1;
			flex-direction: row;
			align-items: center;

			> .title {
				width: 150px;
				color: #1a1a1a;
				font-size: 30px;
			}

			> .picker {
				flex: 1;
				width: 100%;

				.pickerContent {
					width: 100%;

					> .valueText {
						color: #1a1a1a;
						font-size: 28px;
					}

					> .placeholderText {
						color: #9da3b2;
						font-size: 28px;
					}
				}
			}

			.arrow {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 8px;
				color: #4c5666;
			}
		}

		> .subLine {
			// color: #F55858;
			color: #9da3b2;
			font-size: 22px;
		}
	}
}

.contentPressed {
	background: rgb(240, 240, 240);
}
