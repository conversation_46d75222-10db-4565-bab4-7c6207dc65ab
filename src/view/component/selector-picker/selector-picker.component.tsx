import React, { useEffect, useState } from "react";
import { Picker, Text, View, ViewProps } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { AtIcon } from "taro-ui";
import styles from "./selector-picker.module.scss";

interface PickerItemPropsType extends ViewProps {
	heightSize?: "normal" | "small";
	title?: string;
	titleWidthFix?: boolean;
	titleFontWeight?: string;
	showValue?: string;
	placeholder?: string;
	noBorder?: boolean;
	range: {
		code: string | number;
		name: string;
	}[];
	value: any;
	onChange?: (e: any) => void;
}

const Index: React.ComponentType<PickerItemPropsType> = ({
	value,
	onChange,
	range = [],
	className,
	noBorder,
	titleWidthFix,
	titleFontWeight,
	title,
	heightSize,
	placeholder,
}) => {
	const [{ selectedValue, selectedName }, setSelectedData] = useState<any>({
		selectedValue: null,
		selectedName: null,
	});

	useEffect(() => {
		const index: number = range.findIndex((item) => item?.code === value);

		setSelectedData({
			selectedValue: index,
			selectedName: range?.[index]?.name,
		});
	}, [value, range]);

	const getContainerPadding = (heightSize?: "normal" | "small"): string => {
		switch (heightSize) {
			case "normal":
				return Taro.pxTransform(36);
			case "small":
				return Taro.pxTransform(24);
			default:
				return Taro.pxTransform(36);
		}
	};

	const pickerChange = (e) => {
		const index = e.detail.value;
		onChange && onChange(range?.[index]?.code);
	};

	return (
		<View
			className={`${styles.container} ${className ? className : ""}`}
			hoverClass={styles.contentPressed}
			hoverStartTime={0}
			hoverStayTime={50}
		>
			<View
				className={styles.content}
				style={{
					borderBottomColor: noBorder ? "rgba(0,0,0,0)" : "#F4F6FA",
				}}
			>
				<View className={styles.mainLine}>
					<Text
						className={styles.title}
						style={{
							width: titleWidthFix
								? Taro.pxTransform(150)
								: "auto",
							fontWeight: titleFontWeight,
						}}
					>
						{title}
					</Text>
					<Picker
						className={styles.picker}
						range={range}
						range-key="name"
						value={selectedValue}
						mode="selector"
						onChange={pickerChange}
					>
						<View
							className={styles.pickerContent}
							style={{
								paddingTop: getContainerPadding(heightSize),
								paddingBottom: getContainerPadding(heightSize),
							}}
						>
							{selectedName ? (
								<Text className={styles.valueText}>
									{selectedName}
								</Text>
							) : (
								<Text className={styles.placeholderText}>
									{placeholder}
								</Text>
							)}
						</View>
					</Picker>
					<View className={styles.arrow}>
						<AtIcon size="16" value="chevron-right"></AtIcon>
					</View>
				</View>
			</View>
		</View>
	);
};

export default Index;
