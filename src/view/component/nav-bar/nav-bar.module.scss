.customNavBar {
	// position: fixed;
	top: 0;
	right: 0;
	left: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	box-sizing: border-box;
	height: 42px;
	padding: 8px 12px;
	background-color: #fff;

	:global {
		.at-icon-chevron-left {
			color: rgba(0, 0, 0, 0.9);
			font-size: 16px;
		}
	}
}

.backButton {
	position: absolute;
	left: 12px;
}

.title {
	color: #1f1f1f;
	font-weight: 400;
	font-size: 16px;
	text-align: center;
}
