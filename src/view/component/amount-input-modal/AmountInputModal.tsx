import React from 'react';
import { View, Text, Input } from '@tarojs/components';
import styles from './AmountInputModal.module.scss';

interface AmountInputModalProps {
  visible: boolean;
  value: string;
  onInput: (val: string) => void;
  onCancel: () => void;
  onConfirm: () => void;
  title?: string;
  placeholder?: string;
}

const AmountInputModal: React.FC<AmountInputModalProps> = ({
  visible,
  value,
  onInput,
  onCancel,
  onConfirm,
  title = '请输入金额',
  placeholder = '请输入金额'
}) => {
  if (!visible) return null;
  return (
    <View className={styles.modalMask}>
      <View className={styles.modalBox}>
        <Text className={styles.modalTitle}>{title}</Text>
        <Input
          className={styles.modalInput}
          type="digit"
          value={value}
          onInput={e => onInput(e.detail.value)}
          placeholder={placeholder}
          maxlength={10}
        />
        <View className={styles.modalBtnRow}>
          <View className={styles.modalBtn} onClick={onCancel}>取消</View>
          <View className={styles.modalBtn + ' ' + styles.modalBtnPrimary} onClick={onConfirm}>确定</View>
        </View>
      </View>
    </View>
  );
};

export default AmountInputModal; 