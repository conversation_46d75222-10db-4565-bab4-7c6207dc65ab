import { View, Text, Image } from "@tarojs/components";
import React from "react";
import SuccessImage from "@/assets/image/success.png";

interface SuccessModalProps {
  visible: boolean;
  onClose: () => void;
  image?: string;
  text?: string;
  buttonText?: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  visible,
  onClose,
  image,
  text = "已经成功添加预约项目",
  buttonText = "我知道了"
}) => {
  if (!visible) return null;
  return (
    <View className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <View className="bg-white rounded-2xl w-[320px] px-6 py-8 flex flex-col items-center">
        <Image src={image || SuccessImage} className="w-[200px] h-[137px] mb-4" mode="aspectFit" />
        <Text className="text-[18px] text-[#333] font-medium mb-6 text-center">{text}</Text>
        <View
          className="w-full h-[44px] bg-[#FFE18A] rounded-full flex items-center justify-center mt-2"
          onClick={onClose}
        >
          <Text className="text-[#fff] text-[16px] font-bold">{buttonText}</Text>
        </View>
      </View>
    </View>
  );
};

export default SuccessModal; 