import { useEffect, useMemo, useState } from "react";
import styles from "./index.module.scss"
import { ITouchEvent, <PERSON><PERSON><PERSON><PERSON><PERSON>, PickerViewColumn, View } from "@tarojs/components";
import { AtFloatLayout } from "taro-ui";
import { CommonUtil } from "@/util/common-util";

interface IProps {
    show: boolean,
    title?: string,
    data?: string | number,
    range: string[] | { label: string, value: string | number }[],
    onConfirm?: (val: string | number, option: { label: string, value: string | number }) => void,
    onClose: () => void
}

const Index: React.FC<IProps> = (props) => {
    const { show, title, data, range, onConfirm, onClose } = props;

    const [dataIndex, setDataIndex] = useState<number>(0)

    const indicatorClass = useMemo(() => {
        return isWeb ? styles.webIndicator : styles.indicator;
    }, [])

    const pickerOptions = useMemo(() => {
        return range.map(item => {
            if (typeof item === "string") {
                return {
                    label: item,
                    value: item
                }
            } else {
                return item
            }
        })
    }, [range])

    const closeHandler = (e: ITouchEvent) => {
        console.log("关闭对话框")
        e.stopPropagation();
        onClose();
    }

    const changeHandler = (index: number) => {
        setDataIndex(index)
    }

    const confirmHandler = (e: ITouchEvent) => {
        e.stopPropagation();
        onConfirm && onConfirm(pickerOptions[dataIndex]?.value, pickerOptions[dataIndex])
        onClose();
    }

    useEffect(() => {
        if (CommonUtil.stringIsNull(data)) {
            setDataIndex(0)
        } else {
            const index = pickerOptions.findIndex(option => option.value === data);
            setDataIndex(index)
        }
    }, [data, pickerOptions])

    return (

        <AtFloatLayout isOpened={show} onClose={onClose}>
            <View className={styles.custom_picker}>
                <View className={styles.container}>
                    <View className={styles.header}>
                        <View className={styles.cancel} onClick={closeHandler}>取消</View>
                        <View className={styles.title}>{title}</View>
                        <View className={styles.confirm} onClick={confirmHandler}>确定</View>
                    </View>
                    <View className={styles.wrapper}>
                        <View className={styles.pickerRow}>
                            <View className={styles.pickerView}>
                                <PickerView value={[dataIndex]} indicatorClass={indicatorClass}
                                    style={{ height: "100%" }} onChange={e => changeHandler(e.detail.value[0])}>
                                    <PickerViewColumn>
                                        {pickerOptions.map(item => {
                                            return (
                                                <View className={styles.pickerItem}>{item.label}</View>
                                            )
                                        })}
                                    </PickerViewColumn>
                                </PickerView>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        </AtFloatLayout>
    )
}

export default Index;