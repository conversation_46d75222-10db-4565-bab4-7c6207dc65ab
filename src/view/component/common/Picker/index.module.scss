.custom_picker {
    .header {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        font-size: 15px;
        color: #333333;

        .cancel,
        .confirm {
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }

        .confirm {
            color: #FDD244;
        }
    }

    .container {
        padding-bottom: 30px;

        .wrapper {
            .pickerRow {
                width: 100%;
                height: 150px;
                color: #333333;
                overflow: hidden;

                .pickerView {
                    height: 100%;
                    overflow: hidden;
                    // mask: ;

                    .pickerItem {
                        height: 50px;
                        color: #333333;
                        font-size: 20px;
                        line-height: 50px;
                        text-align: center;
                        z-index: 10;
                    }

                    :global {
                        .taro-picker-view-container {
                            height: 100%;
                        }

                        .taro-picker-view-column-container {
                            padding-top: 50px !important;
                            padding-bottom: 50px !important;
                            height: 100%;
                        }

                        // .taro-picker-view-mask-indicator {
                        //     width: 150px;
                        //     background-color: #F7F7F7;
                        //     border: 0;
                        //     margin: 0 auto;
                        // }
                    }
                }

                // .webIndicator {
                //     height: 50px;
                //     width: 150px;
                //     background-color: #F7F7F7;
                //     border: 0;
                //     margin: 0 auto;
                //     border-radius: 16px;
                //     z-index: -1;
                // }

                .indicator {
                    height: 50px;
                    // width: 150px;
                    // background-color: #F7F7F7;
                    // border: 0;
                    // margin: 0 auto;
                    // border-radius: 16px;
                    // left: 50%;
                    // transform: translateX(-50%);
                    // z-index: -1;

                    // &::before {
                    //     border: 0;
                    // }

                    // &::after {
                    //     border: 0;
                    // }
                }
            }
        }
    }
}

:global {
    .at-float-layout__container {
        min-height: unset;
        margin-right: unset;

        .layout-body {
            min-height: unset;
            margin-right: unset;
        }

        .layout-body__content {
            min-height: unset;
            margin-right: unset;
        }
    }
}