import { useEffect, useMemo, useState } from "react";
import styles from "./index.module.scss"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>iewColumn, View } from "@tarojs/components";
import { AtFloatLayout } from "taro-ui";
import dayjs from "dayjs";

interface IProps {
    show: boolean,
    start: string,
    end: string,
    onConfirm?: (start: string, end: string) => void,
    onClose: () => void
}

const interval = 5;

const days = Array.from({ length: 24 }).map((_, i) => {
    return Array.from({ length: Math.round(60 / interval) }).map((_, j) => {
        return [i.toString().padStart(2, '0'), (j * interval).toString().padStart(2, '0')]
    })
}).flat()

const Index: React.FC<IProps> = (props) => {
    const { show, start = "9:00", end = "17:00", onConfirm, onClose } = props;

    // days index
    const [startTime, setStartTime] = useState(0);
    const [endTime, setEndTime] = useState(0);

    const indicatorClass = useMemo(() => {
        return isWeb ? styles.webIndicator : styles.indicator;
    }, [])

    const startTimeStr = useMemo(() => {
        const time = days[startTime];
        return time ? `${time[0]}:${time[1]}` : ''
    }, [startTime])

    const endTimeStr = useMemo(() => {
        const time = days[endTime];
        return time ? `${time[0]}:${time[1]}` : ''
    }, [endTime])

    const confirmHandler = () => {
        onConfirm && onConfirm(startTimeStr, endTimeStr);
        onClose()
    }

    useEffect(() => {
        setStartTime(getTimeIndex(start))
    }, [start])

    useEffect(() => {
        setEndTime(getTimeIndex(end))
    }, [end])

    return (

        <View className={styles.time_range_picker}>
            <AtFloatLayout isOpened={show} onClose={onClose}>
                <View className={styles.container}>
                    <View className={styles.header}>
                        <view className={styles.cancel} onClick={onClose}>取消</view>
                        <view className={styles.title}>选择工作时间</view>
                        <view className={styles.confirm} onClick={confirmHandler}>确定</view>
                    </View>
                    <View className={styles.wrapper}>
                        <View className={styles.infoRow}>
                            <View className={styles.time_box}>
                                <View className={styles.time_info}>当天 {startTimeStr}</View>
                                <View className={styles.time_type}>开始时间</View>
                            </View>
                            <label className={styles.divider}>
                                -
                            </label>
                            <View className={styles.time_box}>
                                <View className={styles.time_info}>当天 {endTimeStr}</View>
                                <View className={styles.time_type}>结束时间</View>
                            </View>
                        </View>
                        <View className={styles.pickerRow}>
                            <View className={styles.pickerView}>
                                <PickerView value={[startTime]} indicatorClass={indicatorClass}
                                    style={{ height: "100%" }} onChange={e => setStartTime(e.detail.value[0])}>
                                    <PickerViewColumn>
                                        {days.map(item => {
                                            return (
                                                <View className={styles.pickerItem}>{item[0]}&nbsp;&nbsp;&nbsp;&nbsp;{item[1]}</View>
                                            )
                                        })}
                                    </PickerViewColumn>
                                </PickerView>
                            </View>
                            <label className={styles.divider}>
                            </label>
                            <View className={styles.pickerView}>
                                <PickerView value={[endTime]} indicatorClass={indicatorClass}
                                    style={{ height: "100%" }} onChange={e => setEndTime(e.detail.value[0])}>
                                    <PickerViewColumn>
                                        {days.map(item => {
                                            return (
                                                <View className={styles.pickerItem}>{item[0]}&nbsp;&nbsp;&nbsp;&nbsp;{item[1]}</View>
                                            )
                                        })}
                                    </PickerViewColumn>
                                </PickerView>
                            </View>
                        </View>
                    </View>
                </View>
            </AtFloatLayout>

        </View>
    )
}

export default Index;

const getTimeIndex = (time: string, split: string = ":") => {
    if (!time.trim()) {
        return 0
    }
    try {
        const _arr = time.split(split).filter(item => item != null && item != undefined && item != "").map(item => Number(item));
        if (_arr.length >= 2) {
            const [hour, minute] = _arr;
            return hour * Math.round(60 / interval) + Math.floor(minute / interval)
        }
    } catch (error) {
        console.log(error)
    }

    return 0
}