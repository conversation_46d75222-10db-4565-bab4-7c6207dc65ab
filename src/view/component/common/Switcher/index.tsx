import { CSSProperties, useEffect, useMemo, useState } from "react";
import styles from "./index.module.scss"
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";

interface IProps {
    value: string | number,
    size?: string,
    options: { label?: string, value: string | number }[],
    onChange: (val: string | number) => void
}

const Index: React.FC<IProps> = (props) => {
    const { value, size = "default", options, onChange } = props;
    const [itemWidth, setItemWidth] = useState<number>()

    const _options = useMemo(() => {
        return options.map(item => ({
            label: item.label || item.value.toString(),
            value: item.value
        }))
    }, [options])

    const index = useMemo(() => {
        const idx = _options.findIndex(option => option.value === value);
        return idx == -1 ? 0 : idx
    }, [value, _options])

    useEffect(() => {
        const query = Taro.createSelectorQuery();
        query
            .selectAll("." + styles.option)
            .boundingClientRect((rect: any) => {
                setItemWidth(Math.max(...rect.map(item => item.width)))
                // rect && setClientHeight(rect?.height);
            })
            .exec();
    }, [])

    return (
        <View className={`${styles.switcher} ${styles[size]}`}>
            <View className={styles.slider} style={{ width: itemWidth, '--translateX': index * 100 + "%" } as CSSProperties}></View>
            {
                _options.map(item => (
                    <View key={item.value} className={`${styles.option} ${item.value === value ? styles.active : ''}`} style={{ width: itemWidth }} onClick={() => onChange(item.value)}>
                        {item.label}
                    </View>
                ))
            }
        </View>
    )
}

export default Index;