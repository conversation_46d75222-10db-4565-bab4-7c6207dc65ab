$padding: 2px;
$border-radius: 5px;
$font-size: 14px;

.switcher {
    border-radius: $border-radius;
    padding: $padding;
    display: flex;
    width: fit-content;
    height: 26px;
    box-sizing: border-box;
    background-color: #EDEDED;
    position: relative;
    font-size: $font-size;

    .slider {
        height: calc(100% - $padding * 2);
        box-sizing: border-box;
        background-color: #FDD244;
        position: absolute;
        left: $padding;
        top: $padding;
        border-radius: $border-radius;
        transform: translateX(var(--translateX));
        transition: 0.25s;
    }

    .option {
        position: relative;
        padding: $padding 12px;
        width: fit-content;
        border-radius: $border-radius;
        box-sizing: border-box;
        z-index: 10;
        transition: 0.25s;
        text-align: center;
        white-space: nowrap;

        &.active {
            color: #fff;
        }
    }

    &.large {
        height: 30px;
        font-size: $font-size + 2;
        padding: $padding + 1;
    }

    &.small {
        height: 22px;
        font-size: $font-size - 2;
        padding: $padding - 1;

        .option {
            padding: 1px 6px;
        }
    }
}