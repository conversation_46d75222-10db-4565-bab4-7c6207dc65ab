import { ReactNode, useEffect, useMemo, useState } from "react";
import { Picker } from "@tarojs/components";
import dayjs from "dayjs";

interface IProps {
    title?: ReactNode,
    data: string,
    children: ReactNode,
    onChange?: (val: string) => void
}

const Index: React.FC<IProps> = (props) => {
    const { title, data, children, onChange } = props;

    const [year, setYear] = useState(0);
    const [month, setMonth] = useState(0);
    const [day, setDay] = useState(0);
    const [hour, setHour] = useState(0);
    const [minute, setMinute] = useState(0);
    const [second, setSecond] = useState(0);

    const value = useMemo(() => {
        return [year, month, day, hour, minute, second]
    }, [year, month, day, hour, minute, second])

    const range = useMemo(() => {
        const year = dayjs().year();

        const yearRange = Array.from({ length: (year - 1970) }).map((_, i) => 1970 + i + 1);
        const monthRange = Array.from({ length: 12 }).map((_, i) => i + 1);



        let dayLength = 28;
        if ([1, 3, 5, 7, 8, 10, 12].includes(month + 1)) {
            dayLength = 31
        } else if ([4, 6, 9, 11].includes(month + 1)) {
            dayLength = 30
        } else {
            dayLength = year % 4 == 0 ? 29 : 28;
        }
        const dayRange = Array.from({ length: dayLength }).map((_, i) => i + 1);
        const hourRange = Array.from({ length: 23 }).map((_, i) => i + 1);
        const minuteRange = Array.from({ length: 59 }).map((_, i) => i + 1);
        const secondRange = Array.from({ length: 59 }).map((_, i) => i + 1);

        return [yearRange, monthRange, dayRange, hourRange, minuteRange, secondRange]
    }, [month])

    useEffect(() => {
        try {
            const datetime = dayjs(data || undefined);
            let Y = datetime.year();
            let M = datetime.month();
            let D = datetime.date();

            let H = datetime.hour();
            let m = datetime.minute();
            let s = datetime.second();

            setYear(Y - 1970 - 1);
            setMonth(M);
            setDay(D - 1);
            setHour(H - 1);
            setMinute(m - 1);
            setSecond(s - 1);
        } catch (error) {

        }
    }, [data])

    const onDatetimeChange = (e) => {
        const [Y, M, D, H, m, s] = e.detail.value;
        onChange && onChange(`${Y + 1970 + 1}-${(M + 1).toString().padStart(2, '0')}-${(D + 1).toString().padStart(2, '0')} ${(H + 1).toString().padStart(2, '0')}:${(m + 1).toString().padStart(2, '0')}:${(s + 1).toString().padStart(2, '0')}`)
    }

    const onColumnChange = (e) => {
        const { column, value } = e.detail;
        if (column == 1) {
            setMonth(value)
        }
    }

    return (
        <Picker mode="multiSelector" value={value} range={range} onChange={onDatetimeChange} onColumnChange={onColumnChange}>
            {children}
        </Picker >
    )
}

export default Index;