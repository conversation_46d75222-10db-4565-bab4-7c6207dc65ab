.confetti {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  overflow: hidden;
  z-index: 1000;

  .piece {
    position: absolute;
    width: 15px;
    height: 28px;
    background-color: hsl(39, 100%, 56%);
    top: 0;
    opacity: 0;
    animation: makeItRain 3000ms infinite linear;
  }
  
  .piece:nth-child(1) {
    left: 7%;
    transform: rotate(-10deg);
    animation-delay: 182ms;
    animation-duration: 2000ms;
  }
  
  .piece:nth-child(2) {
    left: 14%;
    transform: rotate(20deg);
    animation-delay: 161ms;
    animation-duration: 2076ms;
  }
  
  .piece:nth-child(3) {
    left: 21%;
    transform: rotate(-51deg);
    animation-delay: 481ms;
    animation-duration: 2103ms;
  }
  
  .piece:nth-child(4) {
    left: 28%;
    transform: rotate(61deg);
    animation-delay: 334ms;
    animation-duration: 1008ms;
  }
  
  .piece:nth-child(5) {
    left: 35%;
    transform: rotate(-52deg);
    animation-delay: 302ms;
    animation-duration: 1776ms;
  }
  
  .piece:nth-child(6) {
    left: 42%;
    transform: rotate(38deg);
    animation-delay: 180ms;
    animation-duration: 1168ms;
  }
  
  .piece:nth-child(7) {
    left: 49%;
    transform: rotate(11deg);
    animation-delay: 395ms;
    animation-duration: 1200ms;
  }
  
  .piece:nth-child(8) {
    left: 56%;
    transform: rotate(49deg);
    animation-delay: 14ms;
    animation-duration: 1887ms;
  }
  
  .piece:nth-child(9) {
    left: 63%;
    transform: rotate(-72deg);
    animation-delay: 149ms;
    animation-duration: 1805ms;
  }
  
  .piece:nth-child(10) {
    left: 70%;
    transform: rotate(10deg);
    animation-delay: 351ms;
    animation-duration: 2059ms;
  }
  
  .piece:nth-child(11) {
    left: 77%;
    transform: rotate(4deg);
    animation-delay: 307ms;
    animation-duration: 1132ms;
  }
  
  .piece:nth-child(12) {
    left: 84%;
    transform: rotate(42deg);
    animation-delay: 464ms;
    animation-duration: 1776ms;
  }
  
  .piece:nth-child(13) {
    left: 91%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 1818ms;
  }
  
  .piece:nth-child(14) {
    left: 94%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 818ms;
  }
  
  .piece:nth-child(15) {
    left: 96%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 2818ms;
  }
  
  .piece:nth-child(16) {
    left: 98%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 2818ms;
  }
  
  .piece:nth-child(17) {
    left: 50%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 2818ms;
  }
  
  .piece:nth-child(18) {
    left: 60%;
    transform: rotate(-72deg);
    animation-delay: 429ms;
    animation-duration: 1818ms;
  }
  
  .piece:nth-child(odd) {
    background-color: hsl(0, 100%, 67%);
  }
  
  .piece:nth-child(even) {
    z-index: 1;
  }
  
  .piece:nth-child(4n) {
    width: 6px;
    height: 14px;
    animation-duration: 4000ms;
    background-color: #c33764;
  }
  
  .piece:nth-child(5n) {
    width: 3px;
    height: 10px;
    animation-duration: 4000ms;
    background-color: #b06ab3;
  }
  
  .piece:nth-child(3n) {
    width: 4px;
    height: 12px;
    animation-duration: 2500ms;
    animation-delay: 3000ms;
    background-color: #dd2476;
  }
  
  .piece:nth-child(3n-7) {
    background-color: hsl(166, 100%, 37%);
  }
  
}

@keyframes makeItRain {
  from {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    transform: translateY(250px);
  }
}