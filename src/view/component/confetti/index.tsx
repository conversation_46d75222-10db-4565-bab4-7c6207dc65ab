import { observer } from "mobx-react";
import { View } from "@tarojs/components";
import styles from "./index.module.scss";

interface Props {
	width?: string | number;
	height?: string | number;
}

const Confetti: React.FC<Props> = observer((props) => {
	return (
		<View
			className={styles.confetti}
			style={{
				width: props.width ?? "100%",
				height: props.height ?? "100%",
			}}
		>
			{Array.from({ length: 18 }).map((_, idx) => (
				<View key={idx} className={styles.piece}></View>
			))}
		</View>
	);
});

export default Confetti;
