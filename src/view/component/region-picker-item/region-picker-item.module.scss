.container {
	width: 100%;
	transition: 0.1s;

	> .content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		gap: 8px;
		margin-left: 24px;
		padding-left: 4px;
		padding-right: 24px;
		width: calc(100% - 24px - 4px - 24px);
		border-bottom: 1px solid #F4F6FA;

		> .mainLine {
			flex: 1;
			display: flex;
			flex-direction: row;
			align-items: center;

			> .title {
				width: 150px;
				color: #1A1A1A;
				font-size: 30px;
			}

			> .picker {
				flex: 1;
				width: 100%;

				.pickerContent {
					width: 100%;

					> .valueText {
						color: #1A1A1A;
						font-size: 28px;
					}

					> .placeholderText {
						color: #9DA3B2;
						font-size: 28px;
					}
				}
			}

			.arrow {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 8px;
				color: #4C5666;
			}
		}

		> .subLine {
			// color: #F55858;
			color: #9DA3B2;
			font-size: 22px;
		}
	}
}

.contentPressed {
	background: rgb(240, 240, 240);
}
