import { Button, View, Text } from '@tarojs/components';
import MemberCard from '../../../member/member-card';
import styles from './index.module.scss';

const demo = {
  name: '杭州易美整形',
  no: '88888888',
  level: '黑金卡',
  bg_color: '#000000',
  name_color: 'rgb(255,215,0)',
  pinyin_color: 'rgb(255,215,0)',
  no_color: 'rgb(255,215,0)',
  level_color: 'rgb(255,215,0)',
};

const ShareCard = () => {
  return (
    <View className={styles.container}>
      <View className={styles.wrapper}>
        <View className={styles.card}>
          <Button className={styles.openButton}>开启</Button>
          <View className={styles.topPart} />
          <View className={styles.leftPart} />
          <View className={styles.rightPart} />
          <View className={styles.bottomPart} />
        </View>
        <View className={styles.memberCardWrapper}>
          <View className={styles.memberCard}>
            <MemberCard
              {...demo}
              className='relative overflow-hidden rounded-xl bg-blue-gray-500 bg-clip-border text-white shadow-lg shadow-blue-gray-500/40'
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default ShareCard;
