.container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wrapper {
  position: relative;
}

.card {
  position: relative;
  z-index: 10;
  background-color: black;
  width: 656px;
  transition: all 700ms;
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.openButton {
  background-color: #f43f5e;
  color: #991b1b;
  width: 2.5rem;
  aspect-ratio: 1/1;
  border-radius: 9999px;
  z-index: 40;
  word-break: keep-all;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  clip-path: polygon(50% 0%, 80% 10%, 100% 35%, 100% 70%, 80% 90%, 50% 100%, 20% 90%, 0% 70%, 0% 35%, 20% 10%);
  border: 4px solid #7f1d1d;

  .card:hover & {
    opacity: 0;
    transform: scale(0) rotate(180deg);
    transition: all 1000ms;
  }
}

.topPart {
  transition: all 1000ms;
  background-color: #262626;
  position: absolute;
  width: 100%;
  height: 100%;
  clip-path: polygon(50% 50%, 100% 0, 0 0);

  .card:hover & {
    transition-duration: 100ms;
    clip-path: polygon(50% 0%, 100% 0, 0 0);
  }
}

.leftPart {
  transition: all 700ms;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #171717;
  clip-path: polygon(50% 50%, 0 0, 0 100%);
}

.rightPart {
  transition: all 700ms;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #262626;
  clip-path: polygon(50% 50%, 100% 0, 100% 100%);
}

.bottomPart {
  transition: all 700ms;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #171717;
  clip-path: polygon(50% 50%, 100% 100%, 0 100%);
}

.memberCardWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.memberCard {
  transition: all;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition-duration: 300ms;
  background-color: white;
  width: 100%;
  height: 100%;
  position: absolute;

  .card:hover & {
    transition-duration: 1000ms;
    transform: translateY(-10rem);
  }
}
