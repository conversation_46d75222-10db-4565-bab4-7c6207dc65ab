import { View } from "@tarojs/components";
import type { TaroElement } from "@tarojs/runtime";
import {
	createContext,
	useCallback,
	useContext,
	useState,
	type PropsWithChildren,
} from "react";
import { createPortal } from "react-dom";
import { useTaroPage } from "./hooks";

export type PortalTarget = TaroElement | null | undefined;

export interface PortalProps extends PropsWithChildren {
	/**
	 * 是否从页面中脱离出来
	 */
	enable?: boolean;
	/**
	 * 传送的目标：DOM 元素对象
	 */
	target?: PortalTarget;
	/**
	 * 优先级小于 `target`
	 *
	 * `true`     - 渲染到页面根节点
	 * `'first'`  - 渲染到页面根节点的第一个子节点，用于适配 `ConfigProvider` 全局配置
	 */
	root?: boolean | "first";
}

const PortalRefContext = createContext<PortalTarget>(null);

export const usePortalRef = () => useContext(PortalRefContext);

export function PortalProvider({ children }: PropsWithChildren) {
	// ref: https://stackoverflow.com/a/67906087
	const [dom, setDom] = useState<PortalTarget>();
	const ref = useCallback((node: PortalTarget) => node && setDom(node), []);

	return (
		<PortalRefContext.Provider value={dom}>
			{children}
			<View ref={ref} className="teleport-target" />
		</PortalRefContext.Provider>
	);
}

/**
 * ref: https://react.dev/reference/react-dom/createPortal
 * ref: https://docs.taro.zone/docs/components/viewContainer/root-portal
 * ref: https://github.com/NervJS/taro/issues/7282#issuecomment-**********
 */
export default function Portal(props: PortalProps) {
	const { children, enable = true, target, root } = props;
	const provideTarget = usePortalRef();
	const pageNode = useTaroPage();

	const targetNode =
		target ||
		(root
			? root === "first"
				? pageNode?.firstChild
				: pageNode
			: provideTarget ?? pageNode);
	return enable && targetNode
		? createPortal(children, targetNode as any)
		: children;
}
