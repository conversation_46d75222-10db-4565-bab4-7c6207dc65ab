.mask {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 9998;
}

.container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 16px;
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.5);
	border-radius: 16px;

	> .animateCircle {
		animation: circle 2s linear infinite;
	}

	> .message {
		font-size: 28px;
		color: #FFFFFF;
	}
}

.open {
	width: 250px;
	height: 250px;
	z-index: 9999;
}

.close {
	width: 0;
	height: 0;
	z-index: 0;
}

@keyframes circle {
	0% {
		transform: rotate(0deg);
	}
	50% {
		transform: rotate(180deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
