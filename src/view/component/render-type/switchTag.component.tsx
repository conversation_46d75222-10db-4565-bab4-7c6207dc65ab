import React from "react";
import { Text, View, ViewProps, Image } from "@tarojs/components";
import SettingImg from "@/assets/image/common/setting.png";
import styles from "./render-type-item.module.scss";
import Taro from "@tarojs/taro";

interface SwitchTagItemPropsType extends ViewProps {
	range: {
		code: string | number;
		name: string;
	}[];
	value: any;
	onChange?: (e: any) => void;
}

const Index: React.ComponentType<SwitchTagItemPropsType> = ({
	value,
	onChange,
	range = [],
}) => {
	const onClick = (item) => {
		onChange?.(item?.code);
	};

	const toStaffShift = () => {
		Taro.navigateTo({ url: "/employee/staff-shift/staff-shift-page" })
	}

	return (
		<View className={styles.switchTag}>
			<View className={styles.tags}>
				{range?.map((item) => (
					<Text
						className={`${styles.tag} ${value == item.code ? styles.active : ""
							}`}
						key={item.code}
						onClick={() => onClick(item)}
					>
						{item.name}
					</Text>
				))}
			</View>
			<View className={styles.setting} onClick={toStaffShift}>
				<Image
					src={SettingImg}
					className={styles.img}
					mode="aspectFill"
				/>
			</View>
		</View>
	);
};

export default Index;
