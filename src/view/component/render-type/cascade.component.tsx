import React, { useState } from "react";
import { Text, View, ViewProps, Image } from "@tarojs/components";
import CustomCascadeSelect from "@/view/component/custom-cascade-select/custom-cascade-select.component";
import ArrowImag from "@/assets/image/common/right-arrow.png";
import styles from "./render-type-item.module.scss";

interface PickerItemPropsType extends ViewProps {
	range: {
		code: string | number;
		name: string;
	}[];
	value: any;
	placeholder: string;
	onChange?: (e: any) => void;
}

function findPathByCodes(range: any[], codes: (string|number)[]): any[] {
	let path: any[] = [];
	let current: any[] = range;
	for (let i = 0; i < codes.length; i++) {
		const codeStr = String(codes[i]);
		const found = current.find((item: any) => String(item.code) === codeStr);
		if (!found) break;
		path.push(found);
		current = found.children || [];
	}
	return path;
}

const Index: React.ComponentType<PickerItemPropsType> = ({
	value,
	onChange,
	range = [],
	placeholder,
}) => {
	const [visible, setVisible] = useState(false);
	const selectedName = range?.find((item) => item.code === value)?.name;

	return (
		<View className={styles.picker}>
			<View
				className={styles.pickerContent}
				onClick={() => setVisible(true)}
			>
				{selectedName ? (
					<Text className={styles.valueText}>{selectedName}</Text>
				) : (
					<Text className={styles.placeholderText}>
						{placeholder}
					</Text>
				)}
			</View>
			<CustomCascadeSelect
				options={range.map((item: any) => ({ ...item, code: String(item.code) }))}
				value={value ? value : []}
				onChange={(arr) => {
					if (Array.isArray(arr) && arr.length && (typeof arr[0] === 'number' || typeof arr[0] === 'string')) {
						const path = findPathByCodes(range as any[], arr as (string|number)[]);
						const ids = path.map((item: any) => String(item.code)).join(',');
						const names = path.map((item: any) => item.name).join('/');
						console.log(ids, names, 22222, range);
						onChange?.({ businessCategory: ids, businessCategoryLabel: names });
					} else if (Array.isArray(arr) && arr.length && typeof arr[0] === 'object' && 'code' in arr[0] && 'name' in arr[0]) {
						const ids = (arr as any[]).map((item: any) => String(item.code)).join(',');
						const names = (arr as any[]).map((item: any) => item.name).join('/');
						console.log(ids, names, 33333, range);
						onChange?.({ businessCategory: ids, businessCategoryLabel: names });
					} else {
						console.log(arr, 44444, range);
						onChange?.(arr);
					}
					setVisible(false);
				}}
				title={`请选择${placeholder}`}
				visible={visible}
				onClose={() => setVisible(false)}
			/>
			<View className={styles.arrow}>
				<Image
					src={ArrowImag}
					className={styles.img}
					mode="aspectFill"
				/>
			</View>
		</View>
	);
};

export default Index;
