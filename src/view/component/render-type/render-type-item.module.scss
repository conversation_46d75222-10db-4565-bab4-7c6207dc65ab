@use "@/theme.scss" as t;

/** Input 组件 */
.input {
	flex: 1;
	color: #333;
	font-weight: 400;
	font-size: t.$font-size-sm;
	font-size: 15px;
	font-family: PingFangSC, PingFang SC;
	font-style: normal;
	line-height: 20px;
	text-align: left;
}

/** Picker 组件 */
.picker {
	width: calc(100% - 70px);
	@extend %display-flex-row;

	.pickerContent {
		width: 100%;

		> .valueText {
			color: #333333;
			font-size: 15px;
		}

		> .placeholderText {
			color: #9da3b2;
			font-size: 15px;
		}
	}

	.arrow {
		display: flex;
		width: 8px;
		height: 14px;
		.img {
			width: 8px;
			height: 14px;
		}
	}
}

/** Switch 组件 */
.switch {
	padding: 0 !important;
	border-bottom: none;
	:global {
		.at-switch {
			padding: 0 !important;
			border-bottom: none;
		}
	}
}

/** switchTag 组件 */
.switchTag {
	@extend %display-flex-row;
	width: calc(100% - 70px);

	.tags {
		padding: 1px 3px;
		color: #333333;
		font-weight: 400;
		font-size: 14px;
		background: #f4f4f4;
		border-radius: 8px;

		.tag {
			padding: 2px 13px;
			border-radius: 8px;
		}

		.active {
			color: #fff;
			background: #fdd244;
		}
	}

	.setting {
		display: flex;
		width: 15px;
		height: 16px;
		.img {
			width: 15px;
			height: 16px;
		}
	}
}

.container {
	width: 100%;
	transition: 0.1s;

	> .content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		box-sizing: border-box;
		width: 100%;
		width: 100%;
		padding: 14px 0px;
		padding-left: 4px;
		border-bottom: 1px solid t.$border-color;

		> .mainLine {
			@extend %display-flex-row;
			position: relative;

			> .title {
				width: 70px;
				height: 22px;
				color: #1d2129;
				font-weight: 400;
				font-size: 15px;
				font-family: PingFangSC, PingFang SC;
				font-style: normal;
				line-height: 22px;
				text-align: left;

				.required {
					margin-left: 4px;
					color: t.$color-error;
				}
			}
		}

		> .subLine {
			color: #9da3b2;
			font-size: t.$font-size-sm;
		}
	}

	:global {
		.weui-input {
			height: 100% !important;
			color: #333;
		}
	}

	:global {
		.weui-switch {
			background-color: t.$color-brand !important;
			border-color: t.$color-brand !important;
		}
	}
}

.contentPressed {
	background: rgb(240, 240, 240);
}

.inputNumber-container {
	> .content {
		> .mainLine {
			justify-content: space-between;
		}
	}
}
