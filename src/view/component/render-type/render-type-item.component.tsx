import { View, Text, Input, Switch } from "@tarojs/components";
import { AtInputNumber } from "taro-ui";
import Picker from "./pick.component";
import SwitchTag from "./switchTag.component";
import CascadeSelect from "./cascade.component";
import { IType } from "@/util/fieldsMap";
import styles from "./render-type-item.module.scss";

const Index = ({
	type,
	title,
	itemKey,
	value,
	onChange,
	range = [],
	required,
	...rest
}) => {
	const Comp = {
		[IType.Input]: (
			<Input
				placeholder={`请输入${title}`}
				value={value}
				disabled={rest?.disabled}
				onInput={(e) => onChange({ [itemKey]: e.detail.value })}
				placeholderTextColor="#9da3b2"
				className={styles.input}
			/>
		),
		[IType.InputNumber]: (
			<AtInputNumber
				min={0}
				max={999}
				step={1}
				value={value}
				onChange={(v) => onChange({ [itemKey]: v })}
				className={styles.inputNumber}
			/>
		),
		[IType.Picker]: (
			<Picker
				value={value}
				range={range}
				placeholder={title}
				onChange={(v) => onChange({ [itemKey]: v })}
			/>
		),
		[IType.Switch]: (
			<Switch
				checked={Boolean(value)}
				color="#fdd244"
				onChange={(v) => console.log(v)}
				className={styles.switch}
			/>
		),
		[IType.SwitchTag]: (
			<SwitchTag
				range={range}
				value={value}
				onChange={(v) => onChange({ [itemKey]: v })}
			/>
		),
		[IType.CascadeSelect]: (
			<CascadeSelect
				value={value}
				range={range}
				placeholder={title}
				onChange={(v) => onChange({ [itemKey]: v })}
			/>
		),
	}[type];

	return Comp ? (
		<View
			className={`${styles.container} ${styles?.[`${type}-container`]}`}
			key={itemKey}
		>
			<View className={styles.content} style={rest?.style}>
				<View className={styles.mainLine}>
					<View
						className={styles.title}
						style={{
							width: rest?.titleWidth ?? 70,
						}}
					>
						{title}
						{required && <Text className={styles.required}>*</Text>}
					</View>
					{Comp}
				</View>
			</View>
		</View>
	) : null;
};

export default Index;
