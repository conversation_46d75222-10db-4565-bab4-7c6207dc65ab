import React, { useState } from "react";
import { Text, View, ViewProps, Image } from "@tarojs/components";
import CustomPicker from "@/view/component/custom-picker/custom-picker.component";
import ArrowImag from "@/assets/image/common/right-arrow.png";
import styles from "./render-type-item.module.scss";

interface PickerItemPropsType extends ViewProps {
	range: {
		code: string | number;
		name: string;
	}[];
	value: any;
	placeholder: string;
	onChange?: (e: any) => void;
}

const Index: React.ComponentType<PickerItemPropsType> = ({
	value,
	onChange,
	range = [],
	placeholder,
}) => {
	const [visible, setVisible] = useState(false);
	const selectedName = range?.find((item) => item.code === value)?.name;

	return (
		<View className={styles.picker}>
			<View
				className={styles.pickerContent}
				onClick={() => setVisible(true)}
			>
				{selectedName ? (
					<Text className={styles.valueText}>{selectedName}</Text>
				) : (
					<Text className={styles.placeholderText}>
						{placeholder}
					</Text>
				)}
			</View>
			<CustomPicker
				title={`请选择${placeholder}`}
				visible={visible}
				options={range}
				value={value}
				onCancel={() => setVisible(false)}
				onConfirm={(e) => {
					onChange(e);
					setVisible(false);
				}}
			/>
			<View className={styles.arrow}>
				<Image
					src={ArrowImag}
					className={styles.img}
					mode="aspectFill"
				/>
			</View>
		</View>
	);
};

export default Index;
