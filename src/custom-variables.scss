/** 1.重新定义taro-ui主题变量 2.重写tarao-ui 组件默认样式 */
$hd: 1 !default; // 基本单位
$color-brand: #fdd244;
$color-brand-light: #fdd244;
$color-brand-dark: #fdd244;
$color-link-active: #fdd244;
$color-border-base: #bfbfbf;
// $color-bg: #fdd244;

/* Border Radius */
$border-radius-sm: 2px * $hd !default;
$border-radius-md: 4px * $hd !default;
$border-radius-lg: 8px * $hd !default;
$border-radius-circle: 50% !default;

/** Font */
$font-size-xs: 8px * $hd !default; // 非常用字号，用于标签
$font-size-sm: 12px * $hd !default; // 用于辅助信息
$font-size-base: 14px * $hd !default; // 常用字号
$font-size-lg: 15px * $hd !default; // 常规标题
$font-size-xl: 18px * $hd !default; // 大标题

/** Tag */
$at-tag-height: 40px !default;
$at-tag-height-sm: 24px !default;
$at-tag-font-size-sm: 12px !default;

/* Button */
$at-button-height: 36px !default;
$at-button-height-sm: 36px !default;

/* Avatar */
$at-avatar-size-sm: 40px !default;
$at-avatar-size-md: 60px !default;

/** LoadMore */
$at-loading-size: 12px !default;
$at-load-more-height: 40px !default;
$at-load-more-tips-size: 14px !default;
$at-activity-indicator-font-size: 14px !default;

/** checkbox */
$at-checkbox-circle-size: 16px !default;
$at-checkbox-title-font-size: $font-size-base !default;

/** InputNumber */
.at-input-number__btn {
	padding: 4px !important;
}
.at-input-number__btn-subtract,
.at-input-number__btn-add {
	font-size: 14px !important;
	line-height: 24px !important;
}

/** AtToast */
.at-toast {
	.toast-body-content__img-item {
		width: 24px !important;
		height: 24px !important;
	}

	.toast-body {
		min-width: 120px !important;
	}
}

/** AtToast */
.at-tabs {
	.at-tabs__item {
		box-sizing: border-box;
		height: 50px;
		color: #999999;
		font-size: 15px;
	}

	.at-tabs__item--active {
		color: #333333;
		font-weight: 600;
		font-size: 15px;
	}

	.at-tabs__item-underline {
		width: 50%;
		height: 3px;
		margin-left: 25%;
		background: #fdd244;
		border-radius: 2px;
	}
}

/* 引入 Taro UI 默认样式 */
@import "~taro-ui/dist/style/index.scss";
