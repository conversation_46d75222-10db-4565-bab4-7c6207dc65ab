export class CommonUtil {
	public static stringIsNull(s: string | null | undefined | number): boolean {
		return s == null || s == "" || s?.toString()?.trim() == "";
	}

	public static richHtml(s: string): string {
		return s.replace(
			/<img src=/gi,
			'<img style="max-width:100%;height:auto" src='
		);
	}

	/** 校验手机号格式 */
	public static isValidPhoneNumber = (phone: string | undefined): boolean => {
		if (!phone) return false;
		// 中国大陆手机号格式校验：1开头的11位数字
		const phoneRegex = /^1[3-9]\d{9}$/;
		return phoneRegex.test(phone);
	};
}
