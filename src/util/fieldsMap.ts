/** 一些全局性通用字段集合 */
import {
	sexList,
	statusList,
	schedulingStatusList,
	takeOrdersModeList,
	dayCanAppointmentTimeList,
	positionList,
	rankList,
} from "./constant";
import { CommonUtil } from "./common-util";

enum IType {
	Input = "input",
	InputNumber = "inputNumber",
	Switch = "switchConfig",
	SwitchTag = "SwitchTag",
	Picker = "pickerConfig",
	CascadeSelect = "CascadeSelect",
}

/**定义校验规则类型 */
interface ValidationRule {
	key: string;
	title: string;
	validate: (value: any) => boolean;
	message: string;
}

/** 文本型 */
const inputConfig: any = {
	employeeNickname: {
		type: IType.Input,
		title: "用户名",
		key: "employeeNickname",
		validations: [
			{
				validate: (value) => CommonUtil.stringIsNull(value),
				message: "用户名不能为空",
			},
		],
	},
	employeePhone: {
		type: IType.Input,
		title: "手机号",
		key: "employeePhone",
		validations: [
			{
				validate: (value) => CommonUtil.stringIsNull(value),
				message: "手机号不能为空",
			},
			{
				validate: (value) =>
					!CommonUtil.stringIsNull(value) &&
					!CommonUtil.isValidPhoneNumber(value),
				message: "请输入正确的手机号格式",
			},
		],
	},
	position: {
		type: IType.Input,
		title: "职位",
		key: "position",
	},
	templateName: {
		type: IType.Input,
		title: "模版名称",
		key: "templateName",
	},
	businessName: {
		type: IType.Input,
		title: "店铺名字",
		key: "businessName",
	},
	address: {
		type: IType.Input,
		title: "店铺地址",
		key: "address",
	},
	contactPhone: {
		type: IType.Input,
		title: "联系电话",
		key: "contactPhone",
	},
	slogan: {
		type: IType.Input,
		title: "编号备注",
		key: "slogan",
	},
	phone: {
		type: IType.Input,
		title: "营业电话",
		key: "phone",
	},
};

/** 数值型 */
const inputNumberConfig: any = {
	meanwhileReceptionNum: {
		type: IType.InputNumber,
		title: "最多可接待人数",
		key: "meanwhileReceptionNum",
	},
	appointmentNum: {
		type: IType.InputNumber,
		title: "预约次数",
		key: "appointmentNum",
	},
};

/** Radio 型 */
const switchConfig: any = {
	noteNotifySwitch: {
		type: IType.Switch,
		title: "短信通知",
		key: "noteNotifySwitch",
	},
	wxNotifySwitch: {
		type: IType.Switch,
		title: "微信通知",
		key: "wxNotifySwitch",
	},
	notifyPhone: {
		type: IType.Switch,
		title: "预约手机号通知",
		key: "notifyPhone",
	},
	status: {
		type: IType.Switch,
		title: "状态",
		key: "status",
	},
};

/** 特殊的 Switch */
const switchTagConfig: any = {
	onDutyStatus: {
		type: IType.SwitchTag,
		title: "排班状态",
		key: "onDutyStatus",
		range: schedulingStatusList,
	},
};

/**  下拉框单选 */
const pickerConfig: any = {
	sex: {
		type: IType.Picker,
		title: "性别",
		key: "sex",
		range: sexList,
	},
	position: {
		type: IType.Picker,
		title: "岗位",
		key: "position",
		range: positionList,
	},
	rank: {
		type: IType.Picker,
		title: "级别",
		key: "rank",
		range: rankList,
	},
	takeOrdersMode: {
		type: IType.Picker,
		title: "接单模式",
		key: "takeOrdersMode",
		range: takeOrdersModeList,
	},
	projectIds: {
		type: IType.Picker,
		title: "项目名称",
		key: "projectIds",
		range: [],
	},
	employeeIds: {
		type: IType.Picker,
		title: "关联员工",
		key: "employeeIds",
		range: [],
	},
	dayCanAppointmentTime: {
		type: IType.Picker,
		title: "可预约时间段",
		key: "dayCanAppointmentTime",
		range: dayCanAppointmentTimeList,
	},
	weekCanAppointmentDate: {
		type: IType.Picker,
		title: "可预约日期",
		key: "weekCanAppointmentDate",
		range: [],
	},
	businessId: {
		type: IType.Picker,
		title: "所属门店",
		key: "businessId",
		range: [],
	},
};

/** 级联选择器 */
const cascadeSelect: any = {
	category: {
		type: IType.CascadeSelect,
		title: "门店品类",
		key: "category",
		range: [],
	},
	region: {
		type: IType.CascadeSelect,
		title: "所在地区",
		key: "region",
		range: [],
	},
};

/** 所有字段集合 */
const fieldsConfig = {
	...inputConfig,
	...inputNumberConfig,
	...switchConfig,
	...pickerConfig,
	...switchTagConfig,
	...cascadeSelect,
};

/**
 * 获取字段的校验规则
 * @param fieldKey 字段key
 * @returns 校验规则数组
 */
const getFieldValidations = (fieldKey: string): ValidationRule[] => {
	const field = fieldsConfig[fieldKey];
	if (!field || !field.validations) return [];

	return field.validations.map((rule) => ({
		key: field.key,
		title: field.title,
		validate: rule.validate,
		message: rule.message,
	}));
};

/**
 * 校验表单数据
 * @param formData 表单数据
 * @param fields 需要校验的字段数组
 * @returns 校验结果，通过返回null，不通过返回错误信息
 */
const validateForm = (
	formData: any,
	fields: string[]
): { message: string } | null => {
	for (const field of fields) {
		const rules = getFieldValidations(field);
		for (const rule of rules) {
			if (rule.validate(formData[field])) {
				return { message: rule.message };
			}
		}
	}
	return null;
};

export { IType, fieldsConfig, getFieldValidations, validateForm };
