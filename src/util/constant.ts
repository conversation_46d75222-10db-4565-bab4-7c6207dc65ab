/** 一些数据常量的定义 */

// 性别
const sexList = [
	{ code: 1, name: "女" },
	{ code: 2, name: "男" },
];

// 状态
const statusList = [
	{ code: 0, name: "禁用" },
	{ code: 1, name: "启用" },
];

// 排班状态
const schedulingStatusList = [
	{ code: "ON_DUTY", name: "上班" },
	{ code: "OFF_DUTY", name: "下班" },
];

// 接单模式
const takeOrdersModeList = [
	{ code: "AUTO", name: "自动接单" },
	{ code: "MANUAL", name: "手动接单" },
];

// 可预约时间段
const dayCanAppointmentTimeList = [
	{ code: "[9:00,12:00]", name: "[9:00,12:00]" },
];

// 职位
const positionList = ["店长", "店员", "手艺人"].map((item) => ({
	code: item,
	name: item,
}));

// 职位级别
const rankList = ["高级", "中级", "初级"].map((item) => ({
	code: item,
	name: item,
}));

export {
	sexList,
	statusList,
	schedulingStatusList,
	takeOrdersModeList,
	dayCanAppointmentTimeList,
	positionList,
	rankList,
};
