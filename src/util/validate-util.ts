import { CommonUtil } from "./common-util";

export const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phone) {
        return false
    }

    if (!phone || !phone.trim() || !phoneRegex.test(phone)) {
        return false;
    }

    return true
}

export const Validator: Record<string, Function> = {
    REQUIRED: (value): boolean => {
        if (CommonUtil.stringIsNull(value)) {
            return false
        }
        return true
    },
    PHONE_NUMBER: (phoneNumber: string): boolean => {
        if (CommonUtil.stringIsNull(phoneNumber)) {
            return false
        }
        if (!CommonUtil.isValidPhoneNumber(phoneNumber)) {
            return false
        }
        return true
    }
}

export const ValidatorKeys = Object.keys(Validator);

export const validateForm = (
    form: any,
    validator: {
        key: string,
        validateKey: string,
        onError: Function
    }[]
): boolean => {
    if (!form || !validator) {
        return false
    }
    let result = true;

    for (const valid of validator) {
        if (!Reflect.has(form, valid.key) || !Reflect.has(Validator, valid.validateKey)) {
            continue
        }

        const value = form[valid.key];
        const validateFn = Validator[valid.validateKey];

        if (!validateFn(value)) {
            valid.onError && valid.onError();
            result = false;
            break;
        } else {
            continue;
        }
    }

    return result;
}