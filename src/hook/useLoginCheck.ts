import { useEffect } from "react";
import Taro from "@tarojs/taro";
import useStore from "@/hook/store";
import { UserInfoStore } from "@/store/user-info.store";

const useLoginCheck = (callback: () => void) => {
	const userInfo: UserInfoStore = useStore().userInfoStore;

	useEffect(() => {
		if (!userInfo.isLogin) {
			Taro.showToast({
				title: "请先登录",
				icon: "none",
				duration: 2000,
			});
			Taro.navigateTo({
				url: "/view/page/tab/home/<USER>",
			});
		} else {
			callback();
		}
	}, [userInfo.isLogin, callback]);
};

export default useLoginCheck;
