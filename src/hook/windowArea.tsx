import Taro from "@tarojs/taro";
import { useEffect, useState } from "react"

// 获取窗口区域信息
const useWindowArea = () => {
    // 顶部安全区域高度
    const [topArea, setTopArea] = useState(0);
    // 底部安全区域高度
    const [bottomArea, setBottomArea] = useState(0);
    // 导航高度
    const [navArea, setNavArea] = useState(40);

    useEffect(() => {
        const systemInfo = Taro.getSystemInfoSync();

        // 计算小程序navBar高度
        if (process.env.TARO_ENV === "weapp") {
            try {
                if (systemInfo.safeArea?.bottom) {
                    setBottomArea(systemInfo.windowHeight - (systemInfo.safeArea?.bottom));
                }

                setTopArea(systemInfo.safeArea?.top || 0)
                const statusHeight = systemInfo.statusBarHeight || 0;
                // 获取胶囊按钮位置信息
                const menuButtonInfo = Taro.getMenuButtonBoundingClientRect();

                // 计算内容区高度 = 胶囊高度 + 上下边距
                if (menuButtonInfo && menuButtonInfo.height > 0) {
                    const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - statusHeight) * 2;
                    setNavArea(navBarHeight)
                }
            } catch (error) {
                console.error("获取胶囊按钮信息失败", error);
            }
        }
    }, [])

    return {
        topArea, bottomArea, navArea
    }
}

export default useWindowArea;