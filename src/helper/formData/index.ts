import Taro from "@tarojs/taro";
import mimeMap from "./mimeMap";
import FormData from "form-data";

function utf8CodeAt(str, i) {
	let out: any[] = [],
		p = 0;
	let c = str.charCodeAt(i);
	if (c < 128) {
		out[p++] = c;
	} else if (c < 2048) {
		out[p++] = (c >> 6) | 192;
		out[p++] = (c & 63) | 128;
	} else if (
		(c & 0xfc00) == 0xd800 &&
		i + 1 < str.length &&
		(str.charCodeAt(i + 1) & 0xfc00) == 0xdc00
	) {
		// Surrogate Pair
		c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);
		out[p++] = (c >> 18) | 240;
		out[p++] = ((c >> 12) & 63) | 128;
		out[p++] = ((c >> 6) & 63) | 128;
		out[p++] = (c & 63) | 128;
	} else {
		out[p++] = (c >> 12) | 224;
		out[p++] = ((c >> 6) & 63) | 128;
		out[p++] = (c & 63) | 128;
	}
	return out;
}

function toUtf8Bytes(str) {
	const bytes: any[] = [];
	for (let i = 0; i < str.length; i++) {
		bytes.push(...utf8CodeAt(str, i));
		if (str.codePointAt(i) > 0xffff) {
			i++;
		}
	}
	return bytes;
}

function WxFormData() {
	let data = {};
	let files: any[] = [];

	this.append = (name, value: string) => {
		// q: 正则判断value是否是文件路径还是普通字符串
		const isFilePath = /^(https?|file|blob):\/\//.test(value);
		if (!isFilePath) {
			data[name] = value;
		} else {
			const buffer = Taro.getFileSystemManager().readFileSync(value);
			if (
				Object.prototype.toString.call(buffer).indexOf("ArrayBuffer") <
				0
			) {
				return false;
			}

			const fileName = getFileNameFromPath(value);

			files.push({
				name: name,
				buffer: buffer,
				fileName: fileName,
			});
		}
		return true;
	};

	// this.appendFile = (name, path, fileName) => {
	// 	let buffer = fileManager.readFileSync(path);
	// 	if (Object.prototype.toString.call(buffer).indexOf("ArrayBuffer") < 0) {
	// 		return false;
	// 	}

	// 	if (!fileName) {
	// 		fileName = getFileNameFromPath(path);
	// 	}

	// 	files.push({
	// 		name: name,
	// 		buffer: buffer,
	// 		fileName: fileName,
	// 	});
	// 	return true;
	// };

	this.getData = () => convert(data, files);
}

function getFileNameFromPath(path) {
	let idx = path.lastIndexOf("/");
	return path.substr(idx + 1);
}

function convert(data, files) {
	let boundaryKey = "wxmpFormBoundary" + randString(); // 数据分割符，一般是随机的字符串
	let boundary = "--" + boundaryKey;
	let endBoundary = boundary + "--";

	let postArray = [];
	//拼接参数
	if (data && Object.prototype.toString.call(data) == "[object Object]") {
		for (let key in data) {
			postArray = postArray.concat(
				formDataArray(boundary, key, data[key])
			);
		}
	}
	//拼接文件
	if (files && Object.prototype.toString.call(files) == "[object Array]") {
		for (let i in files) {
			let file = files[i];
			postArray = postArray.concat(
				formDataArray(boundary, file.name, file.buffer, file.fileName)
			);
		}
	}
	//结尾
	let endBoundaryArray: any = [];
	endBoundaryArray.push(...toUtf8Bytes(endBoundary));
	postArray = postArray.concat(endBoundaryArray);
	return {
		contentType: "multipart/form-data; boundary=" + boundaryKey,
		buffer: new Uint8Array(postArray).buffer,
	};
}

function randString() {
	var result = "";
	var chars =
		"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	for (var i = 17; i > 0; --i)
		result += chars[Math.floor(Math.random() * chars.length)];
	return result;
}

function formDataArray(boundary, name, value, fileName) {
	let dataString = "";
	let isFile = !!fileName;

	dataString += boundary + "\r\n";
	dataString += 'Content-Disposition: form-data; name="' + name + '"';
	if (isFile) {
		dataString += '; filename="' + fileName + '"' + "\r\n";
		dataString += "Content-Type: " + getFileMime(fileName) + "\r\n\r\n";
	} else {
		dataString += "\r\n\r\n";
		dataString += value;
	}

	let dataArray: any[] = [];
	dataArray.push(...toUtf8Bytes(dataString));

	if (isFile) {
		let fileArray = new Uint8Array(value);
		dataArray = dataArray.concat(Array.prototype.slice.call(fileArray));
	}
	dataArray.push(...toUtf8Bytes("\r"));
	dataArray.push(...toUtf8Bytes("\n"));

	return dataArray;
}

function getFileMime(fileName) {
	let idx = fileName.lastIndexOf(".");
	let mime = mimeMap[fileName.substr(idx)];
	return mime ? mime : "application/octet-stream";
}

const PolyFillFormData =
	Taro.getEnv() === Taro.ENV_TYPE.WEAPP ? WxFormData : FormData;

export default PolyFillFormData;
