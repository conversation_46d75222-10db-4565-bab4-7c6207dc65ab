import { AxiosRequestConfig } from "axios";
import instance from "./instance";
import Taro from "@tarojs/taro";

async function request(
	url: string,
	config: AxiosRequestConfig & { requestType: string }
) {
	const loginInfo = Taro.getStorageSync("loginInfo") ?? {};
	if (config.requestType === "form" && isWeApp) {
		const params = config.data?.getData() ?? {};
		config.data = params?.buffer;
		(config.headers as any)["Content-Type"] = params?.contentType;
	}

	return instance.request.call(instance, url, {
		...config,
		headers: {
			...config.headers,
			Authorization: `Bearer ${loginInfo.token}`,
			Business_Id: String(loginInfo.businessId),
		},
	});
}

export default request as any as typeof instance.request;
