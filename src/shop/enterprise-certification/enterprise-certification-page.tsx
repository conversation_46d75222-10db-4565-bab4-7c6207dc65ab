import { View, Image, Picker } from "@tarojs/components";
import Taro from "@tarojs/taro";
import {
	AtInput,
	AtButton,
	AtList,
	AtListItem,
} from "taro-ui";
import styles from "./enterprise-certification-page.module.scss";
import { useState } from "react";

import NavBar from "@/view/component/nav-bar/nav-bar.component";

const Index: React.FC = () => {
	const [formData, setFormData] = useState({
		certType: "个体工商户",
		companyName: "",
		legalRepresentative: "",
		idNumber: "310091 1987 1209 0098",
		validityPeriod: "永久",
		expiryDate: "2056-09-12",
		licenseCode: "",
		bankName: "",
		bankAccount: "",
		businessNumber: "",
	});

	const [tempLicensePath, setTempLicensePath] = useState("");
	const [isOpened, setIsOpened] = useState(false);

	const certTypes = ["个体工商户", "企业", "其他组织"];
	const validityPeriods = ['永久', '固定期限'];

	const handleChange = (name: string, value: string | number) => {
		setFormData((prev) => ({
			...prev,
			[name]: String(value),
		}));
	};

	const handleCertTypeChange = (e) => {
		const value = certTypes[Number(e.detail.value)];
		handleChange("certType", value);
	};

	const handleValidityPeriodChange = (e) => {
		const value = validityPeriods[Number(e.detail.value)];
		handleChange("validityPeriod", value);
	};

	const handleDateChange = (e) => {
		handleChange("expiryDate", e.detail.value);
	};

	const handleUploadLicense = () => {
		Taro.chooseImage({
			count: 1,
			sizeType: ["compressed"],
			sourceType: ["album", "camera"],
			success: function (res) {
				const tempFilePath = res.tempFilePaths[0];
				setTempLicensePath(tempFilePath);
			},
			fail: function () {
				Taro.showToast({
					title: "选择图片失败",
					icon: "none",
				});
			},
		});
	};

	const handleSubmit = async () => {
		if (!tempLicensePath) {
			Taro.showToast({
				title: "请上传营业执照",
				icon: "none",
			});
			return;
		}

		try {
			Taro.showLoading({
				title: "正在提交",
				mask: true,
			});

			const submitData = {
				...formData,
				businessLicense: tempLicensePath,
			};

			// 提交表单数据
			// await service.business.shangjiaguanli.postEnterpriseCertification(submitData);

			Taro.hideLoading();
			Taro.navigateBack();
		} catch (error) {
			Taro.hideLoading();
			Taro.showToast({
				title: "提交失败，请重试",
				icon: "none",
			});
			console.error("提交失败:", error);
		}
	};

	return (
		<View className={styles.page}>
			<NavBar text="企业认证" />
			<View className={styles.section}>
				<View className={styles.sectionTitle}>上传证件</View>
				<View className={`${styles.formDiv}`}>
					<View className={styles.shopInput}>
						<Picker
							mode="selector"
							range={certTypes}
							onChange={handleCertTypeChange}
						>
							<AtList>
								<AtListItem
									title="证件类型"
									extraText={formData.certType}
								/>
							</AtList>
						</Picker>
					</View>
				</View>

				<View className={styles.uploadArea}>
					{tempLicensePath ? (
						<Image
							className={styles.licensePreview}
							src={tempLicensePath}
							mode="aspectFit"
						/>
					) : (
						<View className={styles.licenseImgDiv} onClick={handleUploadLicense}>
							<Image
								className={styles.licenseImg}
								src={require("@/assets/image/upload-license.png")}
							/>
						</View>
					)}{(tempLicensePath) && (
						<AtButton
							size='small'
							onClick={handleUploadLicense}
						>
							重新上传
						</AtButton>
					)}
				</View>

			</View>

			<View className={styles.section}>
				<View className={styles.sectionTitle}>资质认证</View>
				<View className={`${styles.formDiv}`}>
					<AtInput
						className={styles.licenseInput}
						name="companyName"
						title="公司名称"
						type="text"
						placeholder="请输入公司名称"
						value={formData.companyName}
						onChange={(value) => handleChange("companyName", value)}
					/>
					<AtInput
						className={styles.licenseInput}
						name="legalRepresentative"
						title="法人代表"
						type="text"
						placeholder="请输入法人姓名"
						value={formData.legalRepresentative}
						onChange={(value) =>
							handleChange("legalRepresentative", value)
						}
					/>
					<AtInput
						className={styles.licenseInput}
						name="idNumber"
						title="身份证号"
						type="text"
						value={formData.idNumber}
						onChange={(value) => handleChange("idNumber", value)}
					/>

					<Picker
						mode="selector"
						className={styles.licensePicker}
						range={validityPeriods}
						onChange={handleValidityPeriodChange}
					>
						<AtList>
							<AtListItem
								title="有效期限"
								extraText={formData.validityPeriod}
							/>
						</AtList>
					</Picker>

					{formData.validityPeriod === '固定期限' && (
						<Picker mode='date' onChange={handleDateChange} value={formData.expiryDate}>
							<AtList>
								<AtListItem title='请选择日期' extraText={formData.expiryDate} />
							</AtList>
						</Picker>
					)}
				</View>

				<AtInput className={styles.licenseInput}
					name="licenseCode"
					title="证件编码"
					type="text"
					placeholder="请输入统一社会信用代码"
					value={formData.licenseCode}
					onChange={(value) => handleChange("licenseCode", value)}
				/>
			</View>

			<View className={styles.section}>
				<View className={styles.sectionTitle}>银行信息</View>
				<AtInput
					name="bankName"
					title="开户银行"
					type="text"
					placeholder="请输入开户银行"
					value={formData.bankName}
					onChange={(value) => handleChange("bankName", value)}
				/>
				<AtInput
					name="bankAccount"
					title="银行卡号"
					type="text"
					placeholder="请输入开户卡号"
					value={formData.bankAccount}
					onChange={(value) => handleChange("bankAccount", value)}
				/>
				<AtInput
					name="businessNumber"
					title="企业编号"
					type="text"
					placeholder="请输入企业编号"
					value={formData.businessNumber}
					onChange={(value) => handleChange("businessNumber", value)}
				/>
			</View>

			<View className={styles.submitBtnDiv}>
				<AtButton
					className={styles.submitBtn}
					type="primary"
					onClick={handleSubmit}
				>
					提交
				</AtButton>
			</View>
		</View>
	);
};

export default Index;
