@import "~taro-ui/dist/style/components/form.scss";

.page {
  min-height: 100vh;
  background-color: #f5f5f5;

  .section {
    margin: 0 10px 8px 10px;
    background-color: white;
    border-radius: 8px;

    .sectionTitle {
      font-size: 16px;
      line-height: 40px;
      font-weight: bold;
      color: #1F1F1F;
      padding-left: 13px;
    }

    .formDiv {
      font-size: 16px;

      .shopInput {
        height: 50px;
        margin-bottom: 0;
        padding-left: 3px;
        border: none;
      }

      .licenseInput {
        height: 50px;
        margin-bottom: 0;
      }

      .licensePicker {
        padding-left: 3px;
      }

      :global {
        .at-list::after {
          border: none;
        }
      }
    }
  }

  .submitBtnDiv {
    margin-top: 29px;
    background-color: white;
    padding-bottom: 34px;
    padding-top: 7px;
    padding-left: 17px;
    padding-right: 17px;
    font-size: 14px;

    .submitBtn {
      border-radius: 20px;
    }
  }
}

.uploadArea {
  margin: 10px;
  .licenseImg{
    width: 100%;
    height: 212px;
  }
}