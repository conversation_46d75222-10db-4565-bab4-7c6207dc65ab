@use "@/theme.scss" as t;

.container {
	.header {
		justify-content: flex-start;
		box-sizing: border-box;
		padding: 0px 16px;
		background-color: #fff;
		@extend %display-flex-row;

		.logo {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}

		.name {
			color: #1f1f1f;
			font-weight: 500;
			font-size: 15px;
		}

		.arrow {
			width: 8px;
			height: 6px;
			margin-left: 4px;
		}
	}

	.tabs {
		.tabContent {
			width: 100%;
			padding: 10px;
			overflow-y: auto;
			background: #f6f6f6;
		}
		.card {
			@extend %card;
		}
		.tabContent {
			padding: 10px;
			overflow-y: auto;
			background: #f6f6f6;
		}
		.card {
			@extend %card;

			.title {
				@extend %display-flex-row;
				padding-bottom: 16px;
				color: #999999;
				font-size: 14px;


				.subTitle {
					color: #666666;
					font-weight: 400;
					font-size: 12px;
				}
			}

			.infoItem {
				display: flex;
				padding-bottom: 16px;

				.label {
					width: 100px;
					color: #999999;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
				}

				.value {
					color: #333333;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
				}
			}

			.infoItem:last-child {
				padding-bottom: 0px;
			}
			&:last-child {
				margin-bottom: 40px;
			}
		}

		.shopInfo {
			.images {
				@extend %display-flex-row;
				justify-content: flex-start;
				width: calc(100% - 100px);

				.image {
					width: 60px;
					height: 60px;
					margin-right: 10px;
					border-radius: 4px;
				}

				.total {
					padding: 2px 3px;
					color: #999999;
					font-size: 13px;
					background: #efefef;
					border-radius: 4px;
				}
			}
		}

		.licenseInfo {
			.images {
				@extend %display-flex-row;
				.licenseImage {
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
					border: 1px solid t.$border-color;
					border-radius: 12px;

					.image {
						width: 100%;
						height: 200px;
						border-radius: 12px;
					}

					.updateImage {
						position: absolute;
						bottom: 0px;
						justify-content: center;
						width: 100%;
						height: 27px;
						color: #fff;
						font-size: 12px;
						background: rgba(0, 0, 0, 0.6);
						border-radius: 0px 0px 10px 10px;
						border-radius: 0px 0px 12px 12px;
						@extend %display-flex-row;

						.refresh {
							width: 12px;
							height: 12px;
							margin-right: 2px;
						}
					}
				}
			}

			.licenseInfoItem {
				display: flex;
				align-items: center;
				padding: 12px 0px;
				border-bottom: 1px solid t.$border-color;

				&:last-child {
					padding-bottom: 0px;
					border-bottom: none;
				}

				.label {
					@extend %display-flex-row;
					justify-content: flex-start;
					width: 80px;
					color: #999999;
					font-weight: 400;
					font-size: 14px;

					.required {
						margin-right: 4px;
						color: #ff4d4f;
					}
				}

				.value {
					color: #333333;
					font-size: 14px;
				}

				.validity {
					@extend %display-flex-row;
					align-items: center;
					justify-content: flex-end;
					width: calc(100% - 80px);

					.validityOption {
						box-sizing: border-box;
						width: 72px;
						height: 22px;
						margin-left: 8px;
						color: #333333;
						font-weight: 400;
						font-size: 13px;
						line-height: 22px;
						text-align: center;
						border: 1px solid #e0e0e0;
						border-radius: 4px;
					}

					.validityOptionActive {
						color: #fdd244;
						background: rgba(253, 210, 68, 0.09);
						border: 1px solid #fdd244;
					}
				}
			}
		}
	}

	.obts {
		@extend %bottom-btn;
	}
}
