import React, { useState, useRef, useEffect, useCallback } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image } from "@tarojs/components";
import { AtTabs, AtTabsPane, AtButton } from "taro-ui";
import { cloneDeep } from "lodash";
import dayjs from 'dayjs';
import PageWithNav, {
	PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import ShopIcon from "@/assets/image/common/shop-info.png";
import BottomArrow from "@/assets/image/common/bottom-arrow.png";
import refreshImg from "@/assets/image/common/refresh.png";
import styles from "./shop-management-page.module.scss";
import { getShopLicense } from '@/service/business/shangjiaruzhudianpuguanli';

// 店铺信息配置
const shopInfoConfig = [
	{
		title: "基础信息",
		items: [
			{ label: "店铺名称", key: "businessName" },
			{ label: "店铺编号", key: "businessId" },
			{ label: "店铺分类", key: "businessCategory" },
			{ label: "区域", key: "region" },
			{ label: "详细地址", key: "address" },
			{ label: "联系人", key: "contactUser" },
			{ label: "联系电话", key: "contactPhone" },
		],
	},
	{
		title: "营业信息",
		items: [
			{ label: "营业状态", key: "businessStatus" },
			{ label: "营业时间", key: "businessHours" },
			{ label: "店铺描述", key: "businessDesc" },
		],
	},
];

// 资质信息配置
const licenseConfig = [
	{
		title: "营业执照",
		subTitle: "个体工商户营业执照",
		images: [{ key: "licenseImage", label: "营业执照" }],
		items: [
			{ label: "执照名称", key: "companyName", required: true },
			{ label: "法人代表", key: "legalPerson", required: true },
			{
				label: "统一社会信用代码",
				key: "unifiedCode",
				required: true,
			},
			{
				label: "有效期",
				key: "validity",
				required: true,
				isValidity: true,
			},
		],
	},
	{
		title: "法人信息",
		subTitle: "身份证",
		images: [
			{ key: "legalPersonFrontImage", label: "身份证正面" },
			{ key: "legalPersonBackImage", label: "身份证反面" },
		],
		items: [
			{ label: "姓名", key: "legalPersonName" },
			{ label: "证件号", key: "legalPersonInfo.legalPersonIdNumber" },
			{ label: "有效期", key: "legalPersonInfo.legalPersonValidity" },
			{ label: "手机号", key: "legalPersonPhone" },
		],
	},
];

// tabs
const tabList = [
	{ title: "店铺信息", configs: shopInfoConfig },
	{ title: "资质信息", configs: licenseConfig },
];

const headerHeight = 42; // 店铺信息头部高度
const tabBarHeight = 50; // tabBar高度
const bottomOptsHeight = 50; // 底部操作栏高度

interface ShopInfo {
	businessName: string;
	businessId: number;
	businessCategory: string;
	region: string;
	address: string;
	contactUser: string;
	contactPhone: string;
	businessStatus: string;
	businessStartHours: string;
	businessEndHours: string;
	businessDesc: string;
	logo: string;
	images: string[];
	weChatPaymentCodeQR: string;
	aliPaymentCodeQR: string;
	referralCode: string;
	qrcodeUrl: string;
}

interface LicenseInfo {
	licenseId?: number;
	businessId?: number;
	licenseType?: string;
	licenseIdentifier?: string;
	licenseImageUrl?: string;
	licenseName?: string;
	licenseValidityType?: number;
	licenseValidityDate?: string;
	enterpriseName?: string;
	legalPerson?: string;
	legalPersonIdentifier?: string;
	legalPersonIdentifierFront?: string;
	legalPersonIdentifierBack?: string;
	legalPersonIdentifierValidityType?: number;
	legalPersonIdentifierValidityDate?: string;
	depositBank?: string;
	bankAccount?: string;
	enterpriseTaxNum?: string;
}

const initShopInfo: ShopInfo = {
	businessName: "",
	businessId: 0,
	businessCategory: "",
	region: "",
	address: "",
	contactUser: "",
	contactPhone: "",
	businessStatus: "",
	businessStartHours: "",
	businessEndHours: "",
	businessDesc: "",
	logo: "",
	images: [],
	weChatPaymentCodeQR: "",
	aliPaymentCodeQR: "",
	referralCode: "",
	qrcodeUrl: "",
};

const initLicenseInfo: LicenseInfo = {
	licenseId: undefined,
	businessId: undefined,
	licenseType: '',
	licenseIdentifier: '',
	licenseImageUrl: '',
	licenseName: '',
	licenseValidityType: undefined,
	licenseValidityDate: '',
	enterpriseName: '',
	legalPerson: '',
	legalPersonIdentifier: '',
	legalPersonIdentifierFront: '',
	legalPersonIdentifierBack: '',
	legalPersonIdentifierValidityType: undefined,
	legalPersonIdentifierValidityDate: '',
	depositBank: '',
	bankAccount: '',
	enterpriseTaxNum: ''
};

// 营业状态映射
const businessStatusMap = {
	1: '正常营业',
	2: '停业',
	3: '注销'
};

const formatData = <T extends Record<string, any>>(
	initData: T,
	responseData: Partial<T>
): T => {
	const result = { ...initData };
	Object.keys(initData).forEach((key) => {
		const typedKey = key as keyof T;
		if (typedKey in responseData) {
			result[typedKey] = responseData[typedKey] ?? initData[typedKey];
		}
	});
	return result;
};

const ShopManagementPage: React.FC = () => {
	const container: React.RefObject<PageContainerRef> = useRef(null);
	const [listHeight, setListHeight] = useState("calc(100vh - 42px)");
	const [current, setCurrent] = useState(0);
	const [{ shopInfo, licenseInfo }, setState] = useState({
		shopInfo: cloneDeep(initShopInfo),
		licenseInfo: cloneDeep(initLicenseInfo),
	});

	/** 获取店铺信息 */
	const fetchShopInfo = useCallback(async () => {
		try {
			const res = await service.business.businessController.getShopBusinessId() as BUSINESS.BusinessVO;
			console.log(
				"获取店铺信息成功:",
				res,
				formatData(initShopInfo, res),
				formatData(initLicenseInfo, {})
			);
			setState({
				shopInfo: formatData(initShopInfo, res),
				licenseInfo: initLicenseInfo,
			});
		} catch (error) {
			console.error("获取店铺信息失败:", error);
			Taro.showToast({
				title: "获取店铺信息失败",
				icon: "none",
			});
		}
	}, []);

	/** 获取资质信息 */
	const fetchLicenseInfo = useCallback(async () => {
		if (!shopInfo.businessId) {
			return;
		}
		try {
			const res = await getShopLicense({ businessId: shopInfo.businessId }) as BUSINESS.RBusinessLicense;
			if (res) {
				console.log("获取资质信息成功:", res);
				setState(prev => ({
					...prev,
					licenseInfo: formatData(initLicenseInfo, res as Partial<LicenseInfo>)
				}));
			}
		} catch (error) {
			console.error("获取资质信息失败:", error);
			Taro.showToast({
				title: "获取资质信息失败",
				icon: "none",
			});
		}
	}, [shopInfo.businessId]);

	// 组件加载时调用
	useEffect(() => {
		fetchShopInfo();
		fetchLicenseInfo();
	}, [fetchShopInfo, fetchLicenseInfo]);

	const handleHeightChange = (navHeight: number) => {
		const height =
			navHeight + headerHeight + tabBarHeight + bottomOptsHeight;
		setListHeight(`calc(100vh - ${height}px)`);
	};

	const handleTabClick = (value) => {
		setCurrent(value);
	};

	const handleEditShop = () => {
		Taro.navigateTo({
			url: '/shop/shop-edit/shop-edit-page'
		});
	};

	// 获取嵌套对象属性值的辅助函数
	const getNestedValue = (obj, path) => {
		return path.split(".").reduce((prev, curr) => {
			return prev ? prev[curr] : null;
		}, obj);
	};

	// 格式化日期
	const formatDate = (date: string) => {
		if (!date) return '-';
		return dayjs(date).format('YYYY-MM-DD');
	};

	// 渲染店铺信息部分
	const renderShopInfo = () => {
		return (
			<View key="shopInfo" className={styles.shopInfo}>
				{shopInfoConfig.map((obj, sectionIndex) => (
					<View key={sectionIndex} className={styles.card}>
						<View className={styles.title}>{obj.title}</View>

						{obj.items.map((item, itemIndex) => (
							<View key={itemIndex} className={styles.infoItem}>
								<Text className={styles.label}>
									{item.label}
								</Text>
								<Text className={styles.value}>
									{item.key === 'businessStatus' 
										? businessStatusMap[shopInfo[item.key]] || '未知状态'
										: shopInfo[item.key]}
								</Text>
							</View>
						))}
					</View>
				))}

				<View className={styles.card}>
					<View className={styles.title}>店铺头图/相册</View>

					<View className={styles.infoItem}>
						<Text className={styles.label}>外显小图</Text>
						<View className={styles.images}>
							<Image
								className={styles.image}
								mode="aspectFill"
								src={shopInfo.logo}
							/>
						</View>
					</View>

					<View className={styles.infoItem}>
						<Text className={styles.label}>头图及相册</Text>
						<View className={styles.images}>
							{shopInfo.images.map((img, index) => (
								<Image
									key={index}
									className={styles.image}
									src={img}
									mode="aspectFill"
								/>
							))}
						</View>
					</View>
				</View>
			</View>
		);
	};

	// 渲染资质信息部分
	const renderLicenseInfo = () => {
		return (
			<View key="licenseInfo" className={styles.licenseInfo}>
				{licenseConfig.map((obj, sectionIndex) => (
					<View key={sectionIndex} className={styles.card}>
						<View className={styles.title}>
							<Text>{obj.title}</Text>
							{obj.subTitle && (
								<Text className={styles.subTitle}>
									{obj.subTitle}
								</Text>
							)}
						</View>

						{/* 多张图片 */}
						{obj.images && (
							<View className={`${styles.images}`}>
								{obj.images.map((img, imgIndex) => (
									<View
										key={imgIndex}
										className={styles.licenseImage}
										style={{
											width:
												obj.images?.length === 1
													? "100%"
													: "calc(50% - 8px)",
											height:
												obj.images?.length === 1
													? "200px"
													: "100px",
										}}
									>
										<Image
											className={styles.image}
											src={getNestedValue(
												licenseInfo,
												img.key === 'licenseImage' ? 'licenseImageUrl' : 
												img.key === 'legalPersonFrontImage' ? 'legalPersonIdentifierFront' :
												img.key === 'legalPersonBackImage' ? 'legalPersonIdentifierBack' : img.key
											)}
											mode="aspectFill"
										/>
									</View>
								))}
							</View>
						)}

						{obj.items.map((item, itemIndex) => (
							<View
								key={itemIndex}
								className={styles.licenseInfoItem}
							>
								<Text className={styles.label}>
									<Text>{item.label}</Text>
									{item.required && (
										<Text className={styles.required}>
											*
										</Text>
									)}
								</Text>

								{item.isValidity ? (
									<View className={styles.validity}>
										<View
											className={`${styles.validityOption} ${licenseInfo.licenseValidityType === 0 ? styles.validityOptionActive : ''}`}
										>
											<Text>永久有效</Text>
										</View>
										<View 
											className={`${styles.validityOption} ${licenseInfo.licenseValidityType === 1 ? styles.validityOptionActive : ''}`}
										>
											<Text>截止日期</Text>
										</View>
									</View>
								) : (
									<Text className={styles.value}>
										{item.key === 'validity' || item.key === 'legalPersonInfo.legalPersonValidity' 
											? formatDate(getNestedValue(licenseInfo, item.key === 'validity' ? 'licenseValidityDate' : 'legalPersonIdentifierValidityDate'))
											: getNestedValue(licenseInfo, item.key === 'companyName' ? 'enterpriseName' :
												item.key === 'legalPerson' ? 'legalPerson' :
												item.key === 'unifiedCode' ? 'licenseIdentifier' :
												item.key === 'legalPersonName' ? 'legalPerson' :
												item.key === 'legalPersonInfo.legalPersonIdNumber' ? 'legalPersonIdentifier' :
												item.key === 'legalPersonPhone' ? 'legalPersonPhone' : item.key) || '-'}
									</Text>
								)}
							</View>
						))}
					</View>
				))}
			</View>
		);
	};

	return (
		<PageWithNav
			showNavBar
			title="店铺管理"
			containerRef={container}
			containerClassName={styles.container}
			onHeightChange={handleHeightChange}
		>
			<View className={styles.container}>
				<View
					className={styles.header}
					style={{ height: headerHeight }}
				>
					<Image className={styles.logo} src={ShopIcon} />
					<Text className={styles.name}>{shopInfo.businessName}</Text>
					{/* <Image className={styles.arrow} src={BottomArrow} /> */}
				</View>

				<AtTabs
					current={current}
					tabList={tabList}
					onClick={handleTabClick}
					className={styles.tabs}
					style={{ height: `calc(100vh - ${headerHeight}px)` }}
				>
					{tabList.map((item, index) => (
						<AtTabsPane
							current={current}
							index={index}
							className={styles.tabContent}
							style={{ height: tabBarHeight }}
						>
							<View
								style={{ height: listHeight }}
								key={item.title}
							>
								{current === 0
									? renderShopInfo()
									: renderLicenseInfo()}
							</View>
						</AtTabsPane>
					))}
				</AtTabs>

				<View
					className={styles.obts}
					style={{ height: bottomOptsHeight }}
				>
					<AtButton type="primary" onClick={handleEditShop} circle>
					前往店铺装修编辑
					</AtButton>
				</View>
			</View>
		</PageWithNav>
	);
};

export default ShopManagementPage;
