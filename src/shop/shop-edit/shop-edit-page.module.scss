@use "@/theme.scss" as t;

.wrapper {
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.container {
	background: #f7f8fa;
	flex: 1;
	overflow-y: auto;
	padding-bottom: 55px;
}

.card {
	background: #fff;
	border-radius: 14px;
	margin: 16px 12px 0 12px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.03);
	padding: 12px;
}

.cardTitle {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 8px;
}

.cardDesc {
	font-size: 12px;
	color: #b4b4b4;
	margin-bottom: 8px;
}

.formItem {
	display: flex;
	align-items: center;
	padding: 0px;
	min-height: 52px;
	border-bottom: 1px solid #f0f0f0;
	position: relative;
	background: #fff;
	font-size: 14px;
}
.formItem:last-child {
	border-bottom: none;
}

.label {
	font-size: 15px;
	color: #222;
	font-weight: 500;
	flex: 0 0 90px;
	display: flex;
	align-items: center;
}

.required {
	color: #ff4d4f;
	margin-left: 2px;
	font-size: 14px;
}

.inputWrap {
	flex: 1;
	display: flex;
	align-items: center;
}

.input,
.taro-input {
	background: transparent;
	border: none;
	outline: none;
	font-size: 15px;
	color: #222;
	width: 100%;
	padding: 0;
}

.desc {
	font-size: 13px;
	color: #b4b4b4;
	margin-left: 6px;
}

.selectRow {
	flex: 1;
	min-height: 44px;
	display: flex;
	align-items: center;
	color: #222;
	font-size: 15px;
	cursor: pointer;
	justify-content: space-between;
}
.selectPlaceholder {
	color: #b4b4b4;
}
.arrow {
	margin-left: 6px;
	font-size: 16px;
	color: #b4b4b4;
}

.imgList {
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
	margin-top: 12px;
	width: 100%;
}

.imgItem {
	position: relative;
	width: 96px;
	height: 96px;
	border-radius: 8px;
	overflow: hidden;
	background: #f7f7f7;
	box-shadow: 0 2px 8px rgba(0,0,0,0.03);
	display: flex;
	align-items: center;
	justify-content: center;
}

.img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 8px;
}

.close {
	position: absolute;
	top: 2px;
	right: 2px;
	width: 20px;
	height: 20px;
	background: rgba(0,0,0,0.5);
	color: #fff;
	border-radius: 50%;
	text-align: center;
	line-height: 20px;
	font-size: 16px;
	z-index: 2;
	cursor: pointer;
	transition: background 0.2s;
	&:hover {
		background: rgba(0,0,0,0.7);
	}
}

.addBtn {
	width: 100%;
	height: 96px;
	border: 1px dashed #d9d9d9;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #b4b4b4;
	font-size: 16px;
	background: #fafafa;
	cursor: pointer;
	transition: border 0.2s, color 0.2s;
	margin-left: 0;
	&:hover {
		border: 1px solid #1890ff;
		color: #1890ff;
	}
}

.tip {
	margin-top: 8px;
	font-size: 12px;
	color: #b4b4b4;
}

.submitBtn {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background: #fff;
	padding: 12px 16px 24px 16px;
	box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
	z-index: 10;
	display: flex;
	justify-content: center;
}

.at-button--primary {
	width: 100%;
	font-size: 18px;
	border-radius: 24px;
	height: 48px;
	font-weight: 600;
}

.mapBlock {
	padding: 12px 0 18px 0;
}
.mapLabel {
	font-size: 15px;
	color: #222;
	font-weight: 500;
	margin-bottom: 4px;
}
.mapDesc {
	font-size: 12px;
	color: #b4b4b4;
	margin-bottom: 8px;
}
.mapWrap {
	border-radius: 12px;
	overflow: hidden;
	background: #f5f5f7;
}
.map {
	width: 100%;
	height: 120px;
	border-radius: 12px;
	background: #f5f5f7;
	border: none;
}

.taro-tabs__item--active {
	color: #ffb300 !important;
	font-weight: bold;
}

// 针对头图及门店相册区域优化
.formItem.imgUploadRow {
	display: flex;
	align-items: flex-start;
	padding: 0 18px;
	min-height: 52px;
	border-bottom: none;
	background: #fff;
	margin-bottom: 0;
}

.label.imgLabel {
	font-size: 15px;
	color: #222;
	font-weight: 500;
	flex: 0 0 90px;
	display: flex;
	align-items: center;
	margin-right: 8px;
	margin-top: 8px;
}

.imgUploadCol {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: flex-start;
}

.imgUploadBtnWrap {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-bottom: 4px;
}

.addBtn {
	width: 100%;
	// max-width: 220px;
	height: 40px;
	border: 1.5px dashed #d9d9d9;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #b4b4b4;
	font-size: 12px;
	background: #fafafa;
	cursor: pointer;
	transition: border 0.2s, color 0.2s;
	margin-left: auto;
	margin-right: 0;
	margin-top: 0;
	margin-bottom: 0;
	&:hover {
		border: 1.5px solid #1890ff;
		color: #1890ff;
	}
}

.imgUploadTip {
	font-size: 12px;
	color: #b4b4b4;
	margin-top: 0;
	margin-left: 0;
	margin-bottom: 0;
	text-align: left;
	line-height: 1.5;
}

.imgWrap {
	width: 100%;
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	flex-direction: column;
}

.label1 {
	font-size: 15px;
	color: #222;
	font-weight: 500;
}

.submitBtn1 {
	width: 100%;
}