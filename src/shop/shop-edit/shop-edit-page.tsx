import React, { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Input, Map, Image, ScrollView } from "@tarojs/components";
import { AtButton, AtTabs, AtTabsPane } from "taro-ui";
import Form, { Field, useForm } from "rc-field-form";
import { getShop, putShop } from "@/service/business/shangjiaruzhudianpuguanli";
import { getShopProjectAllCategory } from "@/service/business/shangjiaxiangmuguanli";
import styles from "./shop-edit-page.module.scss";
import CategoryPicker from "./CategoryPicker";
import BasicPage from "@/view/component/basic-page/basic-page.components";
import {
	getRegionCity,
	getRegionDistrict,
	getRegionProvince,
} from "@/service/system/sysRegionController";

const albumTabs = [{ title: "全部" }, { title: "环境" }, { title: "其他" }];

function isLocalImage(path) {
  return path.startsWith('wxfile://') || 
         path.startsWith('http://tmp/') || 
         path.startsWith('https://tmp/') ||
         /^\.\/|\.\.\//.test(path); // 相对路径
}

const ShopEditPage: React.FC = () => {
	const [form] = useForm();
	const [categoryOptions, setCategoryOptions] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);
	const [isH5, setIsH5] = useState(false);
	const [pickerVisible, setPickerVisible] = useState(false);
	const [categoryValue, setCategoryValue] = useState<string[]>([]);
	const [categoryLabel, setCategoryLabel] = useState<string[]>([]);
	const [albumTab, setAlbumTab] = useState(0);
	const [headerImages, setHeaderImages] = useState<string[]>([]);
	const [albumImages, setAlbumImages] = useState<string[]>([]);

	// 格式化品类
	const formatCategoryOptions = (list: any[]): any[] =>
		list.map((item) => ({
			label: item.categoryName,
			value: String(item.categoryId),
			children: item.children
				? formatCategoryOptions(item.children)
				: undefined,
		}));

	// 递归查找路径
	const findCategoryPath = (options, value, path = []) => {
		for (const opt of options) {
			if (opt.value === value) return [...path, opt];
			if (opt.children) {
				const res = findCategoryPath(opt.children, value, [
					...path,
					opt,
				]);
				if (res) return res;
			}
		}
		return null;
	};

	// 初始化
	useEffect(() => {
		setIsH5(process.env.TARO_ENV === "h5");
		getShop().then((res) => {
			if (res) {
				const info = res;
				form.setFieldsValue({
					...info,
					location: {
						latitude: info?.latitude,
						longitude: info?.longitude,
					},
					businessHours:
						info.businessStartHours && info.businessEndHours
							? `${info.businessStartHours}-${info.businessEndHours}`
							: "",
				});
				// 回显门店品类
				if (info.businessCategory) {
					const path =
						findCategoryPath(
							categoryOptions,
							info.businessCategory
						) || [];
					setCategoryValue(path.map((i) => i.value));
					setCategoryLabel(path.map((i) => i.label));
				}
				// 回显相册
				if (info?.albumImages) setAlbumImages(info?.albumImages);
				// 回显 logo
				if (info?.logo) {
					const logoArray = Array.isArray(info.logo) ? info.logo : [info.logo];
					setHeaderImages(logoArray.filter(Boolean));
					form.setFieldsValue({ logo: logoArray.filter(Boolean) });
				}
			}
		});
		getShopProjectAllCategory().then((res) => {
			const opts = formatCategoryOptions(res || []);
			setCategoryOptions(opts);
		});
	}, []);

	// 选择地图位置
	const handleSelectLocation = () => {
		if (isH5) {
			Taro.showModal({
				title: "提示",
				content: "H5暂不支持地图选点，请手动输入坐标",
				showCancel: false,
			});
		} else {
			Taro.chooseLocation({
				success: (res) => {
					form.setFieldsValue({
						location: {
							latitude: res.latitude,
							longitude: res.longitude,
						},
						address: res.address,
					});
				},
			});
		}
	};

	// 头图上传
	const handleUploadHeaderImage = async () => {
		const res = await Taro.chooseImage({ count: 3 - headerImages.length });
		setHeaderImages([...headerImages, ...res.tempFilePaths].slice(0, 3));
		form.setFieldsValue({
			logo: [...headerImages, ...res.tempFilePaths].slice(0, 3),
		});
	};

	// 头图删除
	const handleDeleteHeaderImage = (idx: number) => {
		const imgs = headerImages.filter((_, i) => i !== idx);
		setHeaderImages(imgs);
		form.setFieldsValue({ logo: imgs });
	};

	// 商家相册上传
	const handleUploadAlbumImage = async () => {
		const res = await Taro.chooseImage({ count: 9 - albumImages.length });
		setAlbumImages([...albumImages, ...res.tempFilePaths].slice(0, 9));
	};

	// 商家相册删除
	const handleDeleteAlbumImage = (idx: number) => {
		setAlbumImages(albumImages.filter((_, i) => i !== idx));
	};

	// 提交
	const handleSubmit = async () => {
		try {
			const values = await form.validateFields();
			setLoading(true);
			// 营业时间分割
			let businessStartHours = "";
			let businessEndHours = "";
			if (values.businessHours && values.businessHours.includes("-")) {
				[businessStartHours, businessEndHours] =
					values.businessHours.split("-");
			}
			console.log(values, headerImages, albumImages, 5555);
			const logImg = headerImages[0] || "";
			await putShop(
				{
					...values,
					businessCategory: values.businessCategory,
					businessCategoryLabel: categoryLabel.join("/"),
					latitude: values.location?.latitude || 0,
					longitude: values.location?.longitude || 0,
					businessStartHours,
					businessEndHours,
					// logo: headerImages[0] || "",
				},
				!isLocalImage(logImg) ? undefined : logImg as any
			);
			Taro.showToast({ title: "保存成功", icon: "success" });
			setTimeout(() => Taro.navigateBack(), 1200);
		} catch (e) {
			console.log(e, 3444);
			Taro.showToast({ title: "请检查表单", icon: "none" });
		} finally {
			setLoading(false);
		}
	};

	// 打开弹窗
	const openCategoryPicker = () => setPickerVisible(true);

	// 确认选择
	const handleCategoryConfirm = (valueArr, labelArr) => {
		setPickerVisible(false);
		setCategoryValue(valueArr);
		setCategoryLabel(labelArr);
		form.setFieldsValue({
			businessCategory: valueArr[valueArr.length - 1],
			businessCategoryLabel: labelArr.join("/"),
		});
	};

	return (
		<BasicPage title="店铺管理" bottomSafe>
			<View className={styles.wrapper}>
				<ScrollView className={styles.container} scrollY>
					<Form form={form}>
						{/* 店铺信息卡片 */}
						<View className={styles.card}>
							<View className={styles.cardTitle}>店铺信息</View>
							<View className={styles.cardDesc}>
								为保证审核通过，店铺名称务必喝门店招牌上的名称一致
							</View>
							<Field
								name="businessName"
								rules={[
									{
										required: true,
										message: "请输入店铺名称",
									},
								]}
							>
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											店铺名称
											<Text className={styles.required}>
												*
											</Text>
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="请输入"
												maxlength={50}
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
											<Text className={styles.desc}>
												{value?.length || 0}/50
											</Text>
										</View>
									</View>
								)}
							</Field>
							<Field name="remark">
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											编号备注
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="请输入内容"
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
										</View>
									</View>
								)}
							</Field>
							<Field
								name="businessCategory"
								rules={[
									{
										required: true,
										message: "请选择门店品类",
									},
								]}
							>
								{({ value }) => (
									<View
										className={styles.formItem}
										onClick={openCategoryPicker}
									>
										<Text className={styles.label}>
											门店品类
											<Text className={styles.required}>
												*
											</Text>
										</Text>
										<View className={styles.selectRow}>
											<Text
												className={
													value
														? ""
														: styles.selectPlaceholder
												}
											>
												{categoryLabel.length
													? categoryLabel.join(" / ")
													: "请选择门店品类"}
											</Text>
											<Text className={styles.arrow}>
												›
											</Text>
										</View>
									</View>
								)}
							</Field>
							<Field
								name="region"
								rules={[
									{ required: true, message: "请输入地区" },
								]}
							>
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											所在地区
											<Text className={styles.required}>
												*
											</Text>
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="请输入"
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
										</View>
									</View>
								)}
							</Field>
							<Field
								name="address"
								rules={[
									{
										required: true,
										message: "请输入详细地址",
									},
								]}
							>
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											详细地址
											<Text className={styles.required}>
												*
											</Text>
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="请输入"
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
										</View>
									</View>
								)}
							</Field>
							{/* 地图位置 */}
							<View className={styles.mapBlock}>
								<View className={styles.mapLabel}>
									地图位置
								</View>
								<View className={styles.mapDesc}>
									位置有误直接影响客户到店，请仔细确认。
								</View>
								<View
									className={styles.mapWrap}
									onClick={handleSelectLocation}
								>
									<Map
										longitude={
											form.getFieldValue("location")
												?.longitude || 0
										}
										latitude={
											form.getFieldValue("location")
												?.latitude || 0
										}
										className={styles.map}
										markers={
											form.getFieldValue("location")
												? [
														{
															id: 1,
															longitude:
																form.getFieldValue(
																	"location"
																).longitude,
															latitude:
																form.getFieldValue(
																	"location"
																).latitude,
															iconPath:
																"https://img.yzcdn.cn/vant/marker.png",
															width: 24,
															height: 32,
														},
												  ]
												: []
										}
									/>
								</View>
							</View>
							<Field name="businessHours">
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											营业时间
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="如10:00-23:00"
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
										</View>
									</View>
								)}
							</Field>
							<Field name="contactPhone">
								{({ value, onChange }) => (
									<View className={styles.formItem}>
										<Text className={styles.label}>
											营业电话
										</Text>
										<View className={styles.inputWrap}>
											<Input
												className={styles.input}
												value={value}
												placeholder="请输入"
												onInput={(e) =>
													onChange(e.detail.value)
												}
											/>
										</View>
									</View>
								)}
							</Field>
						</View>

						{/* 头图及门店相册 */}
						<View className={styles.card}>
							<View className={styles.cardTitle}>
								头图及门店相册
							</View>
							<View className={styles.formItem}>
								<View className={styles.imgWrap}>
									<Text className={styles.label1}>
										门店头图
										<Text className={styles.required}>
											*
										</Text>
									</Text>
									<Text className={styles.tip}>
										头图将会默认展示在门店首页上和相册二级页面中，最多展示3张
									</Text>
									<View className={styles.imgList}>
										{headerImages.length < 1 && (
											<View
												className={styles.addBtn}
												onClick={
													handleUploadHeaderImage
												}
											>
												+ 添加头图 (
												{headerImages.length}/1)
											</View>
										)}
										{headerImages.map((img, idx) => (
											<View
												className={styles.imgItem}
												key={img}
											>
												<Image
													src={img}
													className={styles.img}
												/>
												<Text
													className={styles.close}
													onClick={() =>
														handleDeleteHeaderImage(
															idx
														)
													}
												>
													×
												</Text>
											</View>
										))}
									</View>
								</View>
							</View>
						</View>

						{/* 商家相册 */}
						<View className={styles.card}>
							<View className={styles.cardTitle}>商家相册</View>
							<Text className={styles.tip}>
								该图片会在首页、相册二级页中展示
							</Text>
							<View className={styles.imgList}>
								{albumImages.map((img, i) => (
									<View className={styles.imgItem} key={img}>
										<Image
											src={img}
											className={styles.img}
										/>
										<Text
											className={styles.close}
											onClick={() =>
												handleDeleteAlbumImage(i)
											}
										>
											×
										</Text>
									</View>
								))}
								{albumImages.length < 9 && (
									<View
										className={styles.addBtn}
										onClick={handleUploadAlbumImage}
									>
										+ 添加店铺图
									</View>
								)}
							</View>
						</View>
					</Form>
				</ScrollView>
				{/* 提交按钮 */}
				<View className={styles.submitBtn}>
					<AtButton
						type="primary"
						loading={loading}
						className={styles.submitBtn1}
						onClick={handleSubmit}
					>
						提交审核
					</AtButton>
				</View>
				<CategoryPicker
					visible={pickerVisible}
					options={categoryOptions}
					value={categoryValue}
					onClose={() => setPickerVisible(false)}
					onConfirm={handleCategoryConfirm}
				/>
			</View>
		</BasicPage>
	);
};

export default ShopEditPage;
