import React, { useEffect, useState } from "react";
import { View, Text, Input } from "@tarojs/components";
import styles from "./category-picker.module.scss";

interface CategoryOption {
  label: string;
  value: string;
  children?: CategoryOption[];
}

interface Props {
  visible: boolean;
  options: CategoryOption[];
  value?: string[];
  onClose: () => void;
  onConfirm: (value: string[], label: string[]) => void;
}

// 递归搜索所有层级，返回包含关键字的路径数组
function searchOptions(options: CategoryOption[], keyword: string, path: CategoryOption[] = []): CategoryOption[][] {
  let result: CategoryOption[][] = [];
  for (const opt of options) {
    const currentPath = [...path, opt];
    if (opt.label.includes(keyword)) {
      result.push(currentPath);
    }
    if (opt.children) {
      result = result.concat(searchOptions(opt.children, keyword, currentPath));
    }
  }
  return result;
}

const CategoryPicker: React.FC<Props> = ({
  visible, options, value = [], onClose, onConfirm
}) => {
  const [selected, setSelected] = useState<number[]>([0, 0, 0]);
  const [search, setSearch] = useState("");
  const [searchResult, setSearchResult] = useState<CategoryOption[][]>([]);
  const [searching, setSearching] = useState(false);

  // 受控回显
  useEffect(() => {
    if (value.length) {
      const idxs: number[] = [];
      let opts = options;
      value.forEach((v) => {
        const idx = opts.findIndex(i => i.value === v);
        idxs.push(idx >= 0 ? idx : 0);
        opts = opts[idx]?.children || [];
      });
      setSelected([idxs[0] || 0, idxs[1] || 0, idxs[2] || 0]);
    }
  }, [value, options, visible]);

  // 搜索逻辑
  useEffect(() => {
    if (search.trim()) {
      setSearching(true);
      setSearchResult(searchOptions(options, search.trim()));
    } else {
      setSearching(false);
      setSearchResult([]);
    }
  }, [search, options]);

  if (!visible) return null;

  // 三级联动数据
  const col1 = options;
  const col2 = col1[selected[0]]?.children || [];
  const col3 = col2[selected[1]]?.children || [];

  // 当前选中值
  const selectedValues = [
    col1[selected[0]]?.value,
    col2[selected[1]]?.value,
    col3[selected[2]]?.value,
  ].filter(Boolean);

  const selectedLabels = [
    col1[selected[0]]?.label,
    col2[selected[1]]?.label,
    col3[selected[2]]?.label,
  ].filter(Boolean);

  return (
    <View>
      <View className={styles.mask} onClick={onClose} />
      <View className={styles.popup}>
        <View className={styles.header}>
          <Text className={styles.cancel} onClick={onClose}>取消</Text>
          <Text>选择经营类目</Text>
          <Text className={styles.action} onClick={() => onConfirm(selectedValues, selectedLabels)}>确定</Text>
        </View>
        {/* 搜索框 */}
        <View style={{padding: '12px 18px 0 18px'}}>
          <Input
            placeholder="搜索中"
            value={search}
            onInput={e => setSearch(e.detail.value)}
            style={{background:'#f5f5f7', borderRadius: '8px', padding: '8px 12px', fontSize: '15px'}}
            focus={visible}
          />
        </View>
        <View className={styles.body}>
          {searching ? (
            <View style={{width:'100%'}}>
              {searchResult.length === 0 && (
                <View style={{padding:'32px', color:'#bbb', textAlign:'center'}}>无匹配结果</View>
              )}
              {searchResult.map((path, idx) => (
                <View
                  key={path.map(i=>i.value).join('-')}
                  className={styles.item + ' ' + (value[value.length-1] === path[path.length-1].value ? styles.active : '')}
                  style={{paddingLeft: 18, fontSize: 15, display:'flex', alignItems:'center'}}
                  onClick={() => onConfirm(path.map(i=>i.value), path.map(i=>i.label))}
                >
                  {path.map((i, iidx) => (
                    <React.Fragment key={i.value}>
                      {iidx > 0 && <Text style={{color:'#bbb', margin:'0 2px'}}>/</Text>}
                      <Text>{i.label}</Text>
                    </React.Fragment>
                  ))}
                </View>
              ))}
            </View>
          ) : (
            <>
              <View className={styles.col}>
                {col1.map((item, idx) => (
                  <View
                    key={item.value}
                    className={`${styles.item} ${selected[0] === idx ? styles.active : ""}`}
                    onClick={() => setSelected([idx, 0, 0])}
                  >{item.label}</View>
                ))}
              </View>
              <View className={styles.col}>
                {col2.map((item, idx) => (
                  <View
                    key={item.value}
                    className={`${styles.item} ${selected[1] === idx ? styles.active : ""}`}
                    onClick={() => setSelected([selected[0], idx, 0])}
                  >{item.label}</View>
                ))}
              </View>
              <View className={styles.col}>
                {col3.map((item, idx) => (
                  <View
                    key={item.value}
                    className={`${styles.item} ${selected[2] === idx ? styles.active : ""}`}
                    onClick={() => setSelected([selected[0], selected[1], idx])}
                  >{item.label}</View>
                ))}
              </View>
            </>
          )}
        </View>
      </View>
    </View>
  );
};

export default CategoryPicker; 