.mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1000;
}

.popup {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  border-radius: 16px 16px 0 0;
  z-index: 1001;
  min-height: 380px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 18px 0 18px;
  font-size: 17px;
  font-weight: 600;
  color: #222;
  background: #fff;
}

.action {
  color: #ffb300;
  font-size: 16px;
}

.cancel {
  color: #888;
  font-size: 16px;
}

.body {
  display: flex;
  flex: 1;
  min-height: 260px;
  padding: 0 0 16px 0;
  background: #fff;
  overflow: auto;
}

.col {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  &:last-child { border-right: none; }
  display: flex;
  flex-direction: column;
  min-width: 0;
  max-height: 300px;
  overflow-y: auto;
}

.item {
  padding: 12px 0 12px 18px;
  font-size: 15px;
  color: #222;
  background: #fff;
  cursor: pointer;
  transition: background 0.2s;
  border-radius: 0 16px 16px 0;
}

.item.active {
  background: #fffbe6;
  color: #ffb300;
  font-weight: 600;
}

.item.selected {
  background: #f5f5f7;
  color: #222;
  font-weight: 600;
} 