import React, { useState, useRef, useEffect, useCallback } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image, Input, Map } from "@tarojs/components";
import { AtButton } from "taro-ui";
import Form, { Field, useForm } from 'rc-field-form';
import PageWithNav, {
	PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import RenderTypeItem from "@/view/component/render-type/render-type-item.component";
import { fieldsConfig } from "@/util/fieldsMap";
import ShopIcon from "@/assets/image/common/camera.png";
import AddPlusImg from "@/assets/image/common/add-plus.png";
import { getShop, putShop } from '@/service/business/shangjiaruzhudianpuguanli'
import { getShopProjectAllCategory } from '@/service/business/shangjiaxiangmuguanli'
import styles from "./shop-edit-page.module.scss";

interface BusinessVO {
	businessName: string;
	businessId: number;
	businessCategory: string;
	region: string;
	address: string;
	contactUser: string;
	contactPhone: string;
	businessStatus: string;
	businessStartHours: string;
	businessEndHours: string;
	businessDesc: string;
	logo: string;
	images: string[];
	latitude: number;
	longitude: number;
}

interface BusinessParamVo {
	businessName: string;
	businessCategory: string;
	region: string;
	address: string;
	contactUser: string;
	contactPhone: string;
	businessStartHours: string;
	businessEndHours: string;
	businessDesc: string;
	logo: string;
	images: string[];
	latitude: number;
	longitude: number;
	password: string;
}

interface ApiResponse<T> {
	data: T;
	code: number;
	message: string;
}

interface ConfigItem {
	title?: string;
	tip?: string;
	items: Array<{
		title: string;
		key: string;
		type: string;
		required?: boolean;
		maxLength?: number;
		tip?: string;
	}>;
}

const ShopEditPage: React.FC = () => {
	const container: React.RefObject<PageContainerRef> = useRef(null);
	const [listHeight, setListHeight] = useState("calc(100vh - 42px)");
	const [loading, setLoading] = useState(false);
	const [categoryOptions, setCategoryOptions] = useState<any[]>([]);
	const [form] = useForm();

	const [shopInfo, setShopInfo] = useState<BusinessVO & { location: { latitude: number; longitude: number } }>({
		businessName: "",
		businessId: 0,
		businessCategory: "",
		region: "",
		address: "",
		contactUser: "",
		contactPhone: "",
		businessStatus: "",
		businessStartHours: "",
		businessEndHours: "",
		businessDesc: "",
		logo: "",
		images: [],
		latitude: 0,
		longitude: 0,
		location: {
			latitude: 0,
			longitude: 0,
		},
	});
	const [isH5, setIsH5] = useState(false);

	useEffect(() => {
		setIsH5(process.env.TARO_ENV === "h5");
		fetchShopInfo();
		fetchCategoryOptions();
	}, []);

	useEffect(() => {
		form.setFieldsValue(shopInfo);
	}, [shopInfo, form]);

	const fetchShopInfo = useCallback(async () => {
		try {
			setLoading(true);
			const res = await getShop() as ApiResponse<BusinessVO>;
			if (res?.data) {
				setShopInfo({
					...shopInfo,
					...res.data,
					location: {
						latitude: res.data.latitude || 0,
						longitude: res.data.longitude || 0,
					}
				});
			}
		} catch (error) {
			Taro.showToast({ title: "获取店铺信息失败", icon: "none" });
		} finally {
			setLoading(false);
		}
	}, []);

	const fetchCategoryOptions = async () => {
		try {
			const res = await getShopProjectAllCategory();
			const list = (res) ? res : [];
			setCategoryOptions(formatCategoryOptions(list));
		} catch (e) {
			console.error('获取门店品类失败', e);
		}
	};

	const formatCategoryOptions = (list: any[]): any[] => {
		return list.map(item => ({
			name: item.categoryName,
			code: String(item.categoryId),
			children: item.children ? formatCategoryOptions(item.children) : [],
		}));
	};

	const handleHeightChange = (navHeight: number) => {
		const height = navHeight + 20 + 50;
		setListHeight(`calc(100vh - ${height}px)`);
	};

	const handleSave = async () => {
		try {
			const values = await form.validateFields();
			setLoading(true);
			const saveData: BusinessParamVo = {
				...values,
				latitude: values.location?.latitude || 0,
				longitude: values.location?.longitude || 0,
				logo: values.logo,
				images: values.images,
				password: "",
				businessCategory: values.businessCategory,
			};
			const res = await putShop({ param: saveData });
			if (res) {
				Taro.showToast({ title: "保存成功", icon: "success" });
				setTimeout(() => { Taro.navigateBack(); }, 1500);
			}
		} catch (error) {
			Taro.showToast({ title: "保存失败", icon: "none" });
		} finally {
			setLoading(false);
		}
	};

	const handleFieldChange = (changed, all) => {
		console.log(changed, all, 22222);
		setShopInfo({ ...shopInfo, ...all });
	};

	// 店铺编辑信息配置
	const config = [
		{
			title: "店铺信息",
			items: [
				{
					type: "textarea",
					title: "店铺名称",
					key: "businessName",
					required: true,
					maxLength: 50,
					tip: "为保证审核通过，店铺名称务必喝门店招牌上",
				},
				fieldsConfig.slogan,
				{
					...fieldsConfig.category,
					required: true,
					titleWidth: 80,
					range: categoryOptions,
					key: 'businessCategory',
					businessCategoryLabel: shopInfo.businessCategoryLabel,
				},
				{
					...fieldsConfig.region,
					titleWidth: 80,
					range: categoryOptions,
				},
				{
					type: "textarea",
					title: "详细地址",
					key: "address",
					maxLength: 200,
					required: true,
				},
				{
					name: "地图位置",
					key: "location",
					type: "map",
					tip: "位置将作为顾客到店的导航依据，请仔细确认。",
				},
				fieldsConfig.phone,
			],
		},
	];

	// 添加选择位置的方法
	const handleSelectLocation = () => {
		if (isH5) {
			// H5环境下可以使用第三方地图选择器或者手动输入坐标
			Taro.showModal({
				title: "提示",
				content: "H5环境暂不支持地图选点，请手动输入坐标",
				showCancel: false,
			});
		} else {
			// 小程序环境使用原生接口
			Taro.chooseLocation({
				success: function (res: any) {
					setShopInfo({
						...shopInfo,
						location: {
							latitude: res.latitude,
							longitude: res.longitude,
						},
						address: res.address,
					});
				},
				fail: function (err) {
					console.log("选择位置失败", err);
					Taro.showToast({
						title: "选择位置失败",
						icon: "none",
					});
				},
			});
		}
	};

	// 上传图片
	const handleUploadImage = async (type: 'logo' | 'images') => {
		try {
			const res = await Taro.chooseImage({
				count: type === 'logo' ? 1 : 9,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
			});

			if (res.tempFilePaths.length > 0) {
				const uploadTasks = res.tempFilePaths.map(path => {
					return new Promise<string>((resolve, reject) => {
						Taro.uploadFile({
							url: '/api/upload',
							filePath: path,
							name: 'file',
							success: (uploadRes) => {
								const data = JSON.parse(uploadRes.data);
								resolve(data.url as string);
							},
							fail: reject
						});
					});
				});

				const urls = await Promise.all(uploadTasks);
				if (type === 'logo') {
					setShopInfo({ ...shopInfo, logo: urls[0] as string });
				} else {
					setShopInfo({ ...shopInfo, images: [...shopInfo.images, ...urls.map(String)].slice(0, 9) });
				}
			}
		} catch (error) {
			Taro.showToast({ title: "上传图片失败", icon: "none" });
		}
	};

	// 删除图片
	const handleDeleteImage = (index: number, type: 'logo' | 'images') => {
		if (type === 'logo') {
			setShopInfo({ ...shopInfo, logo: '' });
		} else {
			const newImages = [...shopInfo.images];
			newImages.splice(index, 1);
			setShopInfo({ ...shopInfo, images: newImages });
		}
	};

	// 渲染表单项
	const renderFormItem = (obj) => {
		switch (obj.type) {
			case "textarea":
				return (
					<View className={styles.inputSection}>
						<Input
							className={styles.input}
							value={shopInfo[obj.key]}
							placeholder={`请输入${obj.title}`}
							maxlength={obj.maxLength}
							onInput={(e) =>
								setShopInfo({ ...shopInfo, [obj.key]: e.detail.value })
							}
						/>
						<Text className={styles.total}>{`${shopInfo?.[obj.key]?.length ?? 0}/${obj.maxLength}`}</Text>
					</View>
				);
			case "map":
				// 根据环境渲染不同的地图组件
				if (isH5) {
					return (
						<View className={styles.h5MapContainer}>
							<iframe
								className={styles.h5MapIframe}
								src={`https://apis.map.qq.com/tools/poimarker?type=0&marker=coord:${shopInfo.location.latitude},${shopInfo.location.longitude};title:${shopInfo.businessName};addr:${shopInfo.address}&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&referer=myapp`}
								frameBorder="0"
								height={300}
							/>
						</View>
					);
				} else {
					return (
						<View className={styles.mapContainer}>
							<Map
								className={styles.map}
								longitude={shopInfo.location.longitude}
								latitude={shopInfo.location.latitude}
								markers={[
									{
										id: 1,
										longitude: shopInfo.location.longitude,
										latitude: shopInfo.location.latitude,
										iconPath: ShopIcon,
										callout: {
											content: shopInfo.businessName,
											color: '#000000',
											fontSize: 14,
											borderRadius: 4,
											bgColor: '#ffffff',
											padding: 5,
											display: 'ALWAYS',
											textAlign: 'center',
											anchorX: 0,
											anchorY: 0,
											borderWidth: 1,
											borderColor: '#000000',
										},
									},
								]}
								showLocation
								onClick={() => handleSelectLocation()}
							/>
						</View>
					);
				}
		}
	};

	return (
		<PageWithNav
			showNavBar
			title="店铺管理"
			containerRef={container}
			containerClassName={styles.container}
			onHeightChange={handleHeightChange}
		>
			<Form form={form} onValuesChange={handleFieldChange}>
				<View style={{ height: listHeight, overflowY: "auto" }}>
					{config.map((item: ConfigItem, sectionIndex) => (
						<View key={sectionIndex} className={styles.card}>
							{item.title && (
								<View className={styles.title}>
									<Text>{item.title}</Text>
									{item.tip && (
										<Text className={styles.tip}>{item.tip}</Text>
									)}
								</View>
							)}
							{item.items.map((obj, index) => (
								<Field name={obj.key} key={obj.key} initialValue={shopInfo[obj.key]}>
									{(control, meta, formCtx) => (
										<RenderTypeItem
											{...obj}
											itemKey={obj.key}
											value={control.value}
											onChange={val => {
												if (obj.key === 'businessCategory') {
													form.setFieldsValue({
														businessCategory: val,
														businessCategoryLabel: val,
													});
												}
												control.onChange(val);
											}}
											style={
												index === item.items?.length - 1
													? { paddingBottom: 0, borderBottom: "none" }
													: {}
											}
											required={obj.required || false}
										/>
									)}
								</Field>
							))}
						</View>
					))}

					<View className={styles.card}>
						<Text className={styles.title}>门店头图</Text>
						<View className={styles.shopHeader}>
							<View className={styles.shopTitle}>
								门店头图
								<Text className={styles.required}>*</Text>
							</View>
						</View>

						<View className={styles.shopSubTitle}>
							头图将会默认展示在门店首页上和相册二级页面中
						</View>

						<View className={styles.addShopImage} onClick={() => handleUploadImage('logo')}>
							<Image
								className={styles.addIcon}
								src={AddPlusImg}
								mode="aspectFill"
							/>
							<Text>添加头图</Text>
						</View>

						{shopInfo.logo && (
							<View className={styles.imagesWrapper}>
								<View className={styles.imageWrapper}>
									<Image
										className={styles.image}
										src={shopInfo.logo}
										mode="aspectFill"
									/>
									<View className={styles.close} onClick={() => handleDeleteImage(0, 'logo')}>X</View>
								</View>
							</View>
						)}
					</View>

					<View className={styles.card}>
						<Text className={styles.title}>商家相册</Text>
						<View className={styles.shopSubTitle}>
							该图片会在首页，相册二级页中展示
						</View>

						<View className={styles.addShopImage} onClick={() => handleUploadImage('images')}>
							<Image
								className={styles.addIcon}
								src={AddPlusImg}
								mode="aspectFill"
							/>
							<Text>添加店铺图 （{shopInfo.images.length}/9）</Text>
						</View>

						<View className={styles.imagesWrapper}>
							{shopInfo.images.map((item, index) => (
								<View key={index} className={styles.imageWrapper}>
									<Image
										className={styles.image}
										src={item}
										mode="aspectFill"
									/>
									<View className={styles.close} onClick={() => handleDeleteImage(index, 'images')}>X</View>
								</View>
							))}
						</View>
					</View>
				</View>
			</Form>
			<View className={styles.obts}>
				<AtButton type="primary" onClick={handleSave} circle loading={loading}>
					保存修改
				</AtButton>
			</View>
		</PageWithNav>
	);
};

export default ShopEditPage;
