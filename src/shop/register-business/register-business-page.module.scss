@import "~taro-ui/dist/style/components/form.scss";

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .form {
    flex: 1 0;
    background-color: rgba(246, 246, 246, 1);
    overflow: hidden;

    .formWrapper {
      display: grid;
      gap: 11px;
      padding: 11px;
    }

    .businessRegisterDiv {
      width: 100%;
    }

    .businessRegisterImg {
      border-radius: 8px;
      height: 106px;
      width: 100%;
    }

    .formDiv {
      border-radius: 8px;
      background-color: white;
      font-size: 16px;

      .formItem {
        height: 50px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        border-bottom: 1px solid #f7f7f7;
        margin: 0 20px;
        font-size: 15px;

        .label {
          width: 80px;

          &.required {
            &::after {
              content: "*";
              color: red;
            }
          }
        }

        .content {
          flex: 1 0;
          height: 100%;
          display: flex;
          align-items: center;
          display: flex;

          .control {
            flex: 1 0;
          }

          .placeholder {
            color: #ccc;
          }
        }
      }

      .shopInfo {
        line-height: 40px;
        padding-left: 13px;
        color: #1F1F1F;
        font-weight: 600;
      }

      .shopInput {
        // height: 50px;
        // margin-bottom: 0;
        margin-right: 20px;
      }

      .shopLogo {
        font-size: 15px;
        line-height: 30px;
        padding-left: 13px;
      }

      .shopLogoWarn {
        font-size: 13px;
        line-height: 25px;
        padding-left: 13px;
        color: #999999;
      }

      .logoContainer {
        padding-left: 13px;
        padding-top: 12px;
        padding-bottom: 15px;

        .uploadIcon {
          width: 78px;
          height: 78px;
        }

        .uploadText {
          font-size: 13px;
          line-height: 25px;
        }
      }

      .region {
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 20px;
        font-size: 30rpx;
        border-bottom: 1px solid #f2f2f2;

        .label {
          margin-right: 40px;
        }

        .value {
          flex: 1 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .start,
          .end {
            flex: 1 0;
            text-align: center;
          }

          .holder {
            color: #00000040;
          }
        }
      }
    }

    :global {
      .at-input::after {
        border-bottom: 1px solid #f2f2f2;
      }
    }



  }

  .submitDiv {
    background-color: white;
    padding-left: 17px;
    padding-right: 17px;
    font-size: 14px;

    :global {
      .at-checkbox__option-wrap {
        padding: 0;
      }

      .at-checkbox::before {
        border: 0;
      }

      .at-checkbox::after {
        border: 0;
      }

    }

    .agreementLink {
      font-size: 14px;
      color: #FDD244;
    }

    .checkboxWrapper {
      display: flex;
      line-height: 35px;

      .at-checkbox__option-cnt {
        font-size: 14px;
      }

      .agreedCheckbox {
        :global {
          .at-checkbox__icon-cnt {
            border: 1px solid #FDD244;
            margin-top: 9px;
          }

          .at-checkbox__title {
            font-size: 14px;
            line-height: 35px;
          }
        }
      }
    }

    .submitBtn {
      border-radius: 20px;
    }
  }
}