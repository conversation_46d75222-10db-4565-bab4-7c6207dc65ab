import { makeAutoObservable } from "mobx";
 class MemberStore {

    memberList = [{
        cardLevel: '储值卡',
        cardUrl: '123455',
        discount: '5折',
        status: '开启',
        memberNum: '111'
    }, {
        cardLevel: '储值卡',
        cardUrl: '123455',
        discount: '5折',
        status: '开启',
        memberNum: '111'
    }, {
        cardLevel: '储值卡',
        cardUrl: '123455',
        discount: '5折',
        status: '开启',
        memberNum: '111'
    }]

    constructor() {
        makeAutoObservable(this);
    }

}


const memberStore = new MemberStore()
export default memberStore