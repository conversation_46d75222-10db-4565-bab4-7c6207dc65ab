import { View, Text, Image } from '@tarojs/components';
import { useRef, useState } from 'react';
import { AtButton } from 'taro-ui';
import Taro, {  useDidShow } from '@tarojs/taro';
import styles from './member-list-page.module.scss';
import uploadIcon from '@/assets/image/member/tianjia (2)@2x.png';
import { deleteMemCardtemplateTemplateIds, putMemCardtemplate } from '@/service/business/huiyuankamoban'
import PageWithNav, { PageContainerRef } from '@/view/component/page-with-nav/page-with-nav.component';

const colorEnmu = {
  0: {
    color: '#FF5252',
    backgroundColor:
      'linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);',
  },
  2: {
    color: '#3B5998',
    backgroundColor:
      'linear-gradient( 180deg, rgba(255,255,255,0.09) 0%, rgba(255,255,255,0.03) 51%, rgba(255,255,255,0.05) 100%);',
  },
  3: {
    color: '#FFC107',
    backgroundColor:
      'linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);',
  },
  1: {
    color: '#FFC107',
    backgroundColor:
      'linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);',
  },
};
const MemberListPage = () => {
  // 会员卡类型数据
  const [cardTypes, setCardTypes]: any = useState([]);
  // 编辑状态
  const [isEditMode, setIsEditMode] = useState(false);
  // 长按计时器
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);

  const container: React.RefObject<PageContainerRef> = useRef(null);


  // 刷新列表方法
  const fetchMemberCardList = () => {
    service.mem.tMemberCardTemplateController
      .getMemCardtemplateList({})
      .then((res) => {
        setCardTypes(
          (res || []).map((item, index) => {
            return {
              ...item,
              selected: index === 0 ? true : false,
              ...colorEnmu[index % 4],
            };
          })
        );
        console.log(res);
      });
  };

  useMount(() => {
    fetchMemberCardList();
  });

  useDidShow(() => {
    const router = Taro.getCurrentInstance().router;
    if (router?.params?.action === 'created') {
      fetchMemberCardList();
    }
  });

  // 处理卡片选择
  const handleCardSelect = (templateId) => {
    const updatedCards = cardTypes.map((card) => ({
      ...card,
      selected: card.templateId === templateId,
    }));
    setCardTypes(updatedCards);
  };

  // 处理确定按钮点击
  const handleConfirm = () => {
    const selectedCard = cardTypes.find((card) => card.selected);
    if (selectedCard) {
      Taro.showToast({
        title: `已选择${selectedCard.cardType}`,
        icon: 'success',
        duration: 2000,
      });

      Taro.eventCenter.trigger(
        'UPDATE_ADDRESS',
        {
          address: selectedCard,
        },
        {
          success: () => console.log('DEBUG - 事件触发成功'),
          fail: (err) => console.log('DEBUG - 事件触发失败:', err),
        }
      );
      // 这里可以添加导航到下一个页面的逻辑
      setTimeout(() => {
        Taro.navigateBack({
          delta: 1,
        });
      }, 1);
    }
  };

  // 处理添加新卡片
  const handleAddCard = () => {
    Taro.navigateTo({
      url: '/member-card-template/create-member-card/create-member-card-page',
    });
  };

  // 处理长按开始
  const handleLongPressStart = (templateId: string) => {
    const timer = setTimeout(() => {
      setIsEditMode(true);
      Taro.vibrateShort(); // 触觉反馈
      Taro.showToast({
        title: '已进入编辑模式',
        icon: 'none',
        duration: 1500
      });
    }, 500); // 500ms 长按触发
    setLongPressTimer(timer);
  };

  // 处理长按结束
  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  // 处理编辑按钮点击
  const handleEdit = (card: any) => {
    Taro.navigateTo({
      url: `/member-card-template/create-member-card/create-member-card-page?id=${card.templateId}&mode=edit`
    });
  };

  // 处理删除按钮点击
  const handleDelete = (card: any) => {
    Taro.showModal({
      title: '确认删除',
      content: `确定要删除会员卡"${card.cardType}"吗？`,
      confirmColor: '#FF5252',
      success: async (res) => {
        if (res.confirm) {
          try {
            // TODO: 调用删除 API
            await service.mem.tMemberCardTemplateController.deleteMemCardtemplate({
              templateId: card.templateId
            });
            Taro.showToast({ title: '删除成功', icon: 'success' });
            fetchMemberCardList();
          } catch (error) {
            Taro.showToast({ title: '删除失败', icon: 'none' });
          }
        }
      }
    });
  };

  // 退出编辑模式
  const exitEditMode = () => {
    setIsEditMode(false);
  };

  return (
    <PageWithNav
      showNavBar
      onBack={() => Taro.navigateBack()}
      title="会员卡列表"
      containerRef={container}
      className='relative h-full bg-white overflow-hidden'
    >
    <View className={styles.container}>
      {isEditMode && (
        <View className={styles.editModeHeader}>
          <Text className={styles.editModeTitle}>编辑模式</Text>
          {/* <Text className={styles.editModeTip}>点击卡片可编辑</Text> */}
          <View className={styles.exitButton} onClick={exitEditMode}>
            退出编辑
          </View>
        </View>
      )}
      
      <View className={`${styles.cardList}`} style={{ paddingBottom: '160rpx' }}>
        {cardTypes.map((card) => (
          <View
            key={card.templateId}
            className={`${styles.cardItem} ${card.selected ? styles.selected : ''}`}
            style={{ backgroundColor: card.color }}
            onClick={() => isEditMode ? handleEdit(card) : handleCardSelect(card.templateId)}
            onLongPress={() => handleLongPressStart(card.templateId)}
            onTouchEnd={handleLongPressEnd}
          >
            {/* 删除按钮，仅编辑模式下显示 */}
            {isEditMode && (
              <View
                className={styles.deleteIconBtn}
                onClick={e => {
                  e.stopPropagation();
                  Taro.showModal({
                    title: '确认删除',
                    content: `确定要删除会员卡"${card.cardType}"吗？`,
                    confirmColor: '#FF5252',
                    success: async (res) => {
                      if (res.confirm) {
                        try {
                          await deleteMemCardtemplateTemplateIds({ templateIds: [card.templateId] });
                          Taro.showToast({ title: '删除成功', icon: 'success' });
                          fetchMemberCardList();
                        } catch (error) {
                          Taro.showToast({ title: '删除失败', icon: 'none' });
                        }
                      }
                    }
                  });
                }}
              >
                <Text className={styles.deleteIcon}>×</Text>
              </View>
            )}
            <View
              className={styles.cardHeader}
              style={{ backgroundColor: card.backgroundColor }}
            >
              <Text className={styles.cardName}>{card.cardType}</Text>
              {!isEditMode && (
                <View className={styles.radioButton}>
                  <View
                    className={`${styles.radioInner} ${
                      card.selected ? styles.radioSelected : ''
                    }`}
                  ></View>
                </View>
              )}
            </View>
            <View className={styles.cardContent}>
              <Text className={styles.cardDiscount}>
                享受折扣：{card.cardName}
              </Text>
              <Text className={styles.cardProject}>赠送项目：-</Text>
            </View>
            <View className={styles.cardBackground}>
              <View className={styles.cardPattern}></View>
            </View>
          </View>
        ))}

        {!isEditMode && (
          <View className={styles.addCardItem} onClick={handleAddCard}>
            <Image src={uploadIcon} className={styles.addIcon} />
            <Text className={styles.addText}>去设置会员卡模版</Text>
          </View>
        )}
      </View>

      {!isEditMode && (
        <View className={styles.buttonContainer} style={{ position: 'fixed', bottom: 0, left: 0, right: 0, padding: '20rpx', backgroundColor: 'white', boxShadow: '0 -2rpx 10rpx rgba(0,0,0,0.1)', zIndex: 20 }}>
          <AtButton
            type="primary"
            className={styles.confirmButton}
            onClick={handleConfirm}
          >
            确定
          </AtButton>
        </View>
      )}
    </View>
    </PageWithNav>
  );
};

export default MemberListPage;
