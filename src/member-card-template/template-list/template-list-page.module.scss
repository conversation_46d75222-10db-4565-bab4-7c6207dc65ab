// 基础样式常量
$primary-color: #ffb400;
$text-color: #333333;
$text-color-light: #ffffff;
$background-color: #f5f5f5;
$border-radius: 12px;

.container {
  display: flex;
  flex-direction: column;
}

.editModeHeader {
  position: fixed;
  top: 68px;
  left: 30%;
  transform: translateX(-10%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .editModeTitle {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }

  .editModeTip {
    font-size: 12px;
    color: #666666;
  }

  .exitButton {
    padding: 4px 12px;
    background: #F5F5F5;
    border-radius: 4px;
    font-size: 12px;
    color: #666666;
  }
}

.cardList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cardItem {
  background: linear-gradient(76deg, #FFDC7F 0%, #FFBC2A 100%);
  border-radius: 16px;
  padding: 14px 14px 10px 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 15px;
  color: #333;
  margin-bottom: 10px;
}

.cardHeaderRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.cardTitle {
  font-size: 14px;
  font-weight: bold;
  color: #222;
  margin-bottom: 8px;
  line-height: 1.2;
}

.cardGift {
  font-size: 14px;
  color: #7c5a1a;
  opacity: 0.85;
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardInfoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.cardPrice {
  font-size: 14px;
  font-weight: bold;
  color: #7a4e13;
}

.cardGiftRight {
  font-size: 14px;
  color: #7a4e13;
  text-align: right;
}

.cardRemain {
  font-size: 14px;
  color: #7c5a1a;
  opacity: 0.85;
}

.cardBtnRow {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 4px;
}

.editBtn,
.actionBtn {
  min-width: 64rpx;
  height: 40rpx;
  padding: 20rpx 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  box-sizing: border-box;
}

.editBtn {
  background: #fff;
  color: #222;
  border: 1px solid #bbb;
}

.actionBtn {
  background: #fff;
  color: #ffbc2a;
  border: 1px solid #ffbc2a;
  margin-left: 0;
}

.editOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}

.editActions {
  display: flex;
  gap: 24px;
}

.editButton, .deleteButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  Text {
    font-size: 14px;
    color: #333333;
  }
}

.editIcon {
  font-size: 20px;
  color: #3B5998;
}

.deleteIcon {
  font-size: 24px;
  color: #FF5252;
}

.selected {
  // border: 2px solid $primary-color;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  background: linear-gradient( 180deg, rgba(255,255,255,0.14) 0%, rgba(255,255,255,0.11) 51%, rgba(255,255,255,0.05) 100%);
  padding: 10px 20px 4px 20px;
}

.cardName {
  font-size: 16px;
  // font-weight: bold;
  color: $text-color-light;
}

.radioButton {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid $text-color-light;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.radioInner {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: transparent;
}

.radioSelected {
  background-color: $text-color-light;
}

.cardContent {
  flex: 1;
  height: 100vh;
  overflow-y: scroll;
  background: #fff;
  padding: 0 12px;
}

.cardDiscount, .cardProject {
  font-size: 12px;
  color: $text-color-light;
  opacity: 0.9;
}

.cardBackground {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}

.cardPattern {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.addCardItem {
  border-radius: $border-radius;
  padding: 20px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  border: 1px dashed #ddd;
}

.addIcon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.addText {
  font-size: 16px;
  color: #999;
}

.buttonContainer {
  margin-top: auto;
  padding: 20px 0;
}

.confirmButton {
  background-color: $primary-color !important;
  border: none !important;
  border-radius: 30px !important;
  font-size: 16px !important;
  height: 50px !important;
  line-height: 50px !important;
}

.deleteIconBtn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  width: 28px;
  height: 28px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);

  &:active {
    transform: scale(0.9);
  } 

  .deleteIcon {
    color: #FFFFFF;
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
  }
}

.memberCardPage {
  display: flex;
  // height: 100vh;
  flex: 1;
  background: #f5f5f5;
  overflow-y: auto;
}

.sideMenu {
  width: 90px;
  background: #F6F6F6;
  // border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  // padding-top: 16px;
}

.menuItem {
  position: relative;
  padding: 16px 0;
  text-align: center;
  color: #666;
  font-size: 15px;
  cursor: pointer;
  background: #F6F6F6;
  transition: background 0.2s, color 0.2s;
  z-index: 1;

  &.active {
    color: #ffb400;
    font-weight: bold;
    background: #fff;
    z-index: 2;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 60%;
      background: #ffb400;
      border-radius: 4px 0 0 4px;
      box-shadow: 0 0 6px #ffe066;
    }
  }
}

.cardContent {
  flex: 1;
  height: 100vh;
  overflow-y: scroll;
  background: #fff;
  padding: 0 12px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #222;
  margin: 24px 0 12px 0;
}

.cardList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topCardBox {
  margin: 12px 0 16px 0;
  padding: 0 16px;
}

.topCardContent {
  background: #fff;
  border: 1px dashed #d8d8d8;
  border-radius: 8px;
  padding: 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topCardTitle {
  font-size: 17px;
  font-weight: bold;
  color: #222;
  margin-bottom: 6px;
  display: block;
}

.topCardDesc {
  font-size: 13px;
  color: #888;
  display: block;
}

.createBtn {
  display: flex;
  align-items: center;
  background: #ffebad;
  border-radius: 20px;
  padding: 0 14px;
  height: 36px;
  cursor: pointer;
  font-size: 15px;
  color: #ffb400;
  font-weight: 500;
  transition: background 0.2s;
  &:active {
    background: #ffe066;
  }
}

.plusIcon {
  font-size: 20px;
  margin-right: 4px;
  font-weight: bold;
  color: #ffb400;
}

.createText {
  font-size: 15px;
  color: #ffb400;
}


