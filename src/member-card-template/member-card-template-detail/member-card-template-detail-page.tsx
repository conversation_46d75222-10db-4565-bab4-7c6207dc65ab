import React, { useEffect, useState } from "react";
import { View, Text, ScrollView } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import dayjs from "dayjs"; // Import dayjs

import { getMemCardtemplateTemplateId } from "@/service/business/huiyuankamoban";
import PageWithNav from "@/view/component/page-with-nav/page-with-nav.component";

import styles from "./member-card-template-detail-page.module.scss"; // Updated path

// Removed mock function, actual API will be used.

const MemberCardTemplateDetailPage: React.FC = () => {
  const router = useRouter();
  const [detail, setDetail] = useState<any>(null); // Detail will be of type BUSINESS.TMemberCardTemplate or similar
  const [loading, setLoading] = useState(false);

  // Helper function to format date string to YYYY-MM-DD using dayjs
  const formatDateDisplay = (dateTimeString) => {
    if (!dateTimeString) return 'N/A';
    return dayjs(dateTimeString).format('YYYY-MM-DD'); // Use dayjs
  };

  useEffect(() => {
    const fetchDetail = async () => {
      if (!router.params.id) {
        Taro.showToast({ title: "缺少模板ID", icon: "none" });
        return;
      }
      setLoading(true);
      try {
        // Call the actual API
        const res = await getMemCardtemplateTemplateId({ templateId: Number(router.params.id) });
        // The API likely returns the detail object directly
        setDetail(res); 
      } catch (e) {
        Taro.showToast({ title: "获取详情失败", icon: "none" });
      } finally {
        setLoading(false);
      }
    };
    fetchDetail();
  }, [router.params.id]);

  // Helper function to format validity period
  const formatValidityPeriod = (template) => {
    if (!template) return '';
    switch (template.validityType) {
      case '按天数计算':
        return template.validDays ? `${template.validDays}天` : '未设置';
      case '按月份计算':
        return template.validMonths ? `${template.validMonths}个月` : '未设置';
      case '按截止日期计算':
        return template.endDate || '未设置';
      case '长期有效':
        return '长期有效';
      default:
        return template.expireDate || '未设置'; // Fallback to existing mock field or default
    }
  };

  // Placeholder for formatting gift list - adapt based on setmealList structure
  const formatGiftList = (setmealList) => {
    if (!setmealList || setmealList.length === 0) {
      return [{ type: "暂无", itemName: "暂无赠送内容", count: "" }];
    }
    // TODO: Adapt this mapping based on the actual structure of TMemberCardTemplateSetmeal
    return setmealList.map(item => ({
      type: item.itemType || "项目", // Example: map item.itemType to '指定项目' or '商品'
      itemName: item.itemName || "未知项目/商品",
      count: item.itemCount ? `${item.itemCount}次` : "-", // Example
    }));
  };
  
    // Placeholder for formatting discount list
  const formatDiscountList = (template) => {
    if (template?.discountValue) {
      return [{ discount: `${template.discountValue}折`, type: "主要折扣", itemName: "适用于所有符合条件的项目/商品" }];
    }
    // TODO: If setmealList contains discount info, map it here
    return [{ discount: "暂无", type: "暂无折扣信息", itemName: "" }];
  };


  if (loading) {
    return (
      <PageWithNav title="会员卡模板详情">
        <View className={styles.pageBg}>
          <View className={styles.loading}>加载中...</View>
        </View>
      </PageWithNav>
    );
  }

  if (!detail) {
    return (
      <PageWithNav title="会员卡模板详情">
        <View className={styles.pageBg}>
          <View className={styles.loading}>暂无数据</View>
        </View>
      </PageWithNav>
    );
  }

  return (
    <PageWithNav title="会员卡模板详情">
      <ScrollView scrollY style={{ height: '100%' }}>
        <View className={styles.pageBg}>
          {/* 卡片头部 */}
          <View className={styles.cardHeader}>
            <View className={styles.cardNameRow}>
              <Text className={styles.cardName}>{detail?.cardName}</Text>
              <Text className={styles.cardTypeTag}>{detail?.cardType}</Text> {/* API: cardType */} 
            </View>
          </View>

          {/* 卡面价值 */}
          <View className={styles.sectionBox}>
            <View className={styles.sectionTitle}>卡面价值</View>
            <View className={styles.sectionRow}>
              <Text>售价：￥{detail?.retailPrice}</Text> {/* API: retailPrice */}
              <Text>赠送金额：￥{detail?.giftAmount}</Text> {/* API: giftAmount */}
            </View>
          </View>

          {/* 赠送内容 */}
          <View className={styles.sectionBox}>
            <View className={styles.sectionTitle}>赠送内容</View>
            <View className={styles.tableBox}>
              <View className={styles.tableHeader}>
                <Text>类型</Text>
                <Text>项目/商品</Text>
                <Text>次数</Text>
              </View>
              {/* Use formatted gift list */}
              {formatGiftList(detail?.setmealList || detail?.gettSetmealList).map((item, idx) => (
                <View className={styles.tableRow} key={idx}>
                  <Text>{item.type}</Text>
                  <Text>{item.itemName}</Text>
                  <Text>{item.count}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* 折扣内容 */}
          <View className={styles.sectionBox}>
            <View className={styles.sectionTitle}>折扣内容</View>
            <View className={styles.tableBox}>
              <View className={styles.tableHeader}>
                <Text>折扣</Text>
                <Text>类型</Text>
                <Text>项目</Text>
              </View>
              {/* Use formatted discount list */}
              {formatDiscountList(detail).map((item, idx) => (
                <View className={styles.tableRow} key={idx}>
                  <Text>{item.discount}</Text>
                  <Text>{item.type}</Text>
                  <Text>{item.itemName}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* 模板信息 */}
          <View className={styles.infoBox}>
            <View className={styles.infoTitle}>模板信息</View>
            <View className={styles.infoRow}>
              <Text className={styles.label}>模板名称</Text>
              <Text className={styles.value}>{detail?.cardName}</Text>
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.label}>卡片类型</Text>
              <Text className={styles.value}>{detail?.cardType}</Text> {/* API: cardType */} 
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.label}>模板编号</Text>
              <Text className={styles.value}>{detail?.templateId}</Text> {/* API: templateId */} 
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.labelBold}>适用门店</Text>
              {/* TODO: Update validStoreName based on actual API data or logic */}
              <Text className={styles.valueBold}>{detail?.validStoreName || "全部门店通用"}</Text> 
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.labelBold}>状态</Text>
              {/* TODO: Map API status (e.g., delFlag) to "启用"/"禁用" */}
              <Text className={styles.valueBold}>{detail?.delFlag ? "禁用" : "启用"}</Text> 
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.labelBold}>创建日期</Text>
              <Text className={styles.valueBold}>{formatDateDisplay(detail?.createTime)}</Text> {/* API: createTime, formatted */}
            </View>
            <View className={styles.infoRow}>
              <Text className={styles.labelBold}>有效期限</Text>
              <Text className={styles.valueBold}>{formatValidityPeriod(detail)}</Text> {/* Use helper */}
            </View>
          </View>
        </View>
      </ScrollView>
    </PageWithNav>
  );
};

export default MemberCardTemplateDetailPage; 