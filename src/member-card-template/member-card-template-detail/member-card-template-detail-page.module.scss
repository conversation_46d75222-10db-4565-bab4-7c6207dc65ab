.wrapper {
  background: #F6F6F6;
  min-height: 100vh;
  padding: 0 0 16px 0;
}

.title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  padding: 16px 0 12px 16px;
  color: #222;
  background: #fff;
}

.section {
  padding: 0 16px;
  background: #fff;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
}

.label {
  color: #bdbdbd;
  font-size: 14px;
}

.value {
  color: #757575;
  font-size: 14px;
}

.labelBold {
  color: #222;
  font-size: 14px;
  font-weight: bold;
}

.valueBold {
  color: #222;
  font-size: 14px;
  font-weight: bold;
}

.pageBg {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 32px;
}
.cardHeader {
  background: #fffbe6;
  border-radius: 16px;
  margin: 16px;
  padding: 16px;
}
.cardNameRow {
  display: flex;
  align-items: center;
  gap: 8px;
}
.cardName {
  font-size: 20px;
  font-weight: bold;
}
.cardTypeTag {
  background: #5ad47c;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 12px;
}
.cardHolderRow {
  margin-top: 8px;
  color: #888;
  font-size: 14px;
  display: flex;
  gap: 16px;
}
.summaryBox {
  background: #fff;
  border-radius: 12px;
  margin: 16px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  text-align: center;
}
.summaryItem {
  flex: 1;
}
.summaryLabel {
  color: #888;
  font-size: 13px;
}
.summaryValue {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-top: 4px;
  color: #ffbc2a;
}
.sectionBox {
  background: #fff;
  border-radius: 12px;
  margin: 16px;
  padding: 16px;
}
.sectionTitle {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.sectionRow {
  display: flex;
  justify-content: space-between;
  color: #666;
  font-size: 14px;
}
.tableBox {
  margin-top: 8px;
}
.tableHeader, .tableRow {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 4px 0;
}
.tableHeader {
  color: #999;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}
.tableRow {
  color: #333;
}
.infoBox {
  background: #fff;
  border-radius: 12px;
  margin: 16px;
  padding: 16px;
}
.infoTitle {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
  font-size: 14px;
  padding: 10px 0;
  border-bottom: 1px solid #f2f2f2;
}
.infoRow:last-child {
  border-bottom: none;
}
.loading {
  text-align: center;
  color: #999;
  margin-top: 40px;
} 