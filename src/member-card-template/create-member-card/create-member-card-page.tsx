import { useState, useEffect, useRef } from 'react';
import { View, Text, Input, Picker, Switch, Textarea } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Form, { useForm } from 'rc-field-form';
import { postMemCardtemplate, getMemCardtemplateTemplateId, putMemCardtemplate, deleteMemCardtemplateTemplateIds } from '@/service/business/huiyuankamoban';
import { postShopProjectAll } from '@/service/business/shangjiaxiangmuguanli';
import ProjectMultiSelectModal from '@/view/component/project-multi-select-modal/ProjectMultiSelectModal';
import { PageContainerRef } from '@/view/component/page-container/page-container.component';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';

// 卡类型枚举
const CARD_TYPE = {
  STORED_VALUE: '储值卡',
  DISCOUNT: '折扣卡',
  COUNT: '计数卡',
  TIME: '计时卡'
} as const;

// 有效期类型枚举
const VALIDITY_TYPE = {
  FOREVER: '长期有效',
  END_DATE: '截止日期'
} as const;

const CARD_TYPE_LIST = Object.values(CARD_TYPE);
const VALIDITY_TYPE_LIST = Object.values(VALIDITY_TYPE);

const CreateMemberCardPage = () => {
  const [form] = useForm();
  const [validityType, setValidityType] = useState<keyof typeof VALIDITY_TYPE>('FOREVER');
  const [submitting, setSubmitting] = useState(false);
  const [projectList, setProjectList] = useState<BUSINESS.BusinessProject[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const container: React.RefObject<PageContainerRef> = useRef(null);


  // 处理 url 参数
  const router = Taro.useRouter();
  const { id, mode } = router.params || {};

  // 获取项目列表
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const loginInfo = Taro.getStorageSync('loginInfo');
        const res = await postShopProjectAll({
          businessId: loginInfo.businessId,
          status: 1
        });
        if (res) {
          setProjectList(res || []);
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
      }
    };
    fetchProjects();
  }, []);

  // 编辑模式下拉取详情并赋值表单
  useEffect(() => {
    if (mode === 'edit' && id) {
      const fetchCardDetail = async () => {
        try {
          const res = await getMemCardtemplateTemplateId({ templateId: Number(id) });
          if (res) {
            const { 
              cardType, 
              cardName, 
              retailPrice, 
              giftAmount, 
              validityType: cardValidityType, 
              endDate, 
              status, 
              staffRemark, 
              projects 
            } = res;

            // console.log(status, '---status')
            
            // 设置表单值
            form.setFieldsValue({
              cardTypeIndex: cardType ? CARD_TYPE_LIST.indexOf(cardType as typeof CARD_TYPE[keyof typeof CARD_TYPE]) : undefined,
              cardName,
              retailPrice,
              giftAmount,
              endDate,
              status: status === 1 ? true : false,
              staffRemark
            });

            // 设置有效期类型
            const validityTypeKey = Object.entries(VALIDITY_TYPE).find(([_, value]) => value === cardValidityType)?.[0] as keyof typeof VALIDITY_TYPE;
            if (validityTypeKey) {
              setValidityType(validityTypeKey);
            }

            // 设置选中的项目
            if (projects) {
              const validProjectIds = projects
                .map(p => p.projectId)
                .filter((projectId): projectId is number => typeof projectId === 'number');
              setSelectedIds(validProjectIds);
            }
          }
        } catch (error) {
          console.error('获取会员卡详情失败:', error);
          Taro.showToast({ title: '获取会员卡详情失败', icon: 'none' });
        }
      };
      fetchCardDetail();
    }
  }, [mode, id, form]);

  // 提交表单
  const handleSubmit = () => {
    form.validateFields().then(async (values) => {
      setSubmitting(true);
      const commonParams = {
        cardType: CARD_TYPE_LIST[values.cardTypeIndex],
        cardName: values.cardName,
        retailPrice: Number(values.retailPrice),
        giftAmount: Number(values.giftAmount || 0),
        validityType: VALIDITY_TYPE[validityType],
        endDate: validityType === 'END_DATE' ? values.endDate : undefined,
        status: values.status ? 1 : 0,
        staffRemark: values.staffRemark,
        projects: selectedIds.map(projectId => ({
          projectId,
          seq: 1
        }))
      };

      try {
        const res = mode === 'edit' 
          ? await putMemCardtemplate({
              id: Number(id),
              ...commonParams
            })
          : await postMemCardtemplate(commonParams);
        if (res) {
          Taro.showToast({ title: mode === 'edit' ? '更新成功' : '创建成功', icon: 'success' });
          setTimeout(() => {
            Taro.redirectTo({
              url: '/member-card-template/template-list/template-list-page?action=created'
            });
          }, 1000);
        } else {
          Taro.showToast({ title: mode === 'edit' ? '更新失败' : '创建失败', icon: 'none' });
        }
      } catch (e) {
        Taro.showToast({ title: mode === 'edit' ? '更新失败' : '创建失败', icon: 'none' });
      } finally {
        setSubmitting(false);
      }
    }).catch(() => {
      Taro.showToast({ title: '请完善信息', icon: 'none' });
    });
  };

  // 处理项目选择
  const handleProjectSelect = (e: any) => {
    setSelectedIds(e.detail.value);
  };

  const handleDelete = () => {
    Taro.showModal({
      title: '提示',
      content: '确定删除该会员卡模板吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await deleteMemCardtemplateTemplateIds({ templateIds: [Number(id)] });
            Taro.showToast({ title: '删除成功', icon: 'success' });
            setTimeout(() => {
              Taro.redirectTo({
                url: '/member-card-template/template-list/template-list-page?action=deleted'
              });
            }, 500);
          } catch (error) {
            Taro.showToast({ title: '删除失败', icon: 'none' });
          }
        }
      }
    });
  }

  return (
    <PageWithNav
      showNavBar
      onBack={() => Taro.navigateBack()}
      title={mode === 'edit' ? '编辑会员卡模板' : '新建会员卡模板'}
      containerRef={container}
      className='relative h-full bg-white overflow-hidden'
    >
      <View className="h-full bg-[#F8F8F8] flex flex-col overflow-y-auto">
        <Form form={form} initialValues={{ cardTypeIndex: undefined, status: true, giftAmount: '', staffRemark: '' }}>
          <View className="bg-white rounded-lg px-4 py-3 mt-3">
            {/* 卡片类型 */}
            <Form.Field name="cardTypeIndex" rules={[{ required: true, message: '请选择卡片类型' }]}> 
              {(control) => (
                <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
                  <Text className="text-[28rpx] text-[#222] w-[180rpx]">卡片类型<Text className="text-[#FFB300]"> *</Text></Text>
                  <Picker
                    mode="selector"
                    range={CARD_TYPE_LIST}
                    value={control.value}
                    onChange={e => control.onChange(Number(e.detail.value))}
                  >
                    <View className="flex-1 flex items-center justify-between">
                      <Text className={`text-[28rpx] ${control.value === undefined ? 'text-[#C0C0C0]' : 'text-[#222]'}`}>
                        {control.value === undefined ? '请选择卡片类型' : CARD_TYPE_LIST[control.value]}
                      </Text>
                      <Text className="iconfont icon-arrow-right text-[#D8D8D8] text-[32rpx]" />
                    </View>
                  </Picker>
                </View>
              )}
            </Form.Field>
            {/* 卡片名称 */}
            <Form.Field name="cardName" rules={[{ required: true, message: '请输入卡片名字' }]}> 
              {(control) => (
                <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
                  <Text className="text-[28rpx] text-[#222] w-[180rpx]">卡片名字</Text>
                  <Input
                    className="flex-1 text-[28rpx]"
                    placeholder="请输入卡片名字"
                    placeholderStyle="color:#C0C0C0;font-size:28rpx;"
                    value={control.value}
                    onInput={e => control.onChange(e.detail.value)}
                  />
                </View>
              )}
            </Form.Field>
            {/* 卡售零售 */}
            <Form.Field name="retailPrice" rules={[{ required: true, message: '请输入卡的零售金额' }]}> 
              {(control) => (
                <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
                  <Text className="text-[28rpx] text-[#222] w-[180rpx]">卡售零售<Text className="text-[#FFB300]"> *</Text></Text>
                  <Input
                    className="flex-1 text-[28rpx]"
                    placeholder="请输入卡的零售金额"
                    placeholderStyle="color:#C0C0C0;font-size:28rpx;"
                    value={control.value}
                    type="digit"
                    onInput={e => control.onChange(e.detail.value)}
                  />
                  <Text className="ml-2 text-[28rpx] text-[#999]">元</Text>
                </View>
              )}
            </Form.Field>
            {/* 赠送金额 */}
            <Form.Field name="giftAmount">
              {(control) => (
                <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
                  <Text className="text-[28rpx] text-[#222] w-[180rpx]">赠送金额</Text>
                  <Input
                    className="flex-1 text-[28rpx]"
                    placeholder="不赠送则填0"
                    placeholderStyle="color:#C0C0C0;font-size:28rpx;"
                    value={control.value}
                    type="digit"
                    onInput={e => control.onChange(e.detail.value)}
                  />
                  <Text className="ml-2 text-[28rpx] text-[#999]">元</Text>
                </View>
              )}
            </Form.Field>
            {/* 赠送项目 */}
            <View className="border-b border-0 border-[#F0F0F0] border-solid py-1">
              <View className="flex items-center h-[100rpx]">
                <Text className="text-[28rpx] text-[#222] w-[180rpx]">赠送项目</Text>
                <View
                  className="flex-1 flex items-center justify-between text-right rounded-lg px-3 py-2 ml-2 cursor-pointer"
                  onClick={() => setModalVisible(true)}
                  style={{ minHeight: 40 }}
                >
                  <Text
                    className={`text-[28rpx] ${selectedIds.length === 0 ? 'text-[#C0C0C0]' : 'text-[#222]'}`}
                    style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      flex: 1,
                    }}
                  >
                    {selectedIds.length === 0 ? '请选择赠送项目' : `已选择${selectedIds.length}个项目`}
                  </Text>
                  <Text className="iconfont icon-arrow-right text-[#D8D8D8] text-[32rpx] ml-2" />
                </View>
              </View>
              {selectedIds.length > 0 && (
                <View className="mt-1 flex flex-wrap gap-2">
                  {selectedIds.map(projectId => {
                    const project = projectList.find(p => p.projectId === projectId);
                    return (
                      <View
                        key={projectId}
                        className="bg-[#FFF7E7] px-3 py-1 rounded-full flex items-center"
                      >
                        <Text className="text-[28rpx] text-[#FFB300]">{project?.projectName}</Text>
                        <Text
                          className="iconfont icon-close text-[#FFB300] text-[28rpx] ml-1"
                          onClick={() => setSelectedIds(selectedIds.filter(i => i !== projectId))}
                        />
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
            <ProjectMultiSelectModal
              visible={modalVisible}
              projectList={projectList.filter(p => typeof p.projectId === 'number') as { projectId: number; projectName: string }[]}
              selectedIds={selectedIds}
              onClose={() => setModalVisible(false)}
              onConfirm={ids => {
                setSelectedIds(ids);
                setModalVisible(false);
              }}
            />
            {/* 卡有效期 */}
            <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
              <Text className="text-[28rpx] text-[#222] w-[180rpx]">卡有效期</Text>
              <View className="flex gap-2 ml-2">
                {Object.entries(VALIDITY_TYPE).map(([key, value]) => (
                  <View
                    key={key}
                    style={{
                      minWidth: 64,
                      height: 28,
                      borderRadius: 14,
                      background: validityType === key ? '#FFF7E7' : '#F5F5F5',
                      border: `1px solid ${validityType === key ? '#FFB300' : 'transparent'}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0 16px',
                      cursor: 'pointer'
                    }}
                    onClick={() => setValidityType(key as keyof typeof VALIDITY_TYPE)}
                  >
                    <Text style={{
                      color: validityType === key ? '#FFB300' : '#999',
                      fontSize: '28rpx',
                      fontWeight: validityType === key ? 600 : 400
                    }}>{value}</Text>
                  </View>
                ))}
              </View>
            </View>
            {/* 截止日期 */}
            {validityType === 'END_DATE' && (
              <Form.Field name="endDate" rules={[{ required: true, message: '请选择截止日期' }]}> 
                {(control) => (
                  <View className="flex items-center h-[100rpx] border-b border-0 border-[#F0F0F0] border-solid">
                    <Text className="text-[28rpx] text-[#222] w-[180rpx]">截止日期</Text>
                    <Picker
                      mode="date"
                      value={control.value}
                      onChange={e => control.onChange(e.detail.value)}
                    >
                      <View className="flex-1" style={{ minHeight: 40, display: 'flex', alignItems: 'center' }}>
                        <Text className={`text-[28rpx] ${control.value ? 'text-[#222]' : 'text-[#C0C0C0]'}`}>
                          {control.value || '请选择截止日期'}
                        </Text>
                      </View>
                    </Picker>
                    <Text className="iconfont icon-arrow-right text-[#D8D8D8] text-[32rpx] ml-2" />
                  </View>
                )}
              </Form.Field>
            )}
            {/* 卡状态 */}
            <Form.Field name="status">
              {(control) => (
                <View className="flex items-center h-[80rpx] border-b border-0 border-[#F0F0F0] border-solid">
                  <Text className="text-[28rpx] text-[#222] w-[180rpx]">卡状态</Text>
                  <Switch
                    checked={control.value === true || control.value === 1}
                    onChange={e => control.onChange(e.detail.value ? 1 : 0)}
                    color="#FFB300"
                    className="scale-75 origin-left"
                  />
                </View>
              )}
            </Form.Field>
            {/* 备注 */}
            <Form.Field name="staffRemark">
              {(control) => (
                <View className="flex flex-col py-3">
                  <Text className="text-[28rpx] text-[#222] mb-2">备注</Text>
                  <Textarea
                    style={{
                      width: '100%',
                      background: '#F8F8F8',
                      borderRadius: 12,
                      color: '#222',
                      fontSize: '28rpx',
                      height: '240rpx',
                      lineHeight: 1.6,
                      boxSizing: 'border-box',
                      resize: 'none',
                      border: 'none',
                      outline: 'none'
                    }}
                    placeholder="请输入备注，例如：告知员工注意事项等"
                    placeholderStyle="color:#C0C0C0;font-size:28rpx;"
                    maxlength={200}
                    value={control.value}
                    onInput={e => control.onChange(e.detail.value)}
                  />
                  <View className="text-right text-[24rpx] text-[#C0C0C0] mt-1">{(control.value || '').length}/200</View>
                </View>
              )}
            </Form.Field>
          </View>
          {/* 提交按钮 */}
          <View className="fixed bottom-0 left-0 right-0 p-3 bg-white z-10 flex flex-row gap-4">
            <View
              className="w-1/3 h-[72rpx] bg-[#F3F3F3] rounded-full flex items-center justify-center"
              onClick={handleDelete}
              style={{ opacity: submitting ? 0.6 : 1 }}
            >
              <Text className="text-[28rpx] text-[#333333]">删除</Text>
            </View>
            <View
              className="w-2/3 h-[72rpx] bg-[#FFCC33] rounded-full flex items-center justify-center"
              onClick={handleSubmit}
              style={{ opacity: submitting ? 0.6 : 1 }}
            >
              <Text className="text-[28rpx] text-white">{submitting ? '提交中...' : '提交'}</Text>
            </View>
          </View>
        </Form>
      </View>
    </PageWithNav>
  );
};

export default CreateMemberCardPage; 