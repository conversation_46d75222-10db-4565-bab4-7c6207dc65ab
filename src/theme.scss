@import "./custom-variables.scss";

/** 额外定义-项目引用变量*/
$background-color: #f5f5f5;

// padding 间距
$padding-8: 8px;
$padding-12: 12px;
$padding-16: 16px;

// margin 间距
$margin-6: 6px;
$margin-12: 12px;
$container-margin: 12px;
$border-color: rgba(238, 238, 238, 0.7);

%display-flex-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
%display-flex-column {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

%card {
	display: inline-block; //兼容BFC Margin 折叠被阻断
	box-sizing: border-box;
	width: 100%;
	margin-bottom: 16px;
	padding: 16px;
	background: #ffffff;
	border-radius: 8px;
	box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.04); //兼容BFC Margin 折叠被阻断//兼容BFC Margin 折叠被阻断
}

%bottom-btn {
	position: absolute;
	right: 0;
	bottom: 0px;
	left: 0;
	width: 100%;
	height: 50px;
	background: #fff;
	@extend %display-flex-row;

	:global {
		.at-button {
			width: calc(100% - 32px);
		}
	}
}
