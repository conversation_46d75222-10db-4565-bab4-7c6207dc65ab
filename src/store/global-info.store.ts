import { makeAutoObservable } from "mobx";

export class GlobalInfoStore {
	businessId: 1; // 商家id
	businessName: string = "";
  staffData: any = []; // 商家-员工列表
  shopInfo: any = {}; // 商家-店铺信息
	projectsData: any = [
		{
			name: "光子嫩肤",
			code: "1",
			price: 1000,
			label: `光子嫩肤 （1000元/次）`,
			value: "1",
		},
		{
			name: "超皮秒",
			code: "2",
			price: 5000,
			label: `超皮秒 （5000元/次）`,
			value: "2",
		},
	]; // 商家-项目列表
	templatesData: any = []; // 商家-模板列表

	constructor() {
		makeAutoObservable(this);
	}

	clear(): void { }

	setBusinessId(id: any): void {
		this.businessId = id;
	}

	setBusinessName(name: string): void {
		this.businessName = name;
	}

	setsStaffData(arr: any): void {
		this.staffData = arr;
	}

	setProjectsData(arr: any): void {
		this.projectsData = arr;
	}

	setTemplatesData(arr: any): void {
		this.templatesData = arr;
  }
  
  setShopInfo(info: any): void {
    this.shopInfo = info;
  }

  getShopInfo(): any {
    return this.shopInfo
  }
}
