<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discourse 评论提取器 - 现代化界面演示</title>
    <meta name="generator" content="Discourse">
    <meta property="discourse:topic_id" content="12345">
    
    <!-- TailwindCSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        /* 模拟 Discourse 样式 */
        .topic-post {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .username {
            font-weight: 600;
            color: #3b82f6;
        }
        
        .post-content {
            margin-top: 10px;
            line-height: 1.6;
        }
        
        body {
            background: #f9fafb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto max-w-4xl p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
            🎨 Discourse 评论提取器 - 现代化TailwindCSS界面
        </h1>
        
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">功能特点</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg">
                    <div class="text-2xl mb-2">🎨</div>
                    <h3 class="font-semibold text-gray-800">现代化设计</h3>
                    <p class="text-sm text-gray-600">使用TailwindCSS构建的精美界面</p>
                </div>
                <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg">
                    <div class="text-2xl mb-2">⚡</div>
                    <h3 class="font-semibold text-gray-800">流畅动画</h3>
                    <p class="text-sm text-gray-600">丰富的过渡效果和微交互</p>
                </div>
                <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg">
                    <div class="text-2xl mb-2">🚀</div>
                    <h3 class="font-semibold text-gray-800">智能提取</h3>
                    <p class="text-sm text-gray-600">支持多种提取模式和配置</p>
                </div>
            </div>
        </div>
        
        <!-- 模拟 Discourse 帖子结构 -->
        <div class="space-y-4">
            <div class="topic-post" data-post-id="1" data-post-number="1">
                <div class="flex items-center mb-3">
                    <span class="username">admin</span>
                    <span class="ml-2 text-gray-500 text-sm">2023-12-01 10:00</span>
                </div>
                <div class="post-content">
                    <p>欢迎使用全新的Discourse评论提取器！这个版本采用了现代化的TailwindCSS设计，带来了更美观的用户界面和更流畅的交互体验。</p>
                    <p class="mt-2">如果您有任何问题，请联系：<EMAIL></p>
                </div>
            </div>
            
            <div class="topic-post" data-post-id="2" data-post-number="2">
                <div class="flex items-center mb-3">
                    <span class="username">user1</span>
                    <span class="ml-2 text-gray-500 text-sm">2023-12-01 10:15</span>
                </div>
                <div class="post-content">
                    <p>界面看起来很棒！新的渐变效果和动画让整个体验提升了很多。我的邮箱是 <EMAIL>，期待更多更新！</p>
                </div>
            </div>
            
            <div class="topic-post" data-post-id="3" data-post-number="3">
                <div class="flex items-center mb-3">
                    <span class="username">developer</span>
                    <span class="ml-2 text-gray-500 text-sm">2023-12-01 10:30</span>
                </div>
                <div class="post-content">
                    <p>技术实现很棒！TailwindCSS的运用让界面既现代又保持了良好的性能。开发团队的联系方式：<EMAIL></p>
                    <p class="mt-2">期待看到更多功能的加入。</p>
                </div>
            </div>
        </div>
        
        <div class="mt-12 text-center">
            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-6">
                <h3 class="text-xl font-semibold mb-2">✨ 现在试试新界面！</h3>
                <p class="text-indigo-100">点击右上角的"智能提取"按钮体验全新的现代化界面</p>
                <p class="text-indigo-200 text-sm mt-2">（需要安装userscript并刷新页面）</p>
            </div>
        </div>
    </div>
    
    <!-- 模拟当前用户信息 -->
    <div class="current-user" style="display: none;">
        <span class="username">admin</span>
    </div>
    
    <!-- 加载 userscript 进行演示 -->
    <script>
        // 模拟 Discourse 全局对象
        window.Discourse = {
            currentUser: {
                id: 1,
                username: 'admin',
                name: 'Administrator'
            },
            currentTopic: {
                id: 12345,
                title: 'Discourse 评论提取器演示',
                created_by: {
                    id: 1,
                    username: 'admin'
                }
            }
        };
        
        // 提示用户安装userscript
        setTimeout(() => {
            if (!document.querySelector('#discourse-extract-btn')) {
                const notice = document.createElement('div');
                notice.className = 'fixed top-4 left-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg z-50';
                notice.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.936-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                        </svg>
                        <span>请安装userscript以查看完整的现代化界面</span>
                    </div>
                `;
                document.body.appendChild(notice);
                
                setTimeout(() => notice.remove(), 5000);
            }
        }, 1000);
    </script>
</body>
</html> 
