{"name": "member-cloud-tob", "version": "1.0.0", "private": true, "description": "member-cloud-tob", "author": "", "scripts": {"build:alipay": "taro build --type alipay", "build:h5": "taro build --type h5", "build:jd": "taro build --type jd", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "build:rn": "taro build --type rn", "build:swan": "taro build --type swan", "build:tt": "taro build --type tt", "build:weapp": "taro build --type weapp", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:weapp": "npm run build:weapp -- --watch", "postinstall": "weapp-tw patch", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@babel/runtime": "^7.21.5", "@taro-hooks/plugin-react": "^2.1.0", "@tarojs/components": "3.6.14", "@tarojs/helper": "3.6.14", "@tarojs/plugin-framework-react": "3.6.14", "@tarojs/plugin-http": "^4.0.9", "@tarojs/plugin-platform-alipay": "3.6.14", "@tarojs/plugin-platform-h5": "3.6.14", "@tarojs/plugin-platform-jd": "3.6.14", "@tarojs/plugin-platform-qq": "3.6.14", "@tarojs/plugin-platform-swan": "3.6.14", "@tarojs/plugin-platform-tt": "3.6.14", "@tarojs/plugin-platform-weapp": "3.6.14", "@tarojs/react": "3.6.14", "@tarojs/runtime": "3.6.14", "@tarojs/shared": "3.6.14", "@tarojs/taro": "3.6.14", "dayjs": "^1.11.9", "decimal.js": "^10.4.3", "js-sha256": "^0.11.0", "lodash-es": "^4.17.21", "mobx": "^6.10.2", "mobx-react": "^9.0.1", "mockjs": "^1.1.0", "moment": "^2.30.1", "pinyin-pro": "^3.26.0", "rc-field-form": "^2.7.0", "react": "^18.0.0", "react-dom": "^18.0.0", "rxjs": "^7.8.1", "taro-hooks": "^2.1.0", "taro-ui": "^3.1.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@taro-hooks/plugin-auto-import": "^2.1.0", "@tarojs/cli": "3.6.14", "@tarojs/taro-loader": "3.6.14", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.14", "@types/jest": "^29.3.1", "@types/lodash-es": "^4.17.9", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "autoprefixer": "^10.4.14", "axios": "^0.22.0", "babel-preset-taro": "3.6.14", "eslint": "^8.12.0", "eslint-config-taro": "3.6.14", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.21", "postcss-px-scale": "^1.1.1", "react-refresh": "^0.11.0", "realmore-openapi": "^1.0.1", "stylelint": "^14.4.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "weapp-tailwindcss": "^3.7.0", "webpack": "5.78.0"}, "templateInfo": {"name": "default", "typescript": true, "css": "sass"}}