{"miniprogramRoot": "dist/", "projectname": "member-cloud-tob", "description": "member-cloud-tob", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "3.3.3", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "appid": "wxef67bb6578eacf71"}